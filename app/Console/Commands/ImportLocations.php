<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportLocations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:locations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
{
    $file = public_path('locations/file4.csv');

    if (!file_exists($file)) {
        $this->error('File not found: ' . $file);
        return;
    }

    $handle = fopen($file, 'r');
    $header = fgetcsv($handle); // Skip header row

    while (($data = fgetcsv($handle)) !== false) {
        [$stateName, $districtName, $areaName, $pincode] = array_map('trim', $data);

        // Get or create state
        $state = \App\Models\State::firstOrCreate(
            ['name' => $stateName],
            ['status' => 1]
        );

        // Get or create district
        $district = \App\Models\District::firstOrCreate(
            [
                'name' => $districtName,
                'state_id' => $state->id
            ],
            ['status' => 1]
        );

        // Get or create area
        \App\Models\Area::firstOrCreate(
            [
                'name' => $areaName,
                'pincode' => $pincode,
                'state_id' => $state->id,
                'district_id' => $district->id,
            ],
            ['status' => 1]
        );
    }

    fclose($handle);
    $this->info('✅ Locations imported successfully from file.csv');
}

}
