<?php

namespace App\Http\Controllers\Frontend;

use App\Models\Ad;
use App\Models\Area;
use App\Models\District;
use App\Models\User;
use App\Models\HomeService;
use App\Models\Builder;
use App\Models\Job;
use App\Models\Property;
use App\Models\BuildingType;
use App\Models\PropertyImage;
use App\Models\HomeServiceImage;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\HomeServiceCategory;
use App\Models\SupplierCategory;
use App\Models\Supplier;
use App\Models\GeneralEnquire;

use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Mail\HomeServiceRegistrationMail;
use App\Models\Banner;
use App\Models\SupplierImage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class FrontendController extends Controller
{
    public function index()
    {
        $data=[];
        // Get Home Service categories with "Other" always at the end
        $homeServiceCategories = HomeServiceCategory::withCount('homeservices')
            ->where('status', 1)
            ->orderByRaw("CASE WHEN LOWER(name) = 'other' THEN 1 ELSE 0 END, LOWER(name)")
            ->get();

        // Separate "Other" category and get first 23 regular categories
        $otherHomeService = $homeServiceCategories->where('name', 'Other')->first();
        $regularHomeServices = $homeServiceCategories->where('name', '!=', 'Other')->take(23);

        // Combine regular categories with "Other" at the end
        $data['categories'] = $regularHomeServices;
        if ($otherHomeService) {
            $data['categories'] = $data['categories']->push($otherHomeService);
        }

        // Get Supplier categories with "Other" always at the end
        $supplierCategories = SupplierCategory::withCount('suppliers')
            ->where('status', 1)
            ->orderByRaw("CASE WHEN LOWER(name) = 'other' THEN 1 ELSE 0 END, LOWER(name)")
            ->get();

        // Separate "Other" category and get first 23 regular categories
        $otherSupplier = $supplierCategories->where('name', 'Other')->first();
        $regularSuppliers = $supplierCategories->where('name', '!=', 'Other')->take(23);

        // Combine regular categories with "Other" at the end
        $data['supplier_categories'] = $regularSuppliers;
        if ($otherSupplier) {
            $data['supplier_categories'] = $data['supplier_categories']->push($otherSupplier);
        }
        $data['builders'] = Builder::withCount('propeties')->limit(8)->orderBy('name')->get();
        $data['properties'] = Property::where('status', 1)->limit(6)->get();
        $data['jobs'] = Job::where('status', 1)->latest()->take(6)->get();
        $data['banners'] = Banner::where('status', 1)->get();
        $data['ads'] = Ad::whereIn('id', [4,5,6,7])->get()->keyBy('id');
        return view('frontend.index', compact('data'));
    }
    public function aboutus()
    {
        return view('frontend.aboutus');
    }
    public function maintermsandconditions()
    {
        return view('frontend.maintermsandconditions');
    }
    public function privacypolicy()
    {
        return view('frontend.privacypolicy');
    }
    public function tchomeservice()
    {
        return view('frontend.tchomeservice');
    }
    public function tcsupplierreg()
    {
        return view('frontend.tcsupplierreg');
    }
    public function tcpostprop()
    {
        return view('frontend.tcpostprop');
    }
    public function tcpostjob()
    {
        return view('frontend.tcpostjob');
    }

    public function contact()
    {
        return view('frontend.contact');
    }
    public function advertise()
    {
        return view('frontend.advertise');
    }
    public function general_enquiry_form(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'mobile' => 'required|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);
        GeneralEnquire::create($validatedData);
        return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }
    public function homeservices(Request $request, $category)
    {
        $category = HomeServiceCategory::where('slug', $category)->first();
        $homeservices = HomeService::where('status', 1)->where('category_id', $category->id);

        $stateName = null;
        $districtName = null;
        $areaName = null;

        if ($request->has('searchkey') && $request->searchkey != '') {
            $homeservices->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                    ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        // State filter
        if ($request->has('searchState') && $request->searchState != '') {
            $homeservices->where('state_id', $request->searchState);
            // Get State name for display
            $state = \App\Models\State::find($request->searchState);
            if ($state) {
                $stateName = $state->name;
            }
        }

        // District filter
        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $homeservices->where('district_id', $request->searchDistrict);
            // Get District name for display
            $district = \App\Models\District::find($request->searchDistrict);
            if ($district) {
                $districtName = $district->name . ', ' . $district->state->name;
            }
        }

        // Area filter
        if ($request->has('searchArea') && $request->searchArea != '') {
            $homeservices->where('area_id', $request->searchArea);
            // Get Area name for later use
            $area = Area::find($request->searchArea);
            if ($area) {
                $areaName = $area->name . ', ' . $area->district->name . ', ' . $area->state->name;
            }
        }
        if ($request->has('verified') && $request->verified == 'true') {
            $homeservices->where('verified', 1);
        }

        $homeservices = $homeservices->with('homeserviceservices')->paginate(10)->withQueryString();
        $ads = Ad::whereIn('id', [8])->where('status', 1)->get()->keyBy('id');
        return view('frontend.homeservices', compact('homeservices', 'category', 'stateName', 'districtName', 'areaName', 'ads'));
    }
    public function suppliers(Request $request, $category)
    {
        $category = SupplierCategory::where('slug', $category)->first();
        $suppliers = Supplier::where('status', 1)->where('category_id', $category->id);

        $stateName = null;
        $districtName = null;
        $areaName = null;

        if ($request->has('searchkey') && $request->searchkey != '') {
            $suppliers->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                    ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        // State filter
        if ($request->has('searchState') && $request->searchState != '') {
            $suppliers->where('state_id', $request->searchState);
            // Get State name for display
            $state = \App\Models\State::find($request->searchState);
            if ($state) {
                $stateName = $state->name;
            }
        }

        // District filter
        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $suppliers->where('district_id', $request->searchDistrict);
            // Get District name for display
            $district = \App\Models\District::find($request->searchDistrict);
            if ($district) {
                $districtName = $district->name . ', ' . $district->state->name;
            }
        }

        // Area filter
        if ($request->has('searchArea') && $request->searchArea != '') {
            $suppliers->where('area_id', $request->searchArea);
            // Get Area name for later use
            $area = Area::find($request->searchArea);
            if ($area) {
                $areaName = $area->name . ', ' . $area->district->name . ', ' . $area->state->name;
            }
        }

        // Verified filter
        if ($request->has('verified') && $request->verified == 'true') {
            $suppliers->where('verified', 1);
        }

        $suppliers = $suppliers->paginate(10)->withQueryString();
        $ads = Ad::whereIn('id', [8])->where('status', 1)->get()->keyBy('id');
        return view('frontend.suppliers', compact('suppliers', 'category', 'stateName', 'districtName', 'areaName', 'ads'));
    }
    public function properties(Request $request)
    {
        $properties = Property::with(['areaType'])->where('status', 1);
        $buildingtypes = BuildingType::orderBy('type')->get();

        // $buildingtypes = BuildingType::where('id', $properties->building_type_id)->get();
        $stateName = null;
        $districtName = null;
        $areaName = null;

        if ($request->has('searchkey') && $request->searchkey != '') {
            $properties->where(function($query) use ($request) {
                $query->where('title', 'like', "%{$request->searchkey}%")
                      ->orWhere('contact_mobile', 'like', "%{$request->searchkey}%");
            });
        }

        if ($request->has('searchprotype') && $request->searchprotype != '') {
            $properties->where('property_type', $request->searchprotype);
        }

        if ($request->has('searchbldtype') && $request->searchbldtype != '') {
            $properties->where('building_type_id', $request->searchbldtype);
        }

        if ($request->has('searchbuilder') && $request->searchbuilder != '') {
            $properties->where('builder_id', $request->searchbuilder);
        }

        if ($request->has('searchbedrooms') && $request->searchbedrooms != '') {
            $properties->where('no_of_bed_rooms', $request->searchbedrooms);
        }

        // State filter
        if ($request->has('searchState') && $request->searchState != '') {
            // Get properties in areas that belong to the selected state
            $properties->whereHas('area', function($query) use ($request) {
                $query->where('state_id', $request->searchState);
            });

            // Get State name for display
            $state = \App\Models\State::find($request->searchState);
            if ($state) {
                $stateName = $state->name;
            }
        }

        // District filter
        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            // Get properties in areas that belong to the selected district
            $properties->whereHas('area', function($query) use ($request) {
                $query->where('district_id', $request->searchDistrict);
            });

            // Get District name for display
            $district = \App\Models\District::find($request->searchDistrict);
            if ($district) {
                $districtName = $district->name . ', ' . $district->state->name;
            }
        }

        // Area filter
        if ($request->has('searchArea') && $request->searchArea != '') {
            $properties->where('area_id', $request->searchArea);

            // Get Area name for display
            $area = \App\Models\Area::find($request->searchArea);
            if ($area) {
                $areaName = $area->name . ', ' . $area->district->name . ', ' . $area->state->name;
            }
        }

        // Verified filter
        if ($request->has('verified') && $request->verified == 'true') {
            $properties->where('verified', 1);
        }

        $properties = $properties->paginate(10)->withQueryString();
        $builders = \App\Models\Builder::where('status', 1)->orderBy('name')->get();
        $ads = \App\Models\Ad::whereIn('id', [13])->where('status', 1)->get()->keyBy('id');
        return view('frontend.properties', compact('properties', 'stateName', 'districtName', 'areaName', 'builders', 'ads','buildingtypes'));
    }

    public function find_homeservices()
    {
        $categories=HomeServiceCategory::where('status',1)
            ->orderByRaw("CASE WHEN LOWER(name) = 'other' THEN 1 ELSE 0 END, LOWER(name)")
            ->get();
        return view('frontend.find_homeservices', compact('categories'));
    }
    public function find_suppliers()
    {
        $categories=SupplierCategory::where('status',1)
            ->orderByRaw("CASE WHEN LOWER(name) = 'other' THEN 1 ELSE 0 END, LOWER(name)")
            ->get();
        return view('frontend.find_suppliers', compact('categories'));
    }
    public function homeservice_details($slug)
    {
        $homeservice=HomeService::where('slug',$slug)->first();
        $ads=Ad::whereIn('id', [16,15])->where('status', 1)->get()->keyBy('id');
        return view('frontend.homeservice-details', compact('homeservice','ads'));
    }
    public function supplier_details($slug)
    {
        $supplier=Supplier::where('slug',$slug)->first();
        $ads=Ad::whereIn('id', [10,17])->where('status', 1)->get()->keyBy('id');
        return view('frontend.supplier-details', compact('supplier','ads'));
    }
    public function property_details($slug)
    {
        $property=Property::with(['areaType'])->where('slug',$slug)->first();
        $buildingtypes = BuildingType::where('id', $property->building_type_id)->first();
        $ads=Ad::whereIn('id', [11,18])->where('status', 1)->get()->keyBy('id');
        return view('frontend.property-details', compact('property','ads','buildingtypes'));
    }

    public function register()
    {
        return view('frontend.register');
    }
public function usersstore(Request $request)
    {
      //  dd($request->all());
        //  dd('hi');
         $request->validate([
            'name' => 'required',
            'email' => [
                'required',
                'email',
                'unique:users,email',  // Ensure email is unique in both tables
            ],
            'mobile' => 'nullable|digits_between:10,15',
            // 'password' => 'required',
        ]);
       // dd('1');
        // $request->password
        // $pswd=$request->password;
          $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'mobile' => $request->mobile,

               'password' => Hash::make(123456),
            // 'password' => Hash::make($randomPassword), // Default password
            'role_id' => '5',
        ]);
      //  dd($user);
        return view('frontend.register');
    }
    public function login()
    {
        return view('frontend.login');
    }
    public function homeservice_registration()
    {
        $categories=HomeServiceCategory::where('status',1)->get();
        return view('frontend.homeservice_registration',compact('categories'));
    }
    public function storeHomeService(Request $request)
    {
        $request->validate([
            'business_title' => 'required|string|max:255|unique:home_services,business_title',
            'category_id' => 'required|exists:home_service_categories,id',
            'phone' => 'required',
            'email' => 'required|email',
            'state_id' => 'required',
            'district_id' => 'required',
            'area_id' => 'required',
            'address' => 'required',
            'nearest_landmark' => 'nullable|string|max:255',
            'distance' => 'nullable|string|max:100',
            'description' => 'required',
            'services' => 'required|array|min:1',
            'services.*' => 'required|string|max:255',
            'images.*' => 'image|mimes:jpg,jpeg,png|max:2048',
        ]);

        // Create HomeService
        $homeservice = HomeService::create([
            'category_id' => $request->category_id,
            'business_title' => $request->business_title,
            'slug' => Str::slug($request->business_title),
            'phone' => $request->phone,
            'email' => $request->email,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'area_id' => $request->area_id,
            'address' => $request->address,
            'nearest_landmark' => $request->nearest_landmark,
            'distance' => $request->distance,
            'description' => $request->description,
            'website' => $request->website,
            'established_year' => $request->established_year,
            'facebook' => $request->facebook,
            'instagram' => $request->instagram,
            'twitter' => $request->twitter,
            'linked_in' => $request->linked_in,
            'youtube' => $request->youtube,
            'status' => 1,
            'logo' => 1,
            'user_id' => 1, // Later you can use auth()->id() when login is implemented
        ]);

        // Handle Gallery Uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('homeservice_gallery', 'public');

                HomeServiceImage::create([
                    'homeservice_id' => $homeservice->id,
                    'image_path' => $path,
                ]);
            }
        }

        // Store Services
        if ($request->has('services')) {
            foreach ($request->services as $service) {
                if (!empty($service)) {
                    DB::table('home_service_services')->insert([
                        'homeservice_id' => $homeservice->id,
                        'service_name' => $service,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Send confirmation email
       // $this->sendRegistrationEmail($homeservice);

        return redirect()->back()->with('success', 'Your Home Service registration has been submitted successfully! 🎉 Our team will review your details and approve your registration within 24-48 hours. You will be notified once approved.');
    }
 public function supplier_registration()
    {
        $categories=SupplierCategory::where('status',1)->get();
        return view('frontend.supplier_registration',compact('categories'));
    }
    public function storeSupplier(Request $request)
    {
        $request->validate([
            'business_title' => 'required|string|max:255|unique:home_services,business_title',
            'category_id' => 'required|exists:home_service_categories,id',
            'phone' => 'required',
            'email' => 'required|email',
            'state_id' => 'required',
            'district_id' => 'required',
            'area_id' => 'required',
            'address' => 'required',
            'nearest_landmark' => 'nullable|string|max:255',
            'distance' => 'nullable|string|max:100',
            'description' => 'required',
            'services' => 'required|array|min:1',
            'services.*' => 'required|string|max:255',
            'images.*' => 'image|mimes:jpg,jpeg,png|max:2048',
        ]);

        // Create HomeService
        $homeservice = Supplier::create([
            'category_id' => $request->category_id,
            'business_title' => $request->business_title,
            'slug' => Str::slug($request->business_title),
            'phone' => $request->phone,
            'email' => $request->email,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'area_id' => $request->area_id,
            'address' => $request->address,
            'nearest_landmark' => $request->nearest_landmark,
            'distance' => $request->distance,
            'description' => $request->description,
            'website' => $request->website,
            'established_year' => $request->established_year,
            'facebook' => $request->facebook,
            'instagram' => $request->instagram,
            'twitter' => $request->twitter,
            'linked_in' => $request->linked_in,
            'youtube' => $request->youtube,
            'status' => 1,
            'logo' => 1,
            'user_id' => 1, // Later you can use auth()->id() when login is implemented
        ]);

        // Handle Gallery Uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('supplier_gallery', 'public');

                SupplierImage::create([
                    'supplier_id' => $homeservice->id,
                    'image_path' => $path,
                ]);
            }
        }

        // Store Services
        if ($request->has('services')) {
            foreach ($request->services as $service) {
                if (!empty($service)) {
                    DB::table('supplier_services')->insert([
                        'supplier_id' => $homeservice->id,
                        'service_name' => $service,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }

        // Send confirmation email
       // $this->sendRegistrationEmail($homeservice);

        return redirect()->back()->with('success', 'Your Supplier registration has been submitted successfully! 🎉 Our team will review your details and approve your registration within 24-48 hours. You will be notified once approved.');
    }
    private function sendRegistrationEmail($homeservice)
    {
        // You can use Laravel's Mail facade to send emails
        // This is a placeholder - implement actual email sending logic
        // try {
        //     Mail::to($homeservice->email)->send(new HomeServiceRegistrationMail($homeservice));
        // } catch (\Exception $e) {
        //     // Log the error but don't stop the process
        //     \Log::error('Failed to send registration email: ' . $e->getMessage());
        // }
    }
     public function post_property()
    {
      $builders=Builder::where('status',1)->get();
      $areaTypes = \App\Models\AreaType::where('status', 1)->orderBy('name')->get();
      $states = \App\Models\State::where('status', 1)->orderBy('name')->get();
        return view('frontend.post_property',compact('builders', 'areaTypes', 'states'));
    }
    public function storeProperty(Request $request)
    {
        //dd('hi');
        // Define base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'property_type' => 'required',
            'building_type_id' => 'required',
            'area_size' => 'required',
            'area_type_id' => 'required|exists:area_types,id',
            'address' => 'required',
            'nearest_landmark' => 'nullable|string|max:255',
            'distance' => 'nullable|string|max:100',
            'state_id' => 'required',
            'district_id' => 'required',
            'area_id' => 'required',
            'contact_person' => 'required',
            'contact_mobile' => 'required',
            'images.*' => 'image|mimes:jpg,jpeg,png|max:2048',
            'builder_type' => 'required|in:individual,builder',
            'builder_id' => 'required_if:builder_type,builder|nullable|exists:builders,id',
        ];

        // Add conditional validation rules based on building type
        // Agriculture Land (1) and Open Land (7) don't require these fields
        if (!in_array($request->building_type_id, ['1', '7'])) {
            $rules['no_of_bed_rooms'] = 'required';
            $rules['no_of_bath_rooms'] = 'required';
            $rules['no_of_balconies'] = 'required';
            $rules['no_of_car_parkings'] = 'required';
            $rules['no_of_floors'] = 'required';
            $rules['facing'] = 'required';
            $rules['build_year'] = 'required';
        }

        // Add conditional validation based on property type
        if ($request->property_type == '1') { // Sell
            $rules['property_cost'] = 'required|numeric|min:1';
        } elseif ($request->property_type == '2') { // Rent
            $rules['advance_amount'] = 'required|numeric|min:1';
            $rules['rent'] = 'required|numeric|min:1';
        }

        $request->validate($rules);

        // Prepare property data
        $propertyData = [
            'title' => $request->title,
            'slug' => Str::slug($request->title),
            'builder_id' => $request->builder_type === 'builder' ? $request->builder_id : null,
            'property_type' => $request->property_type,
            'building_type_id' => $request->building_type_id,
            'area_size' => $request->area_size,
            'area_type_id' => $request->area_type_id,
        ];

        // Add conditional fields only if not Agriculture Land (1) or Open Land (7)
        if (!in_array($request->building_type_id, ['1', '7'])) {
            $propertyData['no_of_bed_rooms'] = $request->no_of_bed_rooms;
            $propertyData['no_of_bath_rooms'] = $request->no_of_bath_rooms;
            $propertyData['no_of_balconies'] = $request->no_of_balconies;
            $propertyData['no_of_car_parkings'] = $request->no_of_car_parkings;
            $propertyData['no_of_floors'] = $request->no_of_floors;
            $propertyData['facing'] = $request->facing;
            $propertyData['build_year'] = $request->build_year;
        } else {
            // Set default values for Agriculture Land and Open Land
            $propertyData['no_of_bed_rooms'] = null;
            $propertyData['no_of_bath_rooms'] = null;
            $propertyData['no_of_balconies'] = null;
            $propertyData['no_of_car_parkings'] = null;
            $propertyData['no_of_floors'] = null;
            $propertyData['facing'] = null;
            $propertyData['build_year'] = null;
        }

        // Continue with other fields
        $propertyData['address'] = $request->address;
        $propertyData['nearest_landmark'] = $request->nearest_landmark;
        $propertyData['distance'] = $request->distance;
        $propertyData['state_id'] = $request->state_id;
        $propertyData['district_id'] = $request->district_id;
        $propertyData['area_id'] = $request->area_id;
        $propertyData['contact_person'] = $request->contact_person;
        $propertyData['contact_mobile'] = $request->contact_mobile;
        $propertyData['contact_email'] = $request->contact_email;
        $propertyData['amenities_parking'] = $request->amenities_parking;
        $propertyData['amenities_swimming_pool'] = $request->amenities_swimming_pool;
        $propertyData['amenities_lift'] = $request->amenities_lift;
        $propertyData['amenities_gym'] = $request->amenities_gym;
        $propertyData['amenities_spa'] = $request->amenities_spa;
        $propertyData['other_amenities'] = $request->other_amenities;
        $propertyData['status'] = 1;

        // Add conditional cost fields based on property type
        if ($request->property_type == '1') { // Sell
            $propertyData['property_cost'] = $request->property_cost;
            $propertyData['advance_amount'] = null;
            $propertyData['rent'] = null;
        } elseif ($request->property_type == '2') { // Rent
            $propertyData['advance_amount'] = $request->advance_amount;
            $propertyData['rent'] = $request->rent;
            $propertyData['property_cost'] = null;
        }

        // Create Property
        $property = Property::create($propertyData);
//  dd($property);
        // Handle Gallery Uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('property_gallery', 'public');

                PropertyImage::create([
                    'property_id' => $property->id,
                    'image_path' => $path,
                ]);
            }
        }

        return redirect()->back()->with('success', 'Your Property has been submitted successfully! 🏠 Our team will review your listing and approve it within 24-48 hours. Once approved, it will be visible to potential buyers/renters.');
    }

     public function post_job()
    {
      $jobs=Job::where('status',1)->get();
      $ads=Ad::whereIn('id', [9])->where('status', 1)->get()->keyBy('id');
        return view('frontend.post_job',compact('jobs','ads'));
    }


    public function storeJob(Request $request)
    {
        //dd('hi');

        $request->validate([
            'job_title' => 'required|string|max:255',
            'job_role' => 'required',
            'experience' => 'required',
            'salary' => 'required',
            'job_description' => 'required',
            'company_name' => 'required',
            'about_company' => 'required',
            'qualification' => 'required',
            'email' => 'required',
            'mobile' => 'required',
            'no_of_positions' => 'required',
            'state_id' => 'required',
            'district_id' => 'required',
            'area_id' => 'required',
            'address' => 'required|string|max:255',
            'nearest_landmark' => 'nullable|string|max:255',
            'distance' => 'nullable|string|max:100',

        ]);

        // Create Property
        $job = Job::create([
            'job_title' => $request->job_title,
            'job_role' => $request->job_role,
            'experience' => $request->experience,
            'salary' => $request->salary,
            'job_description' => $request->job_description,
            'company_name' => $request->company_name,
            'about_company' => $request->about_company,
            'qualification' => $request->qualification,
            'email' => $request->email,
            'mobile' => $request->mobile,
            'no_of_positions' => $request->no_of_positions,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'area_id' => $request->area_id,
            'address' => $request->address,
            'nearest_landmark' => $request->nearest_landmark,
            'distance' => $request->distance,
            'status' => 1,  // Later you can use auth()->id() when login is implemented
        ]);
//  dd($property);


        return redirect()->back()->with('success', 'Your Job posting has been submitted successfully! 💼 Our team will review your job details and approve it within 24-48 hours. Once approved, it will be visible to job seekers.');
    }

    public function jobs(Request $request)
    {
        $jobs = Job::where('status', 1);

        $stateName = null;
        $districtName = null;
        $areaName = null;

        if ($request->has('searchkey') && $request->searchkey != '') {
            $jobs->where(function($query) use ($request) {
                $query->where('job_title', 'like', "%{$request->searchkey}%")
                      ->orWhere('company_name', 'like', "%{$request->searchkey}%")
                      ->orWhere('job_role', 'like', "%{$request->searchkey}%")
                      ->orWhere('email', 'like', "%{$request->searchkey}%")
                      ->orWhere('mobile', 'like', "%{$request->searchkey}%");
            });
        }

        // State filter
        if ($request->has('searchState') && $request->searchState != '') {
            $jobs->where('state_id', $request->searchState);
            // Get State name for display
            $state = \App\Models\State::find($request->searchState);
            if ($state) {
                $stateName = $state->name;
            }
        }

        // District filter
        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $jobs->where('district_id', $request->searchDistrict);
            // Get District name for display
            $district = \App\Models\District::find($request->searchDistrict);
            if ($district) {
                $districtName = $district->name . ', ' . $district->state->name;
            }
        }

        // Area filter
        if ($request->has('searchArea') && $request->searchArea != '') {
            $jobs->where('area_id', $request->searchArea);
            // Get Area name for display
            $area = \App\Models\Area::find($request->searchArea);
            if ($area) {
                $areaName = $area->name . ', ' . $area->district->name . ', ' . $area->state->name;
            }
        }

        // Verified filter
        if ($request->has('verified') && $request->verified == 'true') {
            $jobs->where('verified', 1);
        }

        $jobs = $jobs->paginate(10)->withQueryString();
        $ads = Ad::whereIn('id', [9])->where('status', 1)->get()->keyBy('id');
        return view('frontend.jobs', compact('jobs', 'stateName', 'districtName', 'areaName', 'ads'));
    }

    public function job_details($id)
    {
        $job=Job::where('id',$id)->first();
        $ads=Ad::whereIn('id', [12,19])->where('status', 1)->get()->keyBy('id');
        return view('frontend.job-details', compact('job','ads'));
    }
    public function builders()
    {
        $builders=Builder::where('status',1)->withCount('propeties')->orderBy('propeties_count','desc')->get();
        return view('frontend.builders',compact('builders'));
    }
}
