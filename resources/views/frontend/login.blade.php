@extends('frontend.layouts.master')
@section('content')

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Login</h2>
				<span class="ipn-subtitle">Login in to your account</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">

	<div class="container">

		<div class="row">
			<div class="col-lg-12 col-md-12">
				<div class="filter_search_opt">
					<a href="javascript:void(0);" class="btn btn-dark full-width mb-4" onclick="openFilterSearch()">
						<span class="svg-icon text-light svg-icon-2hx me-2">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z" fill="currentColor"/>
							</svg>
						</span>Open Filter Option
					</a>
				</div>
			</div>
		</div>

		<div class="row">

			<!-- property-sidebar code  -->
			<!-- property Sidebar -->


			<div class="col-lg-12 col-md-12 list-layout">



				<div class="row justify-content-center">

                    <!-- Single blog Grid -->
                    <div class="col-xl-7 col-lg-8 col-md-9">
                        <div class="card border-0 rounded-4 p-xl-4 p-lg-4 p-md-4 p-3">

                            <div class="simple-form">
                                <div class="form-header text-center mb-5">
                                    {{-- <div class="effco-logo mb-2">
                                        <a class="d-flex align-items-center justify-content-center" href="index.php">
                                            <span class="svg-icon text-primary svg-icon-2hx">
                                                <svg width="90" height="90" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M15.8797 15.375C15.9797 15.075 15.9797 14.775 15.9797 14.475C15.9797 13.775 15.7797 13.075 15.4797 12.475C14.7797 11.275 13.4797 10.475 11.9797 10.475C11.7797 10.475 11.5797 10.475 11.3797 10.575C7.37971 11.075 4.67971 14.575 2.57971 18.075L10.8797 3.675C11.3797 2.775 12.5797 2.775 13.0797 3.675C13.1797 3.875 13.2797 3.975 13.3797 4.175C15.2797 7.575 16.9797 11.675 15.8797 15.375Z" fill="currentColor"/>
                                                    <path opacity="0.3" d="M20.6797 20.6749C16.7797 20.6749 12.3797 20.275 9.57972 17.575C10.2797 18.075 11.0797 18.375 11.9797 18.375C13.4797 18.375 14.7797 17.5749 15.4797 16.2749C15.6797 15.9749 15.7797 15.675 15.7797 15.375V15.2749C16.8797 11.5749 15.2797 7.47495 13.2797 4.07495L21.6797 18.6749C22.2797 19.5749 21.6797 20.6749 20.6797 20.6749ZM8.67972 18.6749C8.17972 17.8749 7.97972 16.975 7.77972 15.975C7.37972 13.575 8.67972 10.775 11.3797 10.375C7.37972 10.875 4.67972 14.375 2.57972 17.875C2.47972 18.075 2.27972 18.375 2.17972 18.575C1.67972 19.475 2.27972 20.475 3.27972 20.475H10.3797C9.67972 20.175 9.07972 19.3749 8.67972 18.6749Z" fill="currentColor"/>
                                                </svg>
                                            </span>
                                        </a>
                                    </div> --}}
                                    <h4 class="fs-2">Login </h4>
                                </div>
                                <div class="login-form">
                                    <form>

                                        <div class="form-floating mb-3">
                                            <input type="email" class="form-control" placeholder="<EMAIL>">
                                            <label>Email address</label>
                                        </div>

                                        <div class="form-floating mb-3">
                                            <input type="password" class="form-control" placeholder="Password">
                                            <label>Password</label>
                                        </div>

                                        <div class="form-group mb-3">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="flex-shrink-0 flex-first">
                                                    {{-- <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="checkbox" id="save-pass" value="option1">
                                                        <label class="form-check-label" for="save-pass">Save Password</label>
                                                    </div> --}}
                                                </div>
                                                <div class="flex-shrink-0 flex-first">
                                                    <a href="#" class="link fw-medium">Forgot Password?</a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <button type="button" class="btn btn-lg btn-primary fw-medium full-width rounded-2">LogIn</button>
                                        </div>

                                    </form>
                                </div>
                                <div class="text-center">
                                    <p class="mt-4">Have't Any Account? <a href="/register" class="link fw-medium">Create An Account</a></p>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <!-- /row -->

				<!-- Pagination -->


			</div>

		</div>
	</div>
</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="#" class="btn btn-call-to-act">SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->
@stop
