<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Area;
use App\Models\City;
use App\Models\District;
use App\Models\State;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    public function fetchData(Request $request)
    {
        $dataFrom = $request->input('datafrom');
        $searchTerm = $request->input('q', '');

        switch ($dataFrom) {
            case 'states':
                return $this->getStates($searchTerm);

            // case 'cities':
            //     $stateId = $request->input('state_id');
            //     return $this->getCities($stateId, $searchTerm);

            case 'districts':
                $stateId = $request->input('state_id');
                return $this->getDistricts($stateId, $searchTerm);

            case 'areas':
                $cityId = $request->input('district_id');
                return $this->getAreas($cityId, $searchTerm);

            case 'areas_with_pincode':
                $districtId = $request->input('district_id');
                return $this->getAreasWithPincode($districtId, $searchTerm);

            default:
                return response()->json([]);
        }
    }

    private function getStates($searchTerm)
    {
        $states = State::where('status', 1)
            ->when($searchTerm, function($query) use ($searchTerm) {
                return $query->where('name', 'like', "%{$searchTerm}%");
            })
            ->orderByRaw('LOWER(name) ASC')
            ->get()
            ->map(function($state) {
                return [
                    'id' => $state->id,
                    'text' => $state->name
                ];
            });

        return $states;
    }

    // private function getCities($stateId, $searchTerm)
    // {
    //     if (!$stateId) {
    //         return [];
    //     }

    //     $cities = City::where('status', 1)
    //         ->where('state_id', $stateId)
    //         ->when($searchTerm, function($query) use ($searchTerm) {
    //             return $query->where('name', 'like', "%{$searchTerm}%");
    //         })
    //         ->orderBy('name')
    //         ->get()
    //         ->map(function($city) {
    //             return [
    //                 'id' => $city->id,
    //                 'text' => $city->name.', '.$city->state->name
    //             ];
    //         });

    //     return $cities;
    // }

    private function getAreas($cityId, $searchTerm)
    {
        if (!$cityId) {
            return [];
        }

        $areas = Area::where('status', 1)
            ->where('district_id', $cityId)
            ->when($searchTerm, function($query) use ($searchTerm) {
                return $query->where('name', 'like', "%{$searchTerm}%");
            })
            ->orderByRaw('LOWER(name) ASC')
            ->get()
            ->map(function($area) {
                return [
                    'id' => $area->id,
                    'text' => $area->name.', '.$area->city->name.', '.$area->state->name
                ];
            });

        return $areas;
    }

    private function getDistricts($stateId, $searchTerm)
    {
        if (!$stateId) {
            return [];
        }

        $districts = District::where('status', 1)
            ->where('state_id', $stateId)
            ->when($searchTerm, function($query) use ($searchTerm) {
                return $query->where('name', 'like', "%{$searchTerm}%");
            })
            ->orderByRaw('LOWER(name) ASC')
            ->get()
            ->map(function($district) {
                return [
                    'id' => $district->id,
                    'text' => $district->name.', '.$district->state->name
                ];
            });

        return $districts;
    }

    private function getAreasWithPincode($districtId, $searchTerm)
    {
        if (!$districtId) {
            return [];
        }

        $areas = Area::where('status', 1)
            ->where('district_id', $districtId)
            ->when($searchTerm, function($query) use ($searchTerm) {
                return $query->where('name', 'like', "%{$searchTerm}%")
                           ->orWhere('pincode', 'like', "%{$searchTerm}%");
            })
            ->orderByRaw('LOWER(name) ASC')
            ->get()
            ->map(function($area) {
                $text = $area->name;
                if ($area->pincode) {
                    $text .= ' - ' . $area->pincode;
                }
                $text .= ', ' . $area->district->name . ', ' . $area->state->name;

                return [
                    'id' => $area->id,
                    'text' => $text
                ];
            });

        return $areas;
    }
}
