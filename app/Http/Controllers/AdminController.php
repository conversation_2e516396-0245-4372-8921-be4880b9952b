<?php

namespace App\Http\Controllers;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function index()
    {
        $roles = Role::all();
        $permissions=Permission::all();
        return view('admin.roles.index', compact('roles','permissions'));
    }
    public function rolePermissions($id)
    {
        $role = Role::findOrFail($id);
        $permissions = Permission::all();
        $rolePermissions = $role->permissions->pluck('name')->toArray(); // Get assigned permissions

        return view('admin.roles.permissions', compact('role', 'permissions', 'rolePermissions'));
    }


    public function createRole(Request $request)
    {
        $request->validate(['name' => 'required|unique:roles,name']);
        Role::create(['name' => $request->name]);
        return back()->with('success', 'Role created successfully.');
    }

    public function assignPermissions(Request $request)
    {

        $role = Role::findOrFail($request->role_id);
        $role->syncPermissions($request->permissions);

        // Reset permission cache to reflect changes immediately
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        return back()->with('success', 'Permissions updated successfully.');
    }

}
