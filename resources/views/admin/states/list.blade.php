@extends('admin.layouts.master')
@section('title', 'States')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">States List</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">States List</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search State" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">

                @can('state-add')

                <a href="/admin/states/add" class="btn btn-sm btn-primary-600 d-flex"><i class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span></a>
                @endcan
            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="statesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>State</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="clientsGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>

@stop
@section('script')
<script>
    $(document).ready(function () {

        var table = $('#statesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('states.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                }
            },
            columns: [
                { data: 'name', name: 'name' },
                { data: 'status', name: 'status', orderable: false, searchable: false },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();

            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            // buttons: [
            //     {
            //         extend: 'csv',
            //         text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
            //         className: 'btn btn-success btn-sm bg-success-500',
            //         filename: 'Clients_' + new Date().toISOString().slice(0, 10)
            //     }
            // ],
            buttons: [
                    {
                        extend: 'csv',
                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                        className: 'btn btn-success btn-sm bg-success-500',
                        filename: 'Clients_' + new Date().toISOString().slice(0, 10),
                        action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                            }, 500); // Small delay for better UX
                        }
                    }
                ],

            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            }
        });

        // Custom search event triggers
        $("#searchkey").on("keyup", function () {
            table.draw();
        });


    });
</script>
@stop
