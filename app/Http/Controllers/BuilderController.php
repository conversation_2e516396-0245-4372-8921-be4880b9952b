<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\District;
use App\Models\State;
use App\Models\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;

class BuilderController extends Controller
{

    //
    public function index()
    {
        $states = State::where('status', 1)->orderBy('name')->get();
        // Don't load all districts at once - use AJAX loading instead
        $districts = collect(); // Empty collection
        // Don't load all areas at once - use AJAX loading instead
        $areas = collect(); // Empty collection
        return view('admin.builders.list', compact('states', 'districts', 'areas'));
    }
    public function add()
    {
        return view('admin.builders.add');
    }
    //
    // Store method to save Builder category data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'phone'        => 'required',
            'email'        => 'required|email',
            'state_id'        => 'required',
            'district_id'        => 'required',
            'area_id'        => 'required',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);
        $data = $request->all();
        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/builders/
            $filePath = $file->storeAs('builders', $filename, 'public');

            // Save the file path in the database
            $data['image'] = $filePath;
        }

        // Save data to database
        Builder::create($data);

        // Redirect to builder list page with success message
        return redirect()->route('builders.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $builder = Builder::where('id', $id)->first();
        $states = State::where('status', 1)->get();
        $districts = District::where('status', 1)->where('state_id',$builder->state_id)->get();
        $areas = Area::where('status', 1)->where('district_id',$builder->district_id)->get();


        return view('admin.builders.edit', compact('builder','states','districts','areas'));
    }
    public function show($id)
    {
        $builder = Builder::where('id', $id)->first();


        return view('admin.builders.view', compact('builder'));
    }
    public function update(Request $request, Builder $builder)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'phone'        => 'required',
            'email'        => 'required|email',
            'state_id'        => 'required',
            'district_id'        => 'required',
            'area_id'        => 'required',
            'image'         => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);

        $data = $request->except(['image']); // Exclude logo from mass update initially

        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/builders/
            $filePath = $file->storeAs('builders', $filename, 'public');

            // Delete the old logo if exists
            if ($builder->image && Storage::disk('public')->exists($builder->image)) {
                Storage::disk('public')->delete($builder->image);
            }

            // Save the new file path
            $data['image'] = $filePath;
        }
        // Update vendorcategory data
        $builder->update($data);

        // Redirect to vendorcategory list page with success message
        return redirect()->route('builders.index')->with('success', $builder->name . ' updated successfully!');
    }

    public function getBuildersData(Request $request)
    {
        if ($request->ajax()) {
            // Get the logged-in user
            $user = Auth::user();

            // Fetch builders query with all relationships
            $builders = Builder::with(['area', 'district', 'state']);

            if ($request->has('searchname') && $request->searchname != '') {
                $builders->where('name', 'like', "%{$request->searchname}%");
            }

            if ($request->has('searchState') && $request->searchState != '') {
                $builders->where('state_id', $request->searchState);
            }

            if ($request->has('searchDistrict') && $request->searchDistrict != '') {
                $builders->where('district_id', $request->searchDistrict);
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $builders->where('area_id', $request->searchArea);
            }
        }

        return DataTables::of($builders)
            ->addColumn('status', function ($row) {
                $checked = $row->status == 1 ? 'checked' : '';
                $statusText = $row->status == 1 ? 'Active' : 'Inactive';

                return '
                    <div class="form-switch switch-success d-flex align-items-center gap-2">
                        <input class="form-check-input status-toggle" type="checkbox" role="switch"
                               data-id="' . $row->id . '" ' . $checked . '>
                        <label class="form-check-label fw-medium text-secondary-light mb-0">' . $statusText . '</label>
                    </div>';
            })
            ->addColumn('image', function ($row) {
                if ($row->image) {
                    $imageUrl = url('storage/'.$row->image); // update path if needed
                    return '<img src="' . $imageUrl . '" alt="' . $row->name . '" width="50" height="50" class="rounded">';
                } else {
                    return '<img src="' . asset('assets/images/default.png') . '" width="50" height="50" class="rounded">';
                }
            })
            ->addColumn('state', function ($row) {
                return $row->state ? $row->state->name : 'N/A';
            })
            ->addColumn('district', function ($row) {
                return $row->district ? $row->district->name : 'N/A';
            })
            ->addColumn('area', function ($row) {
                return $row->area ? $row->area->name : 'N/A';
            })

            ->addColumn('action', function ($row) use ($user) {
                $output = '';
                // Check 'vendor builder-edit' permission
                 if ($user->can('builder-view')) {
                    $output .= '
                        <a href="' . route('builders.show', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center ms-1">
                            <iconify-icon icon="lucide:eye"></iconify-icon>
                        </a>';
                }


                return $output;
            })
            ->filter(function ($query) use ($request) {

                if ($request->has('searchkey') && $request->searchkey != '') {
                    $query->where(function ($q) use ($request) {
                        $q->where('name', 'like', "%{$request->searchkey}%")
                            ;
                    });
                }
            })

            ->rawColumns(['image', 'status', 'action']) // Ensure HTML rendering
            ->make(true);
    }
    public function export(Request $request)
    {
        // Fetch builders query with all relationships
        $builders = Builder::with([
            'area',
            'district',
            'state'
        ]);

        // Apply filters
        if ($request->has('searchname') && $request->searchname != '') {
            $builders->where('name', 'like', "%{$request->searchname}%");
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $builders->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $builders->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $builders->where('area_id', $request->searchArea);
        }

        // Get all builders (no pagination)
        $builders = $builders->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Name',
            'Phone',
            'Email',
            'Area',
            'District',
            'State',
            'Status',
            'Created At'
        ]);

        // Add data rows
        foreach ($builders as $builder) {
            $csv->insertOne([
                $builder->name,
                $builder->phone,
                $builder->email,
                $builder->area->name ?? 'N/A',
                $builder->district->name ?? 'N/A',
                $builder->state->name ?? 'N/A',
                $builder->status ? 'Active' : 'Inactive',
                $builder->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'Builders_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }

    /**
     * Update the status of a builder.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'builder_id' => 'required|exists:builders,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the builder
            $builder = Builder::findOrFail($request->builder_id);

            // Update status
            $builder->status = $request->status;
            $builder->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Builder status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update builder status: ' . $e->getMessage()
            ], 500);
        }
    }
}
