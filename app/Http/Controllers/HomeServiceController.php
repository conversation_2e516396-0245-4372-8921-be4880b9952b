<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\District;
use App\Models\HomeService;
use App\Models\State;
use App\Models\HomeServiceCategory;
use App\Models\HomeServiceService;
use App\Models\HomeServiceImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;

class HomeServiceController extends Controller
{
    //
    public function index()
    {
        $categories = HomeServiceCategory::where('status', 1)->orderBy('name')->get();
        $states = State::where('status', 1)->orderBy('name')->get();
        // Don't load all districts at once - use AJAX loading instead
        $districts = collect(); // Empty collection
        // Don't load all areas at once - use AJAX loading instead
        $areas = collect(); // Empty collection
        return view('admin.homeservices.list', compact('categories', 'states', 'districts', 'areas'));
    }

    public function getHomeServicesData(Request $request)
    {
        $homeservices = HomeService::with(['homeservicecategory', 'area', 'district', 'state']);

        // Apply filters before DataTables processing
        if ($request->has('searchkey') && $request->searchkey != '') {
            $homeservices->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                  ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        if ($request->has('searchCategory') && $request->searchCategory != '') {
            $homeservices->where('category_id', $request->searchCategory);
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $homeservices->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $homeservices->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $homeservices->where('area_id', $request->searchArea);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
            $homeservices->where('verified', $request->searchVerified);
        }

        return DataTables::of($homeservices)
            ->addColumn('category', function ($homeservice) {
                return $homeservice->homeservicecategory ? $homeservice->homeservicecategory->name : 'N/A';
            })
            ->addColumn('area', function ($homeservice) {
                return $homeservice->area ? $homeservice->area->name : 'N/A';
            })
            ->addColumn('action', function ($row) {
                $output = '';

                // Add view button
                $output .= '
                    <a href="' . route('homeservices.show', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center">
                        <iconify-icon icon="lucide:eye"></iconify-icon>
                    </a>';

                // Add edit button
                $output .= '<button class="btn btn-danger btn-sm delete-homeservice" data-id="'. $row->id .'" title="Delete">
                                <iconify-icon icon="lucide:trash-2"></iconify-icon>
                            </button>';

                return $output;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function  show($id)  {
        $homeservice=HomeService::where('id',$id)->first();
        $homeservice_services=HomeServiceService::where('homeservice_id',$homeservice->id)->get();
        $homeservice_images=HomeServiceImage::where('homeservice_id',$homeservice->id)->get();
        return view('admin.homeservices.view',compact('homeservice','homeservice_services','homeservice_images'));

    }

    /**
     * Update the status of a homeservice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'homeservice_id' => 'required|exists:home_services,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the homeservice
            $homeservice = HomeService::findOrFail($request->homeservice_id);

            // Update status
            $homeservice->status = $request->status;
            $homeservice->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Home Service status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update home service status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the verification status of a homeservice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateVerificationStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'homeservice_id' => 'required|exists:home_services,id',
            'verified' => 'required|in:0,1',
        ]);

        try {
            // Find the homeservice
            $homeservice = HomeService::findOrFail($request->homeservice_id);

            // Update verification status
            $homeservice->verified = $request->verified;
            $homeservice->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Home Service verification status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update home service verification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a homeservice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        // dd('h');
        // Validate request
        $validated = $request->validate([
            'homeservice_id' => 'required|exists:home_services,id',
        ]);

        try {
            // Find the homeservice
            $homeservice = HomeService::findOrFail($request->homeservice_id);

            // Delete the homeservice
            $homeservice->delete();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Home Service deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete home service: ' . $e->getMessage()
            ], 500);
        }
    }
    /**
     * Export homeservices data to CSV based on filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        // Fetch homeservices query with all relationships
        $homeservices = HomeService::with([
            'homeservicecategory',
            'area',
            'district',
            'state'
        ]);

        // Apply filters
        if ($request->has('searchkey') && $request->searchkey != '') {
            $homeservices->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                  ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        if ($request->has('searchCategory') && $request->searchCategory != '') {
            $homeservices->where('category_id', $request->searchCategory);
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $homeservices->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $homeservices->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $homeservices->where('area_id', $request->searchArea);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '') {
            $homeservices->where('verified', $request->searchVerified);
        }

        // Get all homeservices (no pagination)
        $homeservices = $homeservices->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Business Title',
            'Category',
            'Description',
            'Phone',
            'Email',
            'Address',
            'Area',
            'District',
            'State',
            'Website',
            'Facebook',
            'Twitter',
            'LinkedIn',
            'Instagram',
            'YouTube',
            'Status',
            'Verified',
            'Created At'
        ]);

        // Add data rows
        foreach ($homeservices as $homeservice) {
            $csv->insertOne([
                $homeservice->business_title,
                $homeservice->homeservicecategory->name ?? 'N/A',
                $homeservice->description,
                $homeservice->phone,
                $homeservice->email,
                $homeservice->address,
                $homeservice->area->name ?? 'N/A',
                $homeservice->district->name ?? 'N/A',
                $homeservice->state->name ?? 'N/A',
                $homeservice->website,
                $homeservice->facebook,
                $homeservice->twitter,
                $homeservice->linked_in,
                $homeservice->instagram,
                $homeservice->youtube,
                $homeservice->status ? 'Active' : 'Inactive',
                $homeservice->verified ? 'Verified' : 'Not Verified',
                $homeservice->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'HomeServices_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }
    /**
     * Get homeservices data for DataTables.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function gethomeservices(Request $request)
    {
        // This is an alias method that calls the existing getHomeServicesData method
        return $this->getHomeServicesData($request);
    }
}
