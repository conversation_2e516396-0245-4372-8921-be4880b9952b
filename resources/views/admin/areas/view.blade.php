@extends('layouts.master')
@section('title', 'Clients View- Paidash')
@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Client</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/clients" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Clients
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Client</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="client-information"
                                    data-bs-toggle="pill" data-bs-target="#client-information-d" type="button" role="tab"
                                    aria-controls="client-information-d" aria-selected="false" tabindex="-1">
                                    Client Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="service-log"
                                    data-bs-toggle="pill" data-bs-target="#service-log-d" type="button" role="tab"
                                    aria-controls="service-log-d" aria-selected="false" tabindex="-1">
                                    Service Log
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="invoice-history"
                                    data-bs-toggle="pill" data-bs-target="#invoice-history-d" type="button"
                                    role="tab" aria-controls="invoice-history-d" aria-selected="false"
                                    tabindex="-1">
                                    Invoice History
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="payments"
                                    data-bs-toggle="pill" data-bs-target="#payments-d" type="button" role="tab"
                                    aria-controls="payments-d" aria-selected="false" tabindex="-1">
                                    Payments
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="discounts"
                                    data-bs-toggle="pill" data-bs-target="#discounts-d" type="button" role="tab"
                                    aria-controls="discounts-d" aria-selected="false" tabindex="-1">
                                    Discounts
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="account-ledger"
                                    data-bs-toggle="pill" data-bs-target="#account-ledger-d" type="button"
                                    role="tab" aria-controls="account-ledger-d" aria-selected="false"
                                    tabindex="-1">
                                    Account Ledger
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="client-information-d" role="tabpanel"
                                aria-labelledby="client-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">
                                            <div class="d-flex align-items-center gap-3">
                                                @if ($client->logo && Storage::exists('public/' . $client->logo))
                                                    <img src="{{ asset('storage/' . $client->logo) }}" alt="Profile Image"
                                                        class="border border-white border-width-2-px w-64 h-64 rounded-circle object-fit-cover">
                                                @else
                                                    <div class="w-64 h-64 rounded-circle d-flex justify-content-center align-items-center bg-info-100 text-info-600 fw-bold fs-4 px-16 py-4">
                                                        {{ strtoupper(substr($client->business_name, 0, 1)) }}
                                                    </div>
                                                @endif

                                                <div>
                                                    <h5 class="mb-1">{{ $client->business_name }}</h5>

                                                    @if (Auth::user()->role_id != 3)
                                                        <p class="mb-0 text-sm">
                                                            Executive:
                                                            <span id="assigned-employee" class="editable text-decoration-underline fw-medium">
                                                                {{ $assignedEmployee && $assignedEmployee->employee ? $assignedEmployee->employee->emp_name : 'Not Assigned' }}
                                                            </span>
                                                            <a href="#" class="text-success ms-2" data-bs-toggle="modal" data-bs-target="#exampleModal">
                                                                <iconify-icon icon="mage:edit"></iconify-icon>
                                                            </a>
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>

                                            @if (Auth::user()->role_id != 3)
                                                <div class="form-switch switch-success d-flex align-items-center gap-2">
                                                    <input class="form-check-input" type="checkbox" role="switch" id="switch3"
                                                        data-id="{{ $client->id }}" {{ $client->status == 1 ? 'checked' : '' }}>
                                                    <label class="form-check-label fw-medium text-secondary-light mb-0" for="switch3">
                                                        {{ $client->status == 1 ? 'Active' : 'Inactive' }}
                                                    </label>
                                                </div>
                                            @endif
                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Business Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Business Name:</strong> <span class="text-muted">{{ $client->business_name }}</span></li>
                                                            <li><strong>Client Code:</strong> <span class="text-muted">{{ $client->client_code }}</span></li>
                                                            <li><strong>Contact Person:</strong> <span class="text-muted">{{ $client->name }}</span></li>
                                                            <li><strong>GST No.:</strong> <span class="text-muted">{{ $client->gst ?? 'NA' }}</span></li>
                                                            <li><strong>PAN No.:</strong> <span class="text-muted">{{ $client->pan ?? 'NA' }}</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Contact Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Mobile:</strong> <span class="text-muted">{{ $client->phone }}</span></li>
                                                            <li><strong>Address:</strong> <span class="text-muted">{{ $client->address }}</span></li>
                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $client->area ?? 'NA' }}</span></li>
                                                            <li>
                                                                <strong>Location:</strong>
                                                                @php
                                                                    $mapLink = !empty($client->lang_lat)
                                                                        ? 'https://www.google.com/maps?q=' . urlencode($client->lang_lat)
                                                                        : '#';
                                                                @endphp
                                                                <a href="{{ $mapLink }}"
                                                                class="d-flex align-center text-decoration-none {{ empty($client->lang_lat) ? 'text-muted' : 'text-success' }}"
                                                                target="_blank" {{ empty($client->lang_lat) ? 'aria-disabled=true' : '' }}>
                                                                    <iconify-icon icon="mdi:map-marker" class="me-1" style="font-size: 18px;"></iconify-icon>
                                                                    {{ empty($client->lang_lat) ? 'No Location' : 'View on Map' }}
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Additional Details Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:information-outline" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Additional Details
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>HCF No.:</strong> <span class="text-muted">{{ $client->hcf_no }}</span></li>
                                                            <li><strong>HCF Type:</strong> <span class="text-muted">{{ $client->hcf_type }}</span></li>
                                                            <li><strong>Description:</strong> <span class="text-muted">{{ $client->description }}</span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="service-log-d" role="tabpanel"
                                aria-labelledby="service-log" tabindex="0">
                                <div class="d-flex justify-content-end mb-2">
                                    <button class="btn btn-sm btn-primary text-nowrap" onclick="downloadCSV()">
                                        Download CSV
                                    </button>
                                </div>
                                <hr class="mb-4">
                                <!-- Service Log Cards -->
                                <div class="row" id="serviceLogBody1">
                                    @foreach ($client_services as $client_service)
                                        <div class="col-xxl-12 mb-4">
                                            <div class="card shadow-none border">
                                                <!-- Card Header with Service Name, Type, and Status Badge -->
                                                <div
                                                    class="card-header bg-light fw-bold d-flex justify-content-between align-items-center">
                                                    <div class=" d-flex align-items-center">
                                                        <iconify-icon icon="mdi:cog-outline"
                                                            width="22"></iconify-icon>
                                                        {{ $client_service->service_type_data->name }} <small
                                                            class="text-muted">({{ $client_service->service->service_name }})</small>
                                                    </div>
                                                    @if ($client_service->status == 1)
                                                        <span class="badge bg-success text-white">Active</span>
                                                    @else
                                                        <span class="badge bg-primary text-white">Completed</span>
                                                    @endif
                                                </div>

                                                <!-- Card Body with Grid Layout -->
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-4 d-flex align-items-center">
                                                            <iconify-icon icon="mdi:calendar-start"
                                                                width="20"></iconify-icon>
                                                            <strong class="ms-2">Start:</strong>
                                                            {{ $client_service->start_date ? date('d-m-Y', strtotime($client_service->start_date)) : 'NA' }}
                                                        </div>
                                                        <div class="col-4 d-flex align-items-center">
                                                            <iconify-icon icon="mdi:calendar-end"
                                                                width="20"></iconify-icon>
                                                            <strong class="ms-2">End:</strong>
                                                            {{ $client_service->end_date ? date('d-m-Y', strtotime($client_service->end_date)) : 'NA' }}
                                                        </div>
                                                        @if ($client_service->beds_count)
                                                            <div class="col-4 d-flex align-items-center">
                                                                <iconify-icon icon="mdi:bed-outline"
                                                                    width="20"></iconify-icon>
                                                                <strong class="ms-2">Units: </strong>
                                                                {{ $client_service->beds_count ? $client_service->beds_count : 'NA' }}
                                                            </div>
                                                        @endif
                                                        @if ($client_service->unit_price)
                                                            <div class="col-4 d-flex align-items-center">
                                                                <iconify-icon icon="mdi:cash"
                                                                    width="20"></iconify-icon>
                                                                <strong class="ms-2">Unit Price: </strong>
                                                                {{ $client_service->unit_price ? '₹' . $client_service->unit_price : 'NA' }}
                                                            </div>
                                                        @endif

                                                        <div class="col-4 d-flex align-items-center mt-2">
                                                            <iconify-icon icon="mdi:cash" width="20"></iconify-icon>
                                                            <strong class="ms-2">Total Price:</strong>
                                                            ₹{{ $client_service->total_price }}
                                                        </div>
                                                        <div class="col-4 d-flex align-items-center mt-2">
                                                            <iconify-icon icon="mdi:credit-card-outline"
                                                                width="20"></iconify-icon>
                                                            <strong class="ms-2">Cycle:</strong>
                                                            {{ $client_service->payment_cycle }}
                                                        </div>
                                                        @if ($client_service->service_id == 2)
                                                            <div class="col-6 d-flex align-items-center mt-2">
                                                                <iconify-icon icon="mdi:bed-outline"
                                                                    width="20"></iconify-icon>
                                                                <strong class="ms-2">Non Bedded Type:</strong>
                                                                {{ $client_service->non_bedded_type }}
                                                            </div>
                                                        @endif
                                                        <div class="col-12 mt-2 d-flex align-items-center">
                                                            <iconify-icon icon="mdi:comment-text-outline"
                                                                width="20"></iconify-icon>
                                                            <strong class="ms-2">Comments:</strong>
                                                            {{ $client_service->description }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <div class="tab-pane fade" id="account-ledger-d" role="tabpanel"
                                aria-labelledby="account-ledger" tabindex="0">
                                <div class="row mb-24">

                                    <div class="col-sm-6">
                                        <div class="input-group mb-3 date_range">
                                            <input type="text" class="form-control" placeholder="Date range"
                                                id="daterange" name="daterange">
                                            <div class="input-group-append">
                                                <span class="input-group-text" id="basic-addon2">
                                                    <iconify-icon icon="mdi:calendar" width="24"
                                                        height="24"></iconify-icon>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <p class="text-right mb-0"><strong>Statement of Accounts</strong></p>
                                        <p class="text-right mb-0"><span id="dateRangeText"></span>
                                            </p>
                                    </div>
                                </div>

                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td><strong>Advance Amount</strong></td>
                                            <td><span id="advanceAmount">₹ 0.00</span>
                                                <button class="btn btn-sm btn-primary text-nowrap ms-4" id="account_settle">
                                                Account Settle
                                            </button></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Opening Balance</strong></td>
                                            <td><span id="openingBalance">₹ 0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Invoiced Amount</strong></td>
                                            <td><span id="invoicedAmount">₹ 0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Amount Received</strong></td>
                                            <td><span id="amountReceived">₹ 0.00</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Balance Due</strong></td>
                                            <td><span id="balanceDue">₹ 0.00</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="d-none d-md-block table-responsive">
                                    <table id="accountsTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Transaction</th>
                                                <th>Details</th>
                                                <th>Amount</th>
                                                <th>Payment</th>
                                                <th>Balance</th>
                                            </tr>
                                        </thead>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-end">Total</th>
                                                <th><strong>₹ 0.00</strong></th>
                                                <th colspan="2"><strong>₹ 0.00</strong></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="payments-d" role="tabpanel" aria-labelledby="payments"
                                tabindex="0">
                                <div class="d-flex align-items-center justify-content-end">
                                    <div class="input-group date_range mb-3" style="max-width: 300px;">
                                        <input type="text" class="form-control" name="payment_daterange"
                                            id="payment_daterange">
                                        <div class="input-group-append">
                                            <span class="input-group-text" id="basic-addon2">
                                                <iconify-icon icon="mdi:calendar" width="24"
                                                    height="24"></iconify-icon>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-none d-md-block table-responsive">
                                    <table class="table" id="paymentsTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Invoice Code</th>
                                                <th>Payment Date</th>
                                                <th>Payment Mode</th>
                                                <th>Paid Amount</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="3" class="text-end">Total</th>
                                                <th id="totalPayment" colspan="2"><strong>₹ 0.00</strong></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="discounts-d" role="tabpanel" aria-labelledby="discounts"
                                tabindex="0">
                                @can('client-discount')
                                    <div class="d-flex justify-content-end mb-2">
                                        <!-- Discount Button -->
                                        <button class="btn btn-sm btn-primary text-nowrap" data-bs-toggle="modal"
                                            data-bs-target="#discountModal">
                                            Apply Discount
                                        </button>
                                    </div>
                                @endcan
                                <div class="d-none d-md-block">
                                    <table id="discountsTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Discount Amount</th>
                                                <th>Reason</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="1" class="text-end">Total </th>
                                                <th id="totalDiscount" colspan="2"><strong>₹ 0.00</strong></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="invoice-history-d" role="tabpanel"
                                aria-labelledby="invoice-history" tabindex="0">

                                <div class="d-flex align-items-center justify-content-end">
                                    <div class="input-group date_range mb-3" style="max-width: 300px;">
                                        <input type="text" class="form-control" name="daterange"
                                            id="invoice_daterange">
                                        <div class="input-group-append">
                                            <span class="input-group-text" id="basic-addon2">
                                                <iconify-icon icon="mdi:calendar" width="24"
                                                    height="24"></iconify-icon>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-none d-md-block">
                                    <input type="hidden" name="client" id="client" value="{{ $client->id }}">
                                    <table id="invoicesTable"
                                        class="table table-sm table-striped table-hover table-bordered text-wrap mt-4"
                                        style="width:100%">
                                        <thead>
                                            <tr>
                                                <th>Invoice</th>
                                                <th>Invoice Date</th>
                                                <th>Invoice Amount</th>
                                                <th>Due Amount</th>
                                                <th>Status</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2" class="text-end">Total</th>
                                                <th id="totalInvoice" ><strong>₹ 0.00</strong></th>
                                                <th id="totalInvoice" colspan="3"><strong>₹ 0.00</strong></th>
                                            </tr>
                                        </tfoot>

                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
    <!-- Modal Start -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog modal-dialog-centered">
            <div class="modal-content radius-16 bg-base">
                <div class="modal-header py-16 px-24 border border-top-0 border-start-0 border-end-0">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Assign Employee</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-24">
                    <form id="assignEmployeeForm">
                        @csrf
                        <div class="row">
                            <div class="col-12 mb-20">
                                <label class="form-label fw-semibold text-primary-light text-sm mb-8">Employee</label>
                                <select class="form-control select2" name="employee_id" id="employee_id">
                                    <option value="">Select Employee</option>
                                    @foreach ($employees as $employee)
                                        <option value="{{ $employee->id }}"
                                            {{ old('employee_id', $client->employee_id) == $employee->id ? 'selected' : '' }}>
                                            {{ $employee->emp_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <input type="hidden" name="client_id" id="client_id" value="{{ $client->id }}">
                            </div>

                            <div class="d-flex align-items-center justify-content-end gap-3 mt-24">
                                <button type="reset"
                                    class="btn btn-sm btn-neutral-500 border-neutral-100 px-32 radius-8"
                                    data-bs-dismiss="modal">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-sm btn-primary-600 px-32 radius-8">
                                    Save
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal End -->
    <!-- Discount Modal -->
    <div class="modal fade" id="discountModal" tabindex="-1" aria-labelledby="discountModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h7 class="modal-title" id="discountModalLabel"><b>Apply Discount</b></h7>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="discountForm">
                        @csrf
                        <div class="mb-3">
                            <label for="pendingAmount" class="form-label">Total Pending Amount</label>
                            <input type="text" class="form-control" id="pendingAmount"
                                value="{{ $client->pending_amount }}" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="discountAmount" class="form-label">Discount Amount<span
                                    class="text-danger-600">*</span></label>
                            <input type="number" class="form-control" id="discountAmount" name="discount_amount"
                                required>
                            <span class="text-danger d-none" id="discountError">Discount cannot exceed pending
                                amount</span>
                        </div>

                        <div class="mb-3">
                            <label for="discountRemarks" class="form-label">Remarks <span
                                    class="text-danger-600">*</span></label>
                            <textarea class="form-control" id="discountRemarks" name="discountRemarks" required></textarea>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Apply Discount</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>
@stop
@section('script')
    <script>
        $(document).ready(function() {
            var invoiceTable;
            var paymentTable;
            //   $('#employee_id').select2();
            $('#employee_id').select2({
                width: '100%', // Ensure it fits the container
                placeholder: "Select an employee",
                allowClear: true
            });
            $(".form-check-input").on("change", function() {
                let isChecked = $(this).prop("checked"); // Get switch status
                let clientId = $(this).data("id"); // Get Client ID
                let status = isChecked ? 1 : 0; // 1 = Active, 0 = Inactive
                let switchElement = $(this); // Store switch reference
                let labelElement = $(this).next("label"); // Get label next to switch

                let actionText = status === 1 ? "activate" : "deactivate";
                let warningText = status === 1 ?
                    "Do you want to activate this client?" :
                    "Changing the status will also deactivate client services. Are you sure you want to proceed?";

                Swal.fire({
                    title: "Are you sure?",
                    text: warningText,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#28a745",
                    cancelButtonColor: "#d33",
                    confirmButtonText: `Yes, ${actionText} it!`,
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "/clients/update-status", // Your route to update status
                            type: "POST",
                            data: {
                                client_id: clientId,
                                status: status,
                                _token: $('meta[name="csrf-token"]').attr(
                                    "content") // CSRF token
                            },
                            success: function(response) {
                                Swal.fire("Updated!", "Client status has been changed.",
                                    "success");

                                // Update text dynamically
                                if (status === 1) {
                                    labelElement.text("Active");
                                } else {
                                    labelElement.text("Inactive");
                                }
                            },
                            error: function() {
                                Swal.fire("Error!", "Something went wrong.", "error");
                                switchElement.prop("checked", !
                                    isChecked); // Revert switch if error
                            }
                        });
                    } else {
                        switchElement.prop("checked", !isChecked); // Revert switch if canceled
                    }
                });
            });
        });

        $(document).ready(function() {
            var invoiceTableInitialized = false;
            var paymentsTableInitialized = false;
            var accountsTableInitialized = false;
            var discountTableInitialized = false;

            // Invoice History Tab
            $('#invoice-history').on('shown.bs.tab', function(e) {
                if (!invoiceTableInitialized) {
                    if (!$.fn.DataTable.isDataTable(
                            '#invoicesTable')) { // Check if DataTable is already initialized
                    invoiceTable=  $('#invoicesTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('invoices.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                    d.daterange = $('#invoice_daterange').val();
                                }
                            },
                            columns: [{
                                    data: 'invoice_code',
                                    name: 'invoice_code'
                                },
                                {
                                    data: 'invoice_date',
                                    name: 'invoice_date'
                                },
                                {
                                    data: 'total_amount_due',
                                    name: 'total_amount_due',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'unpaid_amount',
                                    name: 'unpaid_amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'invoice_status',
                                    name: 'invoice_status',
                                    orderable: false,
                                    searchable: false
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [{
                                extend: 'csv',
                                text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                                className: 'btn btn-success btn-sm bg-success-500',
                                filename: 'Invoices_' + new Date().toISOString().slice(0,
                                    10),
                                    customize: function (csv) {
                                        return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                                    },
                            action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                                }, 500); // Small delay for better UX
                                  }
                            }],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {

                                var api = this.api();

                                // Sum for column 2
                                var totalColumn2 = api
                                    .column(2, { page: 'current' }) // Column index 2 (discount_amount)
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                // Sum for column 3
                                var totalColumn3 = api
                                    .column(3, { page: 'current' }) // Column index 3
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);


                                // Update footer values
                                $(api.column(2).footer()).html(`<strong>₹ ${totalColumn2.toFixed(2)}</strong>`);
                                $(api.column(3).footer()).html(`<strong>₹ ${totalColumn3.toFixed(2)}</strong>`);
                            }

                        });

                        invoiceTableInitialized = true; // Mark as initialized
                    }
                }
            });

            // Payments Tab
            $('#payments').on('shown.bs.tab', function(e) {
                if (!paymentsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#paymentsTable')) { // Check if already initialized
                        paymentTable=  $('#paymentsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('payments.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                    d.daterange = $('#payment_daterange').val();
                                }
                            },
                            columns: [

                                {
                                    data: 'invoice_code',
                                    name: 'invoice_code'
                                },
                                {
                                    data: 'paid_on',
                                    name: 'paid_on'
                                },
                                {
                                    data: 'payment_mode',
                                    name: 'payment_mode'
                                },
                                {
                                    data: 'amount',
                                    name: 'amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#clientsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [{
                                extend: 'csv',
                                text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                                className: 'btn btn-success btn-sm bg-success-500',
                                filename: 'Payments_' + new Date().toISOString().slice(0,
                                    10),
                                    customize: function (csv) {
                                        return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                                    },
                            action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                                }, 500); // Small delay for better UX
                                  }
                            }],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                var api = this.api();

                                // Calculate total discount amount
                                var total = api
                                    .column(3, {
                                        page: 'current'
                                    })
                                    .data()
                                    .reduce(function(a, b) {
                                        return parseFloat(a) + parseFloat(b);
                                    }, 0);

                                // Format total and display in footer
                                $(api.column(3).footer()).html(
                                    `<strong>₹ ${total.toFixed(2)}</strong>`);
                            }
                        });

                        paymentsTableInitialized = true; // Mark as initialized
                    }
                }
            });

            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#invoice_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#invoice_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#payment_daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#payment_daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });
            // Custom search event triggers
            $('#invoice_daterange').on('apply.daterangepicker', function() {
                invoiceTable.draw();
            });
            $('#payment_daterange').on('apply.daterangepicker', function() {
                paymentTable.draw();
            });
            // Discounts Tab
            $('#discounts').on('shown.bs.tab', function(e) {
                if (!discountTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#discountsTable')) { // Check if already initialized
                        $('#discountsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('discounts.data') }}",
                                data: function(d) {
                                    d.client = $('#client').val();
                                }
                            },
                            columns: [{
                                    data: 'created_at',
                                    name: 'created_at'
                                },
                                {
                                    data: 'discount_amount',
                                    name: 'discount_amount',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'remarks',
                                    name: 'remarks'
                                }
                            ],
                            drawCallback: function(settings) {
                                $('#discountsGrid').empty();
                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>",
                            buttons: [{
                                extend: 'csv',
                                text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                                className: 'btn btn-success btn-sm bg-success-500',
                                filename: 'Discounts' + new Date().toISOString().slice(0,
                                    10),
                                    customize: function (csv) {
                                        return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                                    },
                            action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                                }, 500); // Small delay for better UX
                                  }
                            }],
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                console.log('Footer callback executed'); // Debugging

                                var api = this.api();

                                var total = api
                                    .column(1, {
                                        page: 'current'
                                    }) // Column index 1 (discount_amount)
                                    .data()
                                    .reduce(function(a, b) {
                                        return (parseFloat(a) || 0) + (parseFloat(b) || 0);
                                    }, 0);

                                console.log('Total discount:', total); // Debugging

                                $(api.column(1).footer()).html(
                                    `<strong>₹ ${total.toFixed(2)}</strong>`);
                            }
                        });

                        discountTableInitialized = true; // Mark as initialized
                    }
                }
            });

            // Account Ledger Tab
            $('#account-ledger').on('shown.bs.tab', function(e) {
                if (!accountsTableInitialized) {
                    if (!$.fn.DataTable.isDataTable('#accountsTable')) { // Check if already initialized
                        var table = $('#accountsTable').DataTable({
                            processing: true,
                            serverSide: true,
                            searching: false,
                            pageLength: 25,
                            ajax: {
                                url: "{{ route('accounts.data') }}",
                                data: function(d) {
                                    // d.searchkey = $('#searchkey').val();
                                    d.daterange = $('#daterange').val();
                                    d.client = $('#client').val();
                                },
                                beforeSend: function() {
                                    fetchSummary(); // Fetch summary data
                                }
                            },
                            columns: [{
                                    data: 'transaction_date',
                                    name: 'transaction_date',
                                    render: function(data, type, row) {
                                        if (!data) return "";

                                        // Parse date using moment.js (ensure moment.js is included in your project)
                                        return moment(data, ['YYYY-MM-DD', 'DD-MM-YYYY', 'MM/DD/YY']).format('DD-MM-YYYY');
                                    }
                                },
                                {
                                    data: 'transaction',
                                    name: 'transaction'
                                },
                                {
                                    data: 'detials',
                                    name: 'detials'
                                },
                                {
                                    data: 'amount',
                                    name: 'amount',
                                    render: function(data, type, row) {
                                        if (!data || parseFloat(data) === 0) {
                                            return ""; // You can replace this with "N/A" or any other text
                                        }
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'payments',
                                    name: 'payments',
                                    render: function(data, type, row) {
                                        if (!data || parseFloat(data) === 0) {
                                            return ""; // You can replace this with "N/A" or any other text
                                        }
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                                {
                                    data: 'balance',
                                    name: 'balance',
                                    render: function(data, type, row) {
                                        return `₹ ${parseFloat(data).toFixed(2)}`;
                                    }
                                },
                            ],
                            columnDefs: [{
                                    orderable: false,
                                    targets: '_all'
                                } // Disables sorting for all columns
                            ],
                            drawCallback: function(settings) {
                                var api = this.api();
                                var data = api.rows().data();
                                var gridContainer = $('#clientsGrid');
                                gridContainer.empty();

                            },
                            dom: "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" +
                                // Entries Dropdown & CSV Button
                                "<'row'<'col-md-12'tr>>" +
                                "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
                                buttons: [
                                    {
                                        extend: 'csv',
                                        text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
                                        className: 'btn btn-success btn-sm bg-success-500',
                                        filename: 'Account_ledger_' + new Date().toISOString().slice(0, 10),
                                        customize: function (csv) {
                                            return '\uFEFF' + csv; // Add BOM to ensure proper UTF-8 encoding
                                        },
                        action: function (e, dt, node, config) {
                            $('#exportLoader').show(); // Show loader

                            setTimeout(() => {
                                $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
                                $('#exportLoader').hide(); // Hide loader after export
                            }, 500); // Small delay for better UX
                        },
                                        exportOptions: {
                                            modifier: {
                                                search: 'applied',
                                                order: 'applied'
                                            },
                                            columns: ':visible',
                                            format: {
                                                body: function (data, row, column, node) {
                                                    // Assuming date column is at index 0 (adjust if necessary)
                                                    if (column === 0) {
                                                        return formatDate(data);
                                                    }
                                                    return data;
                                                }
                                            }
                                        }
                                    }
]

,
                            infoCallback: function(settings, start, end, max, total, pre) {
                                return `Showing ${start} to ${end} of ${total} records`;
                            },
                            footerCallback: function(row, data, start, end, display) {
                                var api = this.api();

                                // Function to calculate the total of a column
                                function calculateTotal(columnIndex) {
                                    return api
                                        .column(columnIndex, {
                                            page: 'current'
                                        })
                                        .data()
                                        .reduce(function(a, b) {
                                            return (parseFloat(a) || 0) + (parseFloat(b) ||
                                                0);
                                        }, 0);
                                }

                                // Calculate totals for column 3 and column 4
                                var totalColumn3 = calculateTotal(3);
                                var totalColumn4 = calculateTotal(4);



                                // Update footer for column 3
                                $(api.column(3).footer()).html(
                                    `<strong>₹ ${totalColumn3.toFixed(2)}</strong>`);

                                // Update footer for column 4
                                $(api.column(4).footer()).html(
                                    `<strong>₹ ${totalColumn4.toFixed(2)}</strong>`);
                            }
                        });
                        // Custom search event triggers
                        $('#daterange').on('apply.daterangepicker', function() {
                            table.draw();
                        });
                        accountsTableInitialized = true; // Mark as initialized
                    }
                }
            });
            // Function to normalize date format to DD-MM-YYYY
function formatDate(inputDate) {
    if (!inputDate) return '';

    // If date contains '/', assume it is M/D/YY or M/D/YYYY
    if (inputDate.includes('/')) {
        let dateParts = inputDate.split('/');
        let month = dateParts[0], day = dateParts[1], year = dateParts[2];

        if (year.length === 2) {
            year = '20' + year; // Convert YY to YYYY
        }

        return `${('0' + day).slice(-2)}-${('0' + month).slice(-2)}-${year}`;
    }

    // If already in DD-MM-YYYY format, return as is
    if (inputDate.includes('-')) {
        return inputDate;
    }

    return inputDate; // If format is unknown, return as is
}



            $("#assignEmployeeForm").submit(function(e) {
                e.preventDefault();

                let client_id = $("#client_id").val();
                let employee_id = $("#employee_id").val();
                let _token = $('input[name="_token"]').val();

                $.ajax({
                    url: "{{ route('assign.employee') }}",
                    type: "POST",
                    data: {
                        client_id: client_id,
                        employee_id: employee_id,
                        _token: _token
                    },
                    success: function(response) {
                        if (response.status === "success") {
                            // Update the displayed employee name
                            $("#assigned-employee").text(response.employee_name);

                            // Show success message using SweetAlert
                            Swal.fire({
                                icon: "success",
                                title: "Success",
                                text: "Employee assigned successfully!",
                                timer: 2000,
                                showConfirmButton: false
                            });

                            // Close the modal
                            $("#exampleModal").modal("hide");
                        } else {
                            Swal.fire({
                                icon: "error",
                                title: "Error",
                                text: response.message
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: "error",
                            title: "Error",
                            text: "Something went wrong!"
                        });
                    }
                });
            });
            $(function() {
                function getCurrentFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, set FY to start from April 1st of this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().month(3).startOf('month'); // April 1st current year
                        fyEnd = moment().add(1, 'year').month(2).endOf('month'); // March 31st next year
                    } else {
                        // Before April, set FY to start from last year's April 1st
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    }
                    return [fyStart, fyEnd];
                }

                function getLastFinancialYear() {
                    var today = moment();
                    var fyStart, fyEnd;

                    // If current month is April or later, last FY is April 1st of last year - March 31st this year
                    if (today.month() + 1 >= 4) {
                        fyStart = moment().subtract(1, 'year').month(3).startOf(
                        'month'); // April 1st last year
                        fyEnd = moment().month(2).endOf('month'); // March 31st this year
                    } else {
                        // If before April, last FY is two years ago April 1st - last year March 31st
                        fyStart = moment().subtract(2, 'year').month(3).startOf(
                        'month'); // April 1st two years ago
                        fyEnd = moment().subtract(1, 'year').month(2).endOf(
                        'month'); // March 31st last year
                    }
                    return [fyStart, fyEnd];
                }

                function cb(start, end) {
                    $('#daterange').val(start.format('DD/MM/YYYY') + ' - ' + end.format('DD/MM/YYYY'));
                }

                $('#daterange').daterangepicker({
                    startDate: getCurrentFinancialYear()[0], // Default to Current Financial Year
                    endDate: getCurrentFinancialYear()[1],
                    autoApply: true,
                    locale: {
                        format: 'DD/MM/YYYY'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment()
                            .subtract(1, 'month').endOf('month')
                        ],
                        'Current Financial Year': getCurrentFinancialYear(),
                        'Last Financial Year': getLastFinancialYear()
                    }
                }, cb);

                // Set initial date display
                cb(getCurrentFinancialYear()[0], getCurrentFinancialYear()[1]);

            });


        });

        const dummyData = [{
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 5,
                unit_price: 240,
                start_date: "2024-01-01",
                end_date: "2025-01-01",
                total_price: 1200,
                payment_cycle: "Yearly",
                status: "Active",
                comments: "Renewal due next year"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 4,
                unit_price: 200,
                start_date: "2023-09-10",
                end_date: "2024-09-10",
                total_price: 800,
                payment_cycle: "Yearly",
                status: "Completed",
                comments: "Client upgraded to premium plan"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 2,
                unit_price: 250,
                start_date: "2024-02-01",
                end_date: "2024-08-01",
                total_price: 500,
                payment_cycle: "Quarterly",
                status: "Completed",
                comments: "Ongoing risk analysis"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 10,
                unit_price: 500,
                start_date: "2023-05-15",
                end_date: "2023-11-30",
                total_price: 5000,
                payment_cycle: "One-time",
                status: "Completed",
                comments: "Project successfully delivered"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 3,
                unit_price: 100,
                start_date: "2024-04-01",
                end_date: "",
                total_price: 300,
                payment_cycle: "Monthly",
                status: "Active",
                comments: "Completed"
            },
            {
                service_name: "Biomedical Waste",
                service_type: "Bedded",
                beds_count: 6,
                unit_price: 250,
                start_date: "2023-07-10",
                end_date: "2024-07-10",
                total_price: 1500,
                payment_cycle: "Yearly",
                status: "Cancelled",
                comments: "Client moved to another provider"
            }
        ];

        function loadServiceLog() {
            let serviceLogBody = $("#serviceLogBody");
            serviceLogBody.empty();

            dummyData.forEach(service => {
                let badgeClass = service.status === "Active" ? "bg-success text-white" :
                    service.status === "Completed" ? "bg-primary text-white" :
                    "bg-danger text-white";

                let additionalDetails = "";
                if (service.service_type === "Bedded") {
                    additionalDetails = `
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:bed-outline" width="20"></iconify-icon>
                        <strong class="ms-2">Units:</strong> ${service.beds_count}
                    </div>
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:cash" width="20"></iconify-icon>
                        <strong class="ms-2">Unit Price:</strong> ₹${service.unit_price.toFixed(2)}
                    </div>
                `;
                } else {
                    additionalDetails = `
                    <div class="col-4 d-flex align-items-center">
                        <iconify-icon icon="mdi:label-outline" width="20"></iconify-icon>
                        <strong class="ms-2">Non-Bedded Type:</strong> ${service.non_bedded_type || "N/A"}
                    </div>
                `;
                }

                let card = `
                <div class="col-xxl-12 mb-4">
                    <div class="card shadow-none border">
                        <!-- Card Header with Service Name, Type, and Status Badge -->
                        <div class="card-header bg-light fw-bold d-flex justify-content-between align-items-center">
                            <div class=" d-flex align-items-center">
                                <iconify-icon icon="mdi:cog-outline" width="22"></iconify-icon>
                                ${service.service_name} <small class="text-muted">(${service.service_type})</small>
                            </div>
                            <span class="badge ${badgeClass}">${service.status}</span>
                        </div>

                        <!-- Card Body with Grid Layout -->
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4 d-flex align-items-center">
                                    <iconify-icon icon="mdi:calendar-start" width="20"></iconify-icon>
                                    <strong class="ms-2">Start:</strong> ${service.start_date}
                                </div>
                                <div class="col-4 d-flex align-items-center">
                                    <iconify-icon icon="mdi:calendar-end" width="20"></iconify-icon>
                                    <strong class="ms-2">End:</strong> ${service.end_date || "Ongoing"}
                                </div>

                                ${additionalDetails}

                                <div class="col-4 d-flex align-items-center mt-2">
                                    <iconify-icon icon="mdi:cash" width="20"></iconify-icon>
                                    <strong class="ms-2">Total Price:</strong> ₹${service.total_price.toFixed(2)}
                                </div>
                                <div class="col-4 d-flex align-items-center mt-2">
                                    <iconify-icon icon="mdi:credit-card-outline" width="20"></iconify-icon>
                                    <strong class="ms-2">Cycle:</strong> ${service.payment_cycle}
                                </div>

                                <div class="col-12 mt-2 d-flex align-items-center">
                                    <iconify-icon icon="mdi:comment-text-outline" width="20"></iconify-icon>
                                    <strong class="ms-2">Comments:</strong> ${service.comments || "-"}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

                serviceLogBody.append(card);
            });
        }

        function fetchSummary() {
            $.ajax({
                url: "{{ route('accounts.summary') }}",
                type: "GET",
                data: {
                    daterange: $('#daterange').val(),
                    client: $('#client').val()
                },
                success: function(response) {
                    $('#dateRangeText').text(response.date_range);
                    $('#openingBalance').text('₹ ' + response.opening_balance);
                    $('#invoicedAmount').text('₹ ' + response.invoiced_amount);
                    $('#amountReceived').text('₹ ' + response.amount_received);
                    $('#balanceDue').text('₹ ' + response.balance_due);
                    $('#advanceAmount').text('₹ ' + response.advanc_amount);
                    if (parseFloat(response.advanc_amount) > 0) {
                        $('#account_settle').show();  // Show button
                    } else {
                        $('#account_settle').hide();  // Hide button
                    }
                }
            });
        }
        $(document).ready(loadServiceLog);
        $(document).ready(function() {
            $("#discountForm").on("submit", function(e) {
                e.preventDefault();

                var pendingAmount = parseFloat($("#pendingAmount").val());
                var discountAmount = parseFloat($("#discountAmount").val());
                var discountRemarks = $("#discountRemarks").val();

                if (discountAmount > pendingAmount) {
                    $("#discountError").removeClass("d-none");
                } else {
                    $("#discountError").addClass("d-none");

                    // Submit the form via AJAX (Modify URL as needed)
                    $.ajax({
                        url: "{{ route('apply.discount', $client->id) }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            discount_amount: discountAmount,
                            remarks: discountRemarks
                        },
                        success: function(response) {
                            alert("Discount Applied Successfully!");
                            location.reload(); // Reload to update the UI
                        },
                        error: function() {
                            alert("Error applying discount.");
                        }
                    });
                }
            });
        });
        $(document).on("click", "#account_settle", function () {
            let clientId = $("#client").val(); // Get client ID from dropdown or hidden input

            if (!clientId) {
                Swal.fire("Error", "Please select a client.", "error");
                return;
            }

            Swal.fire({
                title: "Are you sure?",
                text: "This will settle outstanding invoices using the available advance amount.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes, Settle Now",
                cancelButtonText: "Cancel",
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('client.settle_account') }}", // Adjust to your route
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            client_id: clientId,
                        },
                        beforeSend: function () {
                            $("#account_settle").prop("disabled", true).text("Processing...");
                        },
                        success: function (response) {
                            Swal.fire("Success", response.message, "success").then(() => {
                                location.reload(); // Reload to update the UI
                            });
                        },
                        error: function (xhr) {
                            Swal.fire("Error", xhr.responseJSON.message || "Something went wrong!", "error");
                            $("#account_settle").prop("disabled", false).text("Account Settle");
                        },
                    });
                }
            });
        });

    </script>
@stop
