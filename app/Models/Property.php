<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Property extends Model
{
    use HasFactory;
    protected $guarded  = [];
     public function builder()
    {
        return $this->belongsTo(Builder::class, 'builder_id');
    }
    public function images()
    {
        return $this->hasMany(PropertyImage::class, 'property_id');
    }
    /**
     * Get the area that the property belongs to.
     */
    public function area()
    {
        return $this->belongsTo(Area::class);
    }
    public function district()
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    // Keep city method for backward compatibility during transition
    public function city()
    {
        return $this->district();
    }
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }
    public function getBuildingTypeNameAttribute()
    {
        $types = [
            1 => 'Apartment',
            2 => 'Individual',
            3 => 'Villa',
            4 => 'Community',
            5 => 'Other',
        ];

        return $types[$this->building_type] ?? 'Unknown';
    }
    public function getFacingNameAttribute()
    {
        $facings = [
            1 => 'East',
            2 => 'North',
            3 => 'Northeast',
            4 => 'West',
            5 => 'South',
            6 => 'Northwest',
            7 => 'Southeast',
            8 => 'Southwest',
        ];

        return $facings[$this->facing] ?? 'Unknown';
    }
    public function buildingType()
    {
        return $this->belongsTo(BuildingType::class, 'building_type_id');
    }

    public function areaType()
    {
        return $this->belongsTo(AreaType::class, 'area_type_id');
    }

}
