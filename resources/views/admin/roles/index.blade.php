@extends('admin.layouts.master')
@section('content')
<div class="dashboard-main-body">
    @if(session('success'))
        <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
            <div class="d-flex align-items-center gap-2">
                <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                <span id="snackbar-message"> {{ session('success') }}</span>

            </div>
            <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
            </button>
        </div>
    @endif
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Roles List</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Roles List</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                    <div class="icon-field w-100">
                        {{-- <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Client" id="searchkey">
                        <span class="icon">
                            <iconify-icon icon="ion:search-outline"></iconify-icon>
                        </span> --}}
                    </div>
                </div>
                <div class="d-flex flex-no-wrap align-items-center gap-3">



                    {{-- <a href="/roles/add" class="btn btn-sm btn-primary-600 d-flex"><i class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span>Role</a> --}}
                </div>
            </div>
            <div class="card-body">
                <!-- Table View for Desktop -->
                <div class="d-none d-md-block">
                    <table id="rolesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                        <thead>
                            <tr>
                                <th>S. No</th>
                                <th>Role Name</th>
                                @can('role-permission')
                                <th>Permissions</th>
                                @endcan
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($roles as $key=>$role)
                            <tr>
                                <td>{{++$key}}</td>
                                <td>{{$role->name}}</td>
                                @can('role-permission')
                                <td>

                                    <a href="{{route('roles.permissions', $role->id) }}" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                        <iconify-icon icon="lucide:edit"></iconify-icon>
                                    </a>

                                    </td>
                                    @endcan
                            </tr>

                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Grid View for Mobile -->
                <div id="clientsGrid" class="d-block d-md-none"></div>
            </div>
        </div>

    </div>
    @stop
