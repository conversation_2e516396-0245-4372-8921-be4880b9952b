@extends('admin.layouts.master')
@section('title', 'Clients View- Paidash')
@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .verification-toggle:checked {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }
        .verification-toggle:not(:checked) {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }
        .verification-label {
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .verification-label.text-success {
            color: #28a745 !important;
        }
        .verification-label.text-danger {
            color: #dc3545 !important;
        }
        .form-switch .form-check-input:disabled {
            opadistrict: 0.6;
            cursor: not-allowed;
        }
    </style>
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Supplier</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/admin/suppliers" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Supplier
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Supplier</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="supplier-information"
                                    data-bs-toggle="pill" data-bs-target="#supplier-information-d" type="button" role="tab"
                                    aria-controls="supplier-information-d" aria-selected="false" tabindex="-1">
                                    Supplier Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="service"
                                    data-bs-toggle="pill" data-bs-target="#service-d" type="button" role="tab"
                                    aria-controls="service-d" aria-selected="false" tabindex="-1">
                                    Services
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="images"
                                    data-bs-toggle="pill" data-bs-target="#images-d" type="button"
                                    role="tab" aria-controls="images-d" aria-selected="false"
                                    tabindex="-1">
                                    Images
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="supplier-information-d" role="tabpanel"
                                aria-labelledby="supplier-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">
                                            <div class="d-flex align-items-center gap-3">
                                                @if ($supplier->logo && Storage::exists('public/' . $supplier->logo))
                                                    <img src="{{ asset('storage/' . $supplier->logo) }}" alt="Profile Image"
                                                        class="border border-white border-width-2-px w-64 h-64 rounded-circle object-fit-cover" width="50px">
                                                @else
                                                    <div class="w-64 h-64 rounded-circle d-flex justify-content-center align-items-center bg-info-100 text-info-600 fw-bold fs-4 px-16 py-4">
                                                        {{ strtoupper(substr($supplier->business_title, 0, 1)) }}
                                                    </div>
                                                @endif

                                                <div>
                                                    <h5 class="mb-1 d-flex align-items-center flex-wrap">
                                                        <span class="me-2">{{ $supplier->business_title }}</span>
                                                        @if($supplier->verified == 1)
                                                            <span class="badge bg-success d-flex align-items-center" title="Verified Business" id="verificationBadge">
                                                                <iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning d-flex align-items-center" title="Not Verified" id="verificationBadge">
                                                                <iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </h5>
                                                </div>
                                            </div>

                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Business Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Business Name:</strong> <span class="text-muted">{{ $supplier->business_title }}</span></li>
                                                            <li><strong>Category:</strong> <span class="text-muted">{{ $supplier->suppliercategory->name }}</span></li>
                                                            <li><strong>Description:</strong> <span class="text-muted">{{ $supplier->description }}</span></li>
                                                            <li class="d-flex align-items-center justify-content-between">
                                                                <strong>Verification Status:</strong>
                                                                <div class="form-switch switch-success d-flex align-items-center">
                                                                    <input class="form-check-input verification-toggle" type="checkbox" role="switch"
                                                                           id="verificationSwitch{{ $supplier->id }}"
                                                                           data-id="{{ $supplier->id }}"
                                                                           {{ $supplier->verified == 1 ? 'checked' : '' }}>
                                                                    <label class="form-check-label ms-2 verification-label {{ $supplier->verified == 1 ? 'text-success' : 'text-danger' }}" for="verificationSwitch{{ $supplier->id }}">
                                                                        {{ $supplier->verified == 1 ? 'Verified' : 'Not Verified' }}
                                                                    </label>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Contact Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Mobile:</strong> <span class="text-muted">{{ $supplier->phone }}</span></li>
                                                            <li><strong>email:</strong> <span class="text-muted">{{ $supplier->email }}</span></li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div> <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:home-map-marker" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Address
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">

                                                            <li><strong>Address:</strong> <span class="text-muted">{{ $supplier->address }}</span></li>
                                                            @if($supplier->nearest_landmark)
                                                            <li><strong>Nearest Landmark:</strong> <span class="text-muted">{{ $supplier->nearest_landmark }}</span></li>
                                                            @endif
                                                            @if($supplier->distance)
                                                            <li><strong>Distance:</strong> <span class="text-muted">{{ $supplier->distance }}</span></li>
                                                            @endif
                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $supplier->area->name ?? 'NA' }}</span></li>
                                                            <li><strong>District:</strong> <span class="text-muted">{{ $supplier->district->name ?? 'NA' }}</span></li>
                                                            <li><strong>State:</strong> <span class="text-muted">{{ $supplier->state->name ?? 'NA' }}</span></li>
                                                            <li>
                                                                <strong>Location:</strong>
                                                                @php
                                                                    $mapLink = !empty($supplier->lang_lat)
                                                                        ? 'https://www.google.com/maps?q=' . urlencode($supplier->lat_long)
                                                                        : '#';
                                                                @endphp
                                                                <a href="{{ $mapLink }}"
                                                                class="d-flex align-center text-decoration-none {{ empty($supplier->lat_long) ? 'text-muted' : 'text-success' }}"
                                                                target="_blank" {{ empty($supplier->lat_long) ? 'aria-disabled=true' : '' }}>
                                                                    <iconify-icon icon="mdi:map-marker" class="me-1" style="font-size: 18px;"></iconify-icon>
                                                                    {{ empty($supplier->lat_long) ? 'No Location' : 'View on Map' }}
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Additional Details Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:share-variant" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                        Social Media
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:facebook" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->facebook }}">{{ $supplier->facebook }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:twitter" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->twitter }}">{{ $supplier->twitter }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:linkedin" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->linked_in }}">{{ $supplier->linked_in }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:youtube" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->youtube }}">{{ $supplier->youtube }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:instagram" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->instagram }}">{{ $supplier->instagram }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:web" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $supplier->website }}">{{ $supplier->website }}</a>
                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="service-d" role="tabpanel"
                                aria-labelledby="service" tabindex="0">
                                <hr class="mb-4">
                                <!-- Service Log Cards -->
                                <ul class="list-group radius-8">
                                    @foreach ($supplier_services as $supplier_category)
                                    <li class="list-group-item border text-secondary-light p-16 bg-base border-bottom-0">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="d-flex">
                                                <iconify-icon icon="mdi:tools" style="font-size: 20px;"></iconify-icon>
                                            </span>
                                            {{ $supplier_category->service_name }}
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="tab-pane fade show" id="service-d" role="tabpanel"
                                aria-labelledby="service" tabindex="0">
                                <hr class="mb-4">
                                <!-- Service Log Cards -->
                                <ul class="list-group radius-8">
                                    @foreach ($supplier_services as $supplier_category)
                                    <li class="list-group-item border text-secondary-light p-16 bg-base border-bottom-0">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="d-flex">
                                                <iconify-icon icon="mdi:tools" style="font-size: 20px;"></iconify-icon>
                                            </span>
                                            {{ $supplier_category->service_name }}
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>

                            <div class="tab-pane fade show" id="images-d" role="tabpanel"
                                aria-labelledby="images" tabindex="0">
                                <div class="row gy-4">
                                    @foreach ($supplier->images as $image)
                                    <div class="col-xxl-3 col-md-4 col-sm-6">
                                        <div class="hover-scale-img border radius-16 overflow-hidden">
                                            <div class="max-h-266-px overflow-hidden">
                                                <img src="{{ url('storage/' . $image->image_path) }}" alt="" class="hover-scale-img__img w-100 h-100 object-fit-cover">
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

@stop

@section('script')
<script>
$(document).ready(function() {
    // Handle verification status toggle
    $(document).on("change", ".verification-toggle", function() {
        let isChecked = $(this).prop("checked"); // Get switch status
        let supplierId = $(this).data("id"); // Get Supplier ID
        let verified = isChecked ? 1 : 0; // 1 = Verified, 0 = Not Verified
        let switchElement = $(this); // Store switch reference
        let labelElement = $(this).next("label"); // Get label next to switch

        let actionText = verified === 1 ? "verify" : "unverify";
        let warningText = verified === 1 ?
            "Do you want to verify this supplier?" :
            "Do you want to remove verification from this supplier?";

        // Revert the toggle until confirmed
        switchElement.prop("checked", !isChecked);

        Swal.fire({
            title: "Are you sure?",
            text: warningText,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes, ${actionText} it!`,
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                // Disable the switch during the request
                switchElement.prop("disabled", true);

                $.ajax({
                    url: "{{ route('suppliers.update-verification') }}",
                    type: "POST",
                    data: {
                        supplier_id: supplierId,
                        verified: verified,
                        _token: $('meta[name="csrf-token"]').attr("content")
                    },
                    success: function(response) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Update the switch state
                        switchElement.prop("checked", isChecked);

                        // Update text dynamically
                        if (verified === 1) {
                            labelElement.text("Verified");
                            labelElement.removeClass("text-danger").addClass("text-success");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-warning').addClass('bg-success')
                                .html('<iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>Verified')
                                .attr('title', 'Verified Business');
                        } else {
                            labelElement.text("Not Verified");
                            labelElement.removeClass("text-success").addClass("text-danger");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-success').addClass('bg-warning')
                                .html('<iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>Not Verified')
                                .attr('title', 'Not Verified');
                        }

                        // Show success message
                        Swal.fire({
                            icon: "success",
                            title: "Updated!",
                            text: "Supplier verification status has been changed.",
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr, status, error) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Show error message
                        let errorMessage = "Failed to update verification status. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: errorMessage,
                            confirmButtonText: "OK"
                        });
                    }
                });
            }
            // If cancelled, the switch remains in its original state (already reverted above)
        });
    });
});
</script>
@endsection
