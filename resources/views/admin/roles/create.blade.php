@extends('admin.layouts.master')

@section('content')
    <div class="container">
        <h2>Create Role</h2>

        {{-- Success & Error Messages --}}
        @if(session('success'))
            <script>
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: "{{ session('success') }}",
                    timer: 2000,
                    showConfirmButton: false
                });
            </script>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        {{-- Form to Create a New Role --}}
        <form action="{{ route('roles.create') }}" method="POST">
            @csrf
            <div class="form-group">
                <label for="roleName">Role Name:</label>
                <input type="text" id="roleName" name="name" class="form-control" placeholder="Enter Role Name" required>
            </div>
            <button type="submit" class="btn btn-primary">Add Role</button>
        </form>

        <hr>

        {{-- List of Existing Roles --}}
        <h3>Existing Roles</h3>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Role Name</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($roles as $index => $role)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $role->name }}</td>
                        <td>
                            {{-- You can add Edit and Delete buttons here --}}
                            <form action="{{ route('roles.delete', $role->id) }}" method="POST" onsubmit="return confirm('Are you sure?');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm">Delete</button>
                            </form>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endsection
