@extends('admin.layouts.master')
@section('title', 'Builder Edit')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Edit Builder</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="{{ route('builders.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Builder
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Edit Builder</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to update Builder</h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard">
                    <form action="{{ route('builders.update', $builder->id) }}" method="POST" id="form"
                        enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <fieldset class="wizard-fieldset show">
                            <div class="row gy-3">
                                <div class="col-sm-12">
                                    <label class="form-label">Builder Logo</label>
                                    <div class="position-relative">
                                        <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                            <div
                                                class="uploaded-img position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50
                                                {{ $builder->image ? '' : 'd-none' }}">
                                                <button type="button"
                                                    class="uploaded-img__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                    <iconify-icon icon="radix-icons:cross-2"
                                                        class="text-xl text-danger-600"></iconify-icon>
                                                </button>
                                                <img id="uploaded-img__preview" class="w-100 h-100 object-fit-cover"
                                                    src="{{ $builder->image ? asset('storage/' . $builder->image) : asset('assets/images/user.png') }}"
                                                    alt="image">
                                            </div>

                                            <label
                                                class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                for="upload-file">
                                                <iconify-icon icon="solar:camera-outline"
                                                    class="text-xl text-secondary-light"></iconify-icon>
                                                <span class="fw-semibold text-secondary-light">Upload</span>
                                                <input id="upload-file" type="file" hidden name="image">
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Builder Name<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="name"
                                        placeholder="Enter Builder Name" value="{{ old('name', $builder->name) }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Email<span class="text-danger-600">*</span></label>
                                    <input type="email" class="form-control wizard-required1" name="email"
                                        placeholder="Enter Email" value="{{ old('email', $builder->email) }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Phone<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="phone"
                                        placeholder="Enter Phone" value="{{ old('phone', $builder->phone) }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">State<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="state_id" name="state_id">
                                        <option value="">Select State</option>
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">District<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="district_id" name="district_id">
                                        <option value="">Select District</option>
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Area<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="area_id" name="area_id">
                                        <option value="">Select Area</option>
                                    </select>
                                </div>


                                <div class="col-sm-6">
                                    <label class="form-label">Status <span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="status">
                                        <option value="1" {{ $builder->status == 1 ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ $builder->status == 0 ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>

                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                    <a href="/builders" class="btn btn-danger-500 border-neutral-100 px-32">Cancel</a>
                                    <button type="submit" class="btn btn-primary-600 px-32">Update</button>
                                </div>
                            </div>
                        </fieldset>
                    </form>

                </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>
@stop

@section('script')
    <script>
        $(document).ready(function() {
            // Initialize Select2 for State
            $('#state_id').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Initialize Select2 for District
            $('#district_id').select2({
                placeholder: 'Select District',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('#state_id').val();
                        if (!stateId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a state first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "districts",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Initialize Select2 for Area
            $('#area_id').select2({
                placeholder: 'Select Area',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let districtId = $('#district_id').val();
                        if (!districtId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a district first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "areas_with_pincode",
                            district_id: districtId, // Send selected district_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Initialize jQuery validation
            $("form").validate({
                ignore: [], // Ensures Select2 fields are not ignored
                rules: {
                    name: {
                        required: true
                    }
                },
                messages: {
                    name: "Client name is required"
                },
                errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });

            // Ensure validation triggers on Select2 field change
            $('select[name="state_id"]').change(function() {
                $('#district_id').html('<option value="">Select District</option>').val(null).trigger(
                    'change.select2');
            });



            // Trigger validation on blur for all fields
            $(document).on("focusout", "input, select, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) { // Check if the form has been initialized with validate()
                    $(this).valid();
                }
            });

        });

        // =============================== Upload Single Image js start here ================================================
        const fileInput = document.getElementById("upload-file");
        const imagePreview = document.getElementById("uploaded-img__preview");
        const uploadedImgContainer = document.querySelector(".uploaded-img");
        const removeButton = document.querySelector(".uploaded-img__remove");

        fileInput.addEventListener("change", (e) => {
            if (e.target.files.length) {
                const src = URL.createObjectURL(e.target.files[0]);
                imagePreview.src = src;
                uploadedImgContainer.classList.remove('d-none');
            }
        });
        removeButton.addEventListener("click", () => {
            imagePreview.src = "";
            uploadedImgContainer.classList.add('d-none');
            fileInput.value = "";
        });
        // =============================== Upload Single Image js End here ================================================
    </script>
@stop
@section('css')
    <style>
        .error-border {
            border: 1px solid #dc3545 !important;
            /* Bootstrap red */
            border-radius: 5px;
        }
    </style>
@stop
