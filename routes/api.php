<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/
Route::post('/fetch_data', [App\Http\Controllers\Api\LocationController::class, 'fetchData']);
Route::post('/fetch_data_other', [App\Http\Controllers\ApiController::class, 'fetch_data']);
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Routes for cascading dropdowns
Route::get('/get-districts/{state_id}', function($state_id) {
    return App\Models\District::where('state_id', $state_id)
                             ->where('status', 1)
                             ->orderByRaw('LOWER(name) ASC')
                             ->get();
});

Route::get('/get-areas/{district_id}', function($district_id) {
    $query = App\Models\Area::where('district_id', $district_id)
                           ->where('status', 1);

    // Add search functionality if 'q' parameter is provided
    if (request()->has('q') && request()->q != '') {
        $searchTerm = request()->q;
        $query->where(function($q) use ($searchTerm) {
            $q->where('name', 'like', '%' . $searchTerm . '%')
              ->orWhere('pincode', 'like', '%' . $searchTerm . '%');
        });
    }

    return $query->select('id', 'name', 'pincode')
                 ->orderByRaw('LOWER(name) ASC')
                 ->limit(100) // Limit to 100 results for performance
                 ->get();
});

// Builder API endpoint
Route::post('/builders', function() {
    try {
        $request = request();

        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:builders,email',
            'phone' => 'required|string|max:20',
            'state_id' => 'required|exists:states,id',
            'district_id' => 'required|exists:districts,id',
            'area_id' => 'required|exists:areas,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Create the builder
        $builder = App\Models\Builder::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'state_id' => $request->state_id,
            'district_id' => $request->district_id,
            'area_id' => $request->area_id,
            'status' => 1
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Builder created successfully',
            'builder' => $builder
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error creating builder: ' . $e->getMessage()
        ], 500);
    }
});
