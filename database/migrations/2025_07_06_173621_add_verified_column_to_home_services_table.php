<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('home_services', function (Blueprint $table) {
            $table->tinyInteger('verified')->default(0)->after('status')->comment('0 = Not Verified, 1 = Verified');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('home_services', function (Blueprint $table) {
            $table->dropColumn('verified');
        });
    }
};
