<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    protected $guarded  = [];
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function district()
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    // Keep city method for backward compatibility during transition
    public function city()
    {
        return $this->district();
    }
    public function area()
    {
        return $this->belongsTo(Area::class, 'area_id');
    }
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function suppliercategory()
    {
        return $this->belongsTo(SupplierCategory::class, 'category_id');
    }
    public function services()
    {
        return $this->hasMany(SupplierService::class, 'supplier_id');
    }

    public function images()
    {
        return $this->hasMany(SupplierImage::class, 'supplier_id');
    }
}
