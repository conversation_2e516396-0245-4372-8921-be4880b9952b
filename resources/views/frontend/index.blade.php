@extends('frontend.layouts.master')
@section('content')
<!-- ============================ Hero Banner  Start================================== -->
<div class="home-slider margin-bottom-0">
  @foreach ($data['banners'] as $item)
    <!-- Slide -->
    <a href="{{ $item->link }}" target="_blank">
      <div class="item">
        <img src="{{ url('storage/' . $item->image) }}" alt="Banner" class="slider-image">
      </div>
    </a>
  @endforeach
</div>

<!-- ============================ Hero Banner End ================================== -->

<!-- ================= Explore Property ================= -->
<section class="pt-4 pb-4">
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-xl-6 col-lg-7 col-md-10 text-center">
				<div class="sec-heading center">
                    <h2 class="text-primary">Real Estate Directory Online</h2>
					<h2>Explore Properties</h2>
					<p>Discover a wide range of residential properties available for rent and sale. From modern apartments to spacious villas, find the perfect home that suits your lifestyle and budget</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center g-4">

                @foreach ($data['properties'] as $property)
                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                    <div class="property-listing card border rounded-3">

                        <div class="listing-img-wrapper p-3">
                            <div class="list-img-slide position-relative">
                                <div class="position-absolute top-0 left-0 ms-3 mt-3 z-1">
                                    @if($property->verified == 1)
                                    <div class="label bg-success text-light d-inline-flex align-items-center justify-content-center">
                                        <span class="svg-icon text-light svg-icon-2hx me-1">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                                                <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                                            </svg>
                                        </span>Verified
                                    </div>
                                    @endif
                                </div>
                                <div class="click rounded-3 overflow-hidden mb-0">
                                    <div>
                                        <a href="/property-details/{{ $property->slug }}">
                                            @if($property->images && $property->images->first())
                                                <img src="{{ url('storage/' . $property->images->first()->image_path) }}" class="img-fluid" alt="{{ $property->title }}" />
                                            @else
                                                <img src="/frontend/assets/img/property-placeholder.jpg" class="img-fluid" alt="{{ $property->title }}" />
                                            @endif
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="listing-caption-wrapper px-3">
                            <div class="listing-detail-wrapper">
                                <div class="listing-short-detail-wrap">
                                    <div class="listing-short-detail">
                                        <div class="d-flex align-items-center">
                                            @if($property->property_type == 1)
                                                <span class="label bg-light-success text-success prt-type me-2">For Sale</span>
                                            @else
                                                <span class="label bg-light-danger text-danger prt-type me-2">For Rent</span>
                                            @endif
                                        </div>
                                        <h4 class="listing-name fw-semibold fs-5 mb-2 mt-3"><a href="/property-details/{{ $property->slug }}">{{ $property->title }}</a></h4>
                                        <div class="prt-location text-muted-2">
                                            <span class="svg-icon svg-icon-2hx">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3" d="M18.0624 15.3453L13.1624 20.7453C12.5624 21.4453 11.5624 21.4453 10.9624 20.7453L6.06242 15.3453C4.56242 13.6453 3.76242 11.4453 4.06242 8.94534C4.56242 5.34534 7.46242 2.44534 11.0624 2.04534C15.8624 1.54534 19.9624 5.24534 19.9624 9.94534C20.0624 12.0453 19.2624 13.9453 18.0624 15.3453Z" fill="currentColor"/>
                                                    <path d="M12.0624 13.0453C13.7193 13.0453 15.0624 11.7022 15.0624 10.0453C15.0624 8.38849 13.7193 7.04535 12.0624 7.04535C10.4056 7.04535 9.06241 8.38849 9.06241 10.0453C9.06241 11.7022 10.4056 13.0453 12.0624 13.0453Z" fill="currentColor"/>
                                                </svg>
                                            </span>
                                            {{ $property->address }},{{ $property->area->name }},{{ $property->city->name }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="price-features-wrapper">
                                <div class="list-fx-features d-flex align-items-center justify-content-between">
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-building-shield fs-xs"></i></div><span class="text-muted-2 fs-sm">{{$property->buildingType->type}}</span>
                                    </div>
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-bed fs-xs"></i></div><span class="text-muted-2 fs-sm">{{$property->no_of_bed_rooms}} BHK</span>
                                    </div>
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-clone fs-xs"></i></div><span class="text-muted-2 fs-sm">{{$property->area_size}} SQFT</span>
                                    </div>
                                </div>
                            </div>

                            <div class="listing-detail-footer d-flex align-items-center justify-content-between py-4">
                                <div class="listing-short-detail-flex">
                                    <h6 class="listing-card-info-price m-0">
                                        @if($property->property_type == 1)
                                            ₹{{ $property->property_cost }}
                                        @else
                                            ₹{{ $property->rent }}
                                        @endif
                                    </h6>
                                </div>
                                <div class="footer-flex">
                                    <a href="/property-details/{{ $property->slug }}" class="prt-view">
                                        <span class="svg-icon text-primary svg-icon-2hx">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M15.43 8.56949L10.744 15.1395C10.6422 15.282 10.5804 15.4492 10.5651 15.6236C10.5498 15.7981 10.5815 15.9734 10.657 16.1315L13.194 21.4425C13.2737 21.6097 13.3991 21.751 13.5557 21.8499C13.7123 21.9488 13.8938 22.0014 14.079 22.0015H14.117C14.3087 21.9941 14.4941 21.9307 14.6502 21.8191C14.8062 21.7075 14.9261 21.5526 14.995 21.3735L21.933 3.33649C22.0011 3.15918 22.0164 2.96594 21.977 2.78013C21.9376 2.59432 21.8452 2.4239 21.711 2.28949L15.43 8.56949Z" fill="currentColor"/>
                                                <path opacity="0.3" d="M20.664 2.06648L2.62602 9.00148C2.44768 9.07085 2.29348 9.19082 2.1824 9.34663C2.07131 9.50244 2.00818 9.68731 2.00074 9.87853C1.99331 10.0697 2.04189 10.259 2.14054 10.4229C2.23919 10.5869 2.38359 10.7185 2.55601 10.8015L7.86601 13.3365C8.02383 13.4126 8.19925 13.4448 8.37382 13.4297C8.54839 13.4145 8.71565 13.3526 8.85801 13.2505L15.43 8.56548L21.711 2.28448C21.5762 2.15096 21.4055 2.05932 21.2198 2.02064C21.034 1.98196 20.8409 1.99788 20.664 2.06648Z" fill="currentColor"/>
                                            </svg>
                                        </span>
                                    </a>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                @endforeach
                <!-- Single Property -->

                <!-- End Single Property -->
                </div>

		</div>

		<div class="row align-items-center justify-content-center">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-5">
				<a href="/properties" class="btn btn-primary px-md-5 rounded" target="_blank">Browse More Properties</a>
			</div>
		</div>

	</div>
</section>
<!-- ================================= Explore Property =============================== -->
<div class="clearfix"></div>
<!-- ============================ Builders Start ================================== -->
<section class="bg-light pt-4 pb-4">
	<div class="container">



		<div class="row justify-content-center">
			<div class="col-lg-7 col-md-10 text-center">
				<div class="sec-heading center">
					<h2>Top Rated Builders</h2>
					<p>Discover a wide range of residential properties available for rent and sale. From modern apartments to spacious villas, find the perfect home that suits your lifestyle and budget.</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center g-4">

			<!-- agents code  -->


                @foreach ($data['builders'] as $builder)
                    <!-- Single Agent -->
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="agents-grid card rounded-3 shadow">
                            <div class="agents-grid-wrap">
                                <div class="fr-grid-thumb mx-auto text-center mt-5 mb-3">
                                    <a href="/properties?searchbuilder={{$builder->id}}" class="d-inline-flex p-1 circle border">
                                        <img src="{{ url('storage/' . $builder->image) }}" class="img-fluid circle" width="130" alt="" />
                                    </a>
                                </div>
                                <div class="fr-grid-deatil text-center mb-5">
                                    <div class="fr-grid-deatil-flex">
                                        <h5 class="fr-can-name mb-0"><a href="/properties?searchbuilder={{$builder->id}}" target="_blank">{{$builder->name}}</a></h5>
                                        <span class="agent-property text-muted-2">{{$builder->propeties_count}} Properties</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach

		</div>

		<!-- Pagination -->
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-5">
				<a href="/builders" class="btn btn-primary px-lg-5 rounded"  target="_blank">Explore More Builders</a>
			</div>
		</div>

	</div>
</section>
<div class="clearfix"></div>
<!-- ============================ Builders End ================================== -->

<!-- ============================ Ad section Start ================================== -->
<section class="bg-primary pt-4 pb-4">
    <div class="container">
        <div class="row align-items-center justify-content-center ">
            <!-- good-places code  -->
            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 text-center">
                <a href="{{ $data['ads'][4]->link }}" target="_blank">
                    <img src="{{ url('storage/' . $data['ads'][4]->image) }}"  width="80%" alt="">
                </a>
            </div>
            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 text-center">
                <a href="{{ $data['ads'][5]->link }}" target="_blank">
                    <img src="{{ url('storage/' . $data['ads'][5]->image) }}"  width="80%" alt="">
                </a>
            </div>
            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 text-center">
                <a href="{{ $data['ads'][6]->link }}" target="_blank">
                    <img src="{{ url('storage/' . $data['ads'][6]->image) }}"  width="80%" alt="">
                </a>
            </div>
        </div>
    </div>
</section>
<!-- ============================ Ad section End ================================== -->
<div class="clearfix"></div>
<!-- ============================ Home Services Start ================================== -->
<section class=" bg-light pb-4 pt-4">
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-xl-6 col-lg-7 col-md-10">
				<div class="sec-heading text-center">

					<h2>Home Services</h2>
					<p>Looking for reliable home service professionals? Find and hire verified plumbers, electricians, carpenters, painters, pest control experts, and welders in your area. Quick, affordable, and hassle-free solutions for all your household needs.</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center gx-3 gy-3">

			<!-- homeservice categories - simplified text only -->
                @foreach ($data['categories'] as $category)
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                    <div class="category-item h-100">
                        <a class="category-link bg-white rounded-3 px-3 py-3 d-flex flex-column h-100 text-decoration-none border" href="/vendors/{{ $category->slug }}" target="_blank">
                            {{-- All images and icons removed --}}
                            {{-- <img src="{{ url('storage/' . $category->image) }}" alt="" width="50"> --}}

                            <div class="category-content text-center">
                                <h4 class="mb-2 text-dark fw-semibold" style="font-size: 16px; line-height: 1.4;">{{$category->name}}</h4>
                                <p class="mb-0 text-muted small">{{$category->vendor_count}} {{ $category->vendor_count == 1 ? 'Service' : 'Services' }}</p>
                            </div>
                        </a>
                    </div>
                </div>
                @endforeach
		</div>
        <div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-5">
				<a href="/find_homeservices" class="btn btn-primary px-lg-5 rounded" target="_blank">Explore More Home Services</a>
			</div>
		</div>

	</div>
</section>
<!-- ============================ Home Services End ================================== -->
<div class="clearfix"></div>

<!-- ================= Latest Jobs ================= -->
<!-- ============================ Supplier Categories Start ================================== -->
<section class="pt-4 pb-4">
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-xl-6 col-lg-7 col-md-10">
				<div class="sec-heading text-center">
					<h2>Real Estate Suppliers/Vendors</h2>
					<p>Looking for reliable suppliers and vendors? Find verified suppliers for construction materials, equipment, furniture, and more. Quality products and services for all your business and personal needs.</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center gx-3 gy-3">

			<!-- supplier-categories - simplified text only -->
            @foreach ($data['supplier_categories'] as $category)
            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                <div class="category-item h-100">
                    <a class="category-link bg-white rounded-3 px-3 py-3 d-flex flex-column h-100 text-decoration-none border" href="/suppliers/{{ $category->slug }}">
                        {{-- All images and icons removed --}}
                        {{-- <img src="{{ url('storage/' . $category->image) }}" alt="{{ $category->name }}" width="50"> --}}

                        <div class="category-content text-center">
                            <h4 class="mb-2 text-dark fw-semibold" style="font-size: 16px; line-height: 1.4;">{{$category->name}}</h4>
                            <p class="mb-0 text-muted small">{{$category->suppliers_count}} {{ $category->suppliers_count == 1 ? 'Supplier' : 'Suppliers' }}</p>
                        </div>
                    </a>
                </div>
            </div>
            @endforeach
		</div>
    <div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-5">
				<a href="/find_suppliers" class="btn btn-primary px-lg-5 rounded" target="_blank">Explore More Suppliers</a>
			</div>
		</div>

	</div>
</section>
<!-- ============================ Supplier Categories End ================================== -->
<!-- ============================ Long Ad Start ================================== -->
<section class="bg-primary call-to-act-wrap pt-4 pb-4">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">
                <a href="{{ $data['ads'][7]->link }}" target="_blank">
                    <img src="{{ url('storage/' . $data['ads'][7]->image) }}"  width="100%" alt="">
                </a>
            </div>
        </div>

	</div>
</section>
<!-- ============================ Long Ad End ================================== -->
<div class="clearfix"></div>
<!-- ============================ Latest Jobs Start ================================== -->
<section class="pt-4 pb-4">
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-xl-6 col-lg-7 col-md-10 text-center">
				<div class="sec-heading center">
					<h2>Real Estate Jobs</h2>
					<p>Discover exciting career opportunities with top companies. Find your dream job and take the next step in your professional journey.</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center g-4">

            @foreach ($data['jobs'] as $job)
                <div class="col-xl-4 col-lg-4 col-md-6 col-sm-12">
                <div class="property-listing card border rounded-3">

                    <div class="listing-img-wrapper p-3">
                        <div class="list-img-slide position-relative">
                            <div class="position-absolute top-0 left-0 ms-3 mt-3 z-1">
                                @if($job->verified == 1)
                                <div class="label bg-success text-light d-inline-flex align-items-center justify-content-center">
                                    <span class="svg-icon text-light svg-icon-2hx me-1">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                                            <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                                        </svg>
                                    </span>Verified
                                </div>
                                @endif
                            </div>
                            <div class="click rounded-3 overflow-hidden mb-0 bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <div class="text-center p-4">
                                    <i class="fa-solid fa-briefcase text-primary" style="font-size: 3rem;"></i>
                                    <h5 class="mt-3 text-primary">{{ $job->company_name }}</h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="listing-caption-wrapper px-3">
                        <div class="listing-detail-wrapper">
                            <div class="listing-short-detail-wrap">
                                <div class="listing-short-detail">
                                    <div class="d-flex align-items-center">
                                        <span class="label bg-light-info text-info prt-type me-2">{{ $job->job_role }}</span>
                                    </div>
                                    <h4 class="listing-name fw-semibold fs-5 mb-2 mt-3"><a href="/job-details/{{ $job->id }}">{{ $job->job_title }}</a></h4>
                                    <div class="prt-location text-muted-2">
                                        <span class="svg-icon svg-icon-2hx">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M18.0624 15.3453L13.1624 20.7453C12.5624 21.4453 11.5624 21.4453 10.9624 20.7453L6.06242 15.3453C4.56242 13.6453 3.76242 11.4453 4.06242 8.94534C4.56242 5.34534 7.46242 2.44534 11.0624 2.04534C15.8624 1.54534 19.9624 5.24534 19.9624 9.94534C20.0624 12.0453 19.2624 13.9453 18.0624 15.3453Z" fill="currentColor"/>
                                                <path d="M12.0624 13.0453C13.7193 13.0453 15.0624 11.7022 15.0624 10.0453C15.0624 8.38849 13.7193 7.04535 12.0624 7.04535C10.4056 7.04535 9.06241 8.38849 9.06241 10.0453C9.06241 11.7022 10.4056 13.0453 12.0624 13.0453Z" fill="currentColor"/>
                                            </svg>
                                        </span>
                                        {{ $job->area->name ?? 'N/A' }}, {{ $job->city->name ?? 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="price-features-wrapper">
                            <div class="list-fx-features d-flex align-items-center justify-content-between">
                                <div class="listing-card d-flex align-items-center">
                                    <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-clock fs-xs"></i></div><span class="text-muted-2 fs-sm">{{ $job->experience }}</span>
                                </div>
                                <div class="listing-card d-flex align-items-center">
                                    <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-users fs-xs"></i></div><span class="text-muted-2 fs-sm">{{ $job->no_of_positions }} Positions</span>
                                </div>
                                <div class="listing-card d-flex align-items-center">
                                    <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-graduation-cap fs-xs"></i></div><span class="text-muted-2 fs-sm">{{ Str::limit($job->qualification, 10) }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="listing-detail-footer d-flex align-items-center justify-content-between py-4">
                            <div class="listing-short-detail-flex">
                                <h6 class="listing-card-info-price m-0">
                                    ₹{{ $job->salary }}
                                </h6>
                            </div>
                            <div class="footer-flex">
                                <a href="/job-details/{{ $job->id }}" class="prt-view">
                                    <span class="svg-icon text-primary svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M15.43 8.56949L10.744 15.1395C10.6422 15.282 10.5804 15.4492 10.5651 15.6236C10.5498 15.7981 10.5815 15.9734 10.657 16.1315L13.194 21.4425C13.2737 21.6097 13.3991 21.751 13.5557 21.8499C13.7123 21.9488 13.8938 22.0014 14.079 22.0015H14.117C14.3087 21.9941 14.4941 21.9307 14.6502 21.8191C14.8062 21.7075 14.9261 21.5526 14.995 21.3735L21.933 3.33649C22.0011 3.15918 22.0164 2.96594 21.977 2.78013C21.9376 2.59432 21.8452 2.4239 21.711 2.28949L15.43 8.56949Z" fill="currentColor"/>
                                            <path opacity="0.3" d="M20.664 2.06648L2.62602 9.00148C2.44768 9.07085 2.29348 9.19082 2.1824 9.34663C2.07131 9.50244 2.00818 9.68731 2.00074 9.87853C1.99331 10.0697 2.04189 10.259 2.14054 10.4229C2.23919 10.5869 2.38359 10.7185 2.55601 10.8015L7.86601 13.3365C8.02383 13.4126 8.19925 13.4448 8.37382 13.4297C8.54839 13.4145 8.71565 13.3526 8.85801 13.2505L15.43 8.56548L21.711 2.28448C21.5762 2.15096 21.4055 2.05932 21.2198 2.02064C21.034 1.98196 20.8409 1.99788 20.664 2.06648Z" fill="currentColor"/>
                                        </svg>
                                    </span>
                                </a>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
            @endforeach

            </div>

		</div>

		<div class="row align-items-center justify-content-center">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-5">
				<a href="/jobs" class="btn btn-primary px-md-5 rounded" target="_blank">Browse More Real Estate Jobs</a>
			</div>
		</div>

	</div>
</section>
<!-- ================================= Latest Jobs End =============================== -->
<div class="clearfix"></div>



<!-- ============================ All Property ================================== -->
{{-- <section>
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-lg-7 col-md-10 text-center">
				<div class="sec-heading center">
					<h2>Featured Property For Sale</h2>
					<p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores</p>
				</div>
			</div>
		</div>

		<div class="row list-layout">

			<!-- featured-propertys1 code  -->
			<?php
                $propertys = [
                    [
                        'id' => 1,
                        'img' => '/img/p-1.jpg',
                        'tag' => 'For Sale',
                        'title' => 'Adobe Property Advisors',
                        'reviews' => '(42 Reviews)',
                        'price' => '$120M',
                        'location' => 'Quice Market, Canada',
                    ],
                    [
                        'id' => 2,
                        'img' => '/img/p-2.jpg',
                        'tag' => 'For Sale',
                        'title' => 'Agile Real Estate Group',
                        'reviews' => '(34 Reviews)',
                        'price' => '$132M',
                        'location' => 'Quice Market, Canada',
                    ],
                    [
                        'id' => 3,
                        'img' => '/img/p-3.jpg',
                        'tag' => 'For Sale',
                        'title' => 'Bluebell Real Estate',
                        'reviews' => '(124 Reviews)',
                        'price' => '$127M',
                        'location' => 'Quice Market, Canada',
                    ],
                    [
                        'id' => 4,
                        'img' => '/img/p-4.jpg',
                        'tag' => 'For Sale',
                        'title' => 'Strive Partners Realty',
                        'reviews' => '(56 Reviews)',
                        'price' => '$132M',
                        'location' => 'Quice Market, Canada',
                    ]
                ];
                ?>

                <?php foreach ($propertys as $item): ?>
                <!-- Single Property Start -->
                <div class="col-xl-6 col-lg-6 col-md-12">
                    <div class="property-listing property-1 border bg-white p-2 rounded">

                        <div class="listing-img-wrapper">
                            <a href="single-property-2.php?title=<?php echo urlencode(str_replace(' ', '-', strtolower($item['title']))); ?>">
                                <img src="<?php echo '/frontend/assets/', $item['img']; ?>" class="img-fluid mx-auto rounded" alt="" />
                            </a>
                        </div>

                        <div class="listing-content">

                            <div class="listing-detail-wrapper-box">
                                <div class="listing-detail-wrapper d-flex align-items-center justify-content-between">
                                    <div class="listing-short-detail">
                                        <span class="label bg-light-danger text-danger d-inline-flex mb-1"><?php echo $item['tag']; ?></span>
                                        <h4 class="listing-name mb-1 mt-2"><a href="single-property-2.php?title=<?php echo urlencode(str_replace(' ', '-', strtolower($item['title']))); ?>"><?php echo $item['title']; ?></a></h4>
                                        <div class="fr-can-rating">
                                            <i class="fas fa-star fs-xs filled"></i>
                                            <i class="fas fa-star fs-xs filled"></i>
                                            <i class="fas fa-star fs-xs filled"></i>
                                            <i class="fas fa-star fs-xs filled"></i>
                                            <i class="fas fa-star fs-xs"></i>
                                            <span class="reviews_text fs-sm text-muted ms-2"><?php echo $item['reviews']; ?></span>
                                        </div>

                                    </div>
                                    <div class="list-price">
                                        <h6 class="listing-card-info-price text-primary"><?php echo $item['price']; ?></h6>
                                    </div>
                                </div>
                            </div>

                            <div class="price-features-wrapper">
                                <div class="list-fx-features d-flex align-items-center justify-content-between mt-3 mb-1">
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-building-shield fs-xs"></i></div><span class="text-muted-2 fs-sm">3BHK</span>
                                    </div>
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-bed fs-xs"></i></div><span class="text-muted-2 fs-sm">3 Beds</span>
                                    </div>
                                    <div class="listing-card d-flex align-items-center">
                                        <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-clone fs-xs"></i></div><span class="text-muted-2 fs-sm">1800 SQFT</span>
                                    </div>
                                </div>
                            </div>

                            <div class="listing-footer-wrapper">
                                <div class="listing-locate">
                                    <span class="listing-location text-muted-2"><i class="fa-solid fa-location-pin me-1"></i><?php echo $item['location']; ?></span>
                                </div>
                                <div class="listing-detail-btn">
                                    <a href="single-property-2.php?title=<?php echo urlencode(str_replace(' ', '-', strtolower($item['title']))); ?>" class="btn btn-sm px-4 fw-medium btn-primary">View</a>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
                <!-- Single Property End -->
                <?php endforeach; ?>

		</div>

		<!-- Pagination -->
		<div class="row">
			<div class="col-lg-12 col-md-12 col-sm-12 text-center mt-4">
				<a href="listings-list-with-sidebar.php" class="btn btn-primary px-lg-5 rounded">Browse More Properties</a>
			</div>
		</div>

	</div>
</section> --}}
<!-- ============================ All Featured Property ================================== -->

<!-- ============================ Smart Testimonials ================================== -->
{{-- <section class="gray-bg">
	<div class="container">

		<div class="row justify-content-center">
			<div class="col-lg-7 col-md-10 text-center">
				<div class="sec-heading center">
					<h2>Good Reviews by Customers</h2>
					<p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores</p>
				</div>
			</div>
		</div>

		<div class="row justify-content-center">

			<div class="col-lg-12 col-md-12">

				<div class="smart-textimonials smart-center" id="smart-textimonials">

					<!-- reviews code  -->
					<?php
                        $reviews = [
                            [
                                'img' => '/img/user-3.jpg',
                                'name' => "Adam Williams",
                                'title' => "CEO Of Microwoft",
                                'desc' => "Cicero famously orated against his political opponent Lucius Sergius Catilina. Occasionally the first Oration against Catiline is taken specimens.",
                                'style' => "quotes bg-primary",
                            ],
                            [
                                'img' => '/img/user-8.jpg',
                                'name' => "Retha Deowalim",
                                'title' => "CEO Of Apple",
                                'desc' => "Cicero famously orated against his political opponent Lucius Sergius Catilina. Occasionally the first Oration against Catiline is taken specimens.",
                                'style' => "quotes bg-success",
                            ],
                            [
                                'img' => '/img/user-4.jpg',
                                'name' => "Sam J. Wasim",
                                'title' => "Pio Founder",
                                'desc' => "Cicero famously orated against his political opponent Lucius Sergius Catilina. Occasionally the first Oration against Catiline is taken specimens.",
                                'style' => "quotes bg-purple",
                            ],
                            [
                                'img' => '/img/user-5.jpg',
                                'name' => "Usan Gulwarm",
                                'title' => "CEO Of Facewarm",
                                'desc' => "Cicero famously orated against his political opponent Lucius Sergius Catilina. Occasionally the first Oration against Catiline is taken specimens.",
                                'style' => "quotes bg-seegreen",
                            ],
                            [
                                'img' => '/img/user-6.jpg',
                                'name' => "Shilpa Shethy",
                                'title' => "CEO Of Zapple",
                                'desc' => "Cicero famously orated against his political opponent Lucius Sergius Catilina. Occasionally the first Oration against Catiline is taken specimens.",
                                'style' => "quotes bg-danger",
                            ],
                        ];
                        ?>

                        <?php foreach ($reviews as $item): ?>
                        <!-- Single Item -->
                        <div class="item">
                            <div class="item-box">
                                <div class="smart-tes-author">
                                    <div class="st-author-box">
                                        <div class="st-author-thumb">
                                            <div class="<?php echo $item['style']; ?>"><i class="fa-solid fa-quote-left"></i></div>
                                            <img src="<?php echo '/frontend/assets/', $item['img']; ?>" class="img-fluid" alt="" />
                                        </div>
                                    </div>
                                </div>

                                <div class="smart-tes-content">
                                    <p><?php echo $item['desc']; ?></p>
                                </div>

                                <div class="st-author-info">
                                    <h4 class="st-author-title"><?php echo $item['name']; ?></h4>
                                    <span class="st-author-subtitle"><?php echo $item['title']; ?></span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>

				</div>
			</div>

		</div>

	</div>
</section>
<!-- ============================ Smart Testimonials End ================================== --> --}}

<!-- ========================== Download App Section =============================== -->
{{--  --}}
<!-- ========================== Download App Section =============================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap pt-4 pb-4">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="#" class="btn btn-call-to-act">SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->

<style>
/* Simplified Category Cards - Text Only (Home Page) */
.category-item {
    margin-bottom: 16px;
}

.category-link {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
    border-color: #007bff !important;
    background-color: #f8f9ff !important;
}

.category-content {
    width: 100%;
}

.category-content h4 {
    margin-bottom: 8px;
    color: #333;
    transition: color 0.3s ease;
}

.category-link:hover .category-content h4 {
    color: #007bff;
}

.category-content p {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .category-link {
        min-height: 70px;
        padding: 15px 20px !important;
    }

    .category-content h4 {
        font-size: 15px !important;
    }

    .category-content p {
        font-size: 12px !important;
    }
}

@media (max-width: 576px) {
    .category-link {
        min-height: 60px;
        padding: 12px 15px !important;
    }

    .category-content h4 {
        font-size: 14px !important;
        line-height: 1.3 !important;
    }

    .category-content p {
        font-size: 11px !important;
    }
}

/* Grid Layout */
.row.justify-content-center.gx-3.gy-3 {
    margin: 0 -8px;
}

.row.justify-content-center.gx-3.gy-3 > [class*="col-"] {
    padding: 0 8px;
}

/* Focus States for Accessibility */
.category-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Loading State */
.category-link.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>

@stop
