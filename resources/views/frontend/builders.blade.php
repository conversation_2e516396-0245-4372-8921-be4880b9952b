@extends('frontend.layouts.master')
@section('content')

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Explore Trusted Vendors Across All Service Categories</h2>
				<span class="ipn-subtitle">Browse a wide range of service categories and find top-rated vendors for every need—plumbing, carpentry, electrical, cleaning, and more. View profiles, compare ratings, and choose the right professional for your task.</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">

	<div class="container">

		<div class="row">
			<div class="col-lg-12 col-md-12">
				<div class="filter_search_opt">
					<a href="javascript:void(0);" class="btn btn-dark full-width mb-4" onclick="openFilterSearch()">
						<span class="svg-icon text-light svg-icon-2hx me-2">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z" fill="currentColor"/>
							</svg>
						</span>Open Filter Option
					</a>
				</div>
			</div>
		</div>

		<div class="row">

			<!-- property-sidebar code  -->
			<!-- property Sidebar -->


			<div class="col-lg-12 col-md-12 list-layout">



				<div class="row justify-content-center gx-3 gy-3">

                    <!-- property-type code  -->
                       @foreach ($builders as $builder)
                    <!-- Single Agent -->
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                        <div class="agents-grid card rounded-3 shadow">
                            <div class="agents-grid-wrap">
                                <div class="fr-grid-thumb mx-auto text-center mt-5 mb-3">
                                    <a href="/properties?searchbuilder={{$builder->id}}" class="d-inline-flex p-1 circle border">
                                        <img src="{{ url('storage/' . $builder->image) }}" class="img-fluid circle" width="130" alt="" />
                                    </a>
                                </div>
                                <div class="fr-grid-deatil text-center mb-5">
                                    <div class="fr-grid-deatil-flex">
                                        <h5 class="fr-can-name mb-0"><a href="/properties?searchbuilder={{$builder->id}}">{{$builder->name}}</a></h5>
                                        <span class="agent-property text-muted-2">{{$builder->propeties_count}} Properties</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach

                </div>

				<!-- Pagination -->


			</div>

		</div>
	</div>
</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="#" class="btn btn-call-to-act">SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->
@stop
