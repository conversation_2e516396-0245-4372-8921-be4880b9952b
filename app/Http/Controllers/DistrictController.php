<?php

namespace App\Http\Controllers;

use App\Models\District;
use App\Models\State;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class DistrictController extends Controller
{

    //
    public function index()
    {
        return view('admin.districts.list');
    }
    public function add()
    {
        return view('admin.districts.add');
    }
    //
    // Store method to save district data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'state_id'        => 'required',
            'status'        => 'required',
        ]);
        $data = $request->all();

        // Save data to database
        District::create($data);

        // Redirect to district list page with success message
        return redirect()->route('districts.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $district = District::where('id', $id)->first();
        $states = State::where('status', 1)->get();


        return view('admin.districts.edit', compact('district','states'));
    }
    public function update(Request $request, District $district)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'state_id'        => 'required',
            'status'        => 'required',
        ]);

       // $data = $request->except(['image']); // Exclude logo from mass update initially

       $data = $request->all();
        // Handle file upload

        // Update district  data
        $district->update($data);

        // Redirect to district  list page with success message
        return redirect()->route('districts.index')->with('success', $district->name . ' updated successfully!');
    }

    public function getdistricts(Request $request)
    {
        //dd('h');
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();
            // Fetch base districts  query
            $districts = District::select(['id', 'name', 'state_id' , 'status', 'created_at']);



            return DataTables::of($districts)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Active</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Inactive</span>';
                    }
                })
                ->addColumn('state', function ($row) {
                    return $row->state->name;
                 })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'district-edit' permission
                    if ($user->can('district-edit')) {
                        $output .= '
                            <a href="' . route('districts.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }


                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ;
                        });
                    }
                })

                ->rawColumns(['status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }

}
