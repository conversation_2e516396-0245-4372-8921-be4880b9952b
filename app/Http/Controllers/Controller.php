<?php

namespace App\Http\Controllers;

use App\Models\Job;
use App\Models\Area;
use App\Models\District;
use App\Models\State;
use App\Models\Builder;
use App\Models\Property;
use App\Models\Supplier;
use App\Models\HomeService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    public function dashboard()
    {
        // Get counts for all entities
        $totalProperties = Property::count();
        $totalBuilders = Builder::count();
        $totalSuppliers = Supplier::count();
        $totalHomeServices = HomeService::count();
        $totalJobs = Job::count();

        // Get verified counts
        $verifiedProperties = Property::where('verified', 1)->count();
        $verifiedSuppliers = Supplier::where('verified', 1)->count();
        $verifiedHomeServices = HomeService::where('verified', 1)->count();
        $verifiedJobs = Job::where('verified', 1)->count();

        // Get recent additions (last 30 days)
        $recentProperties = Property::where('created_at', '>=', now()->subDays(30))->count();
        $recentBuilders = Builder::where('created_at', '>=', now()->subDays(30))->count();
        $recentSuppliers = Supplier::where('created_at', '>=', now()->subDays(30))->count();
        $recentHomeServices = HomeService::where('created_at', '>=', now()->subDays(30))->count();
        $recentJobs = Job::where('created_at', '>=', now()->subDays(30))->count();

        // Get property type breakdown
        $sellProperties = Property::where('property_type', 1)->count();
        $rentProperties = Property::where('property_type', 2)->count();

        // Get building type breakdown
        $buildingTypes = [
            'Apartment' => Property::where('building_type_id', 1)->count(),
            'Individual' => Property::where('building_type_id', 2)->count(),
            'Villa' => Property::where('building_type_id', 3)->count(),
            'Community' => Property::where('building_type_id', 4)->count(),
            'Other' => Property::where('building_type_id', 5)->count(),
        ];

        // Get location statistics
        $totalStates = State::where('status', 1)->count();
        $totalCities = District::where('status', 1)->count();
        $totalAreas = Area::where('status', 1)->count();

        // Get monthly data for charts (last 12 months)
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthlyData[] = [
                'month' => $month->format('M Y'),
                'properties' => Property::whereYear('created_at', $month->year)
                                      ->whereMonth('created_at', $month->month)
                                      ->count(),
                'suppliers' => Supplier::whereYear('created_at', $month->year)
                                     ->whereMonth('created_at', $month->month)
                                     ->count(),
                'homeservices' => HomeService::whereYear('created_at', $month->year)
                                            ->whereMonth('created_at', $month->month)
                                            ->count(),
                'jobs' => Job::whereYear('created_at', $month->year)
                            ->whereMonth('created_at', $month->month)
                            ->count(),
            ];
        }

        return view('admin.dashboard', compact(
            'totalProperties', 'totalBuilders', 'totalSuppliers', 'totalHomeServices', 'totalJobs',
            'verifiedProperties', 'verifiedSuppliers', 'verifiedHomeServices', 'verifiedJobs',
            'recentProperties', 'recentBuilders', 'recentSuppliers', 'recentHomeServices', 'recentJobs',
            'sellProperties', 'rentProperties', 'buildingTypes',
            'totalStates', 'totalCities', 'totalAreas', 'monthlyData'
        ));
    }
}
