<!-- <PERSON><PERSON> -->
<div id="cookie-consent-banner" class="cookie-consent-banner" style="display: none;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-9 col-sm-12">
                <p class="cookie-consent-text mb-0">
                    By using this site, you agree with our
                    <a href="/maintermsandconditions" class="cookie-privacy-link">Terms & Conditions</a>
                    and
                    <a href="/privacypolicy" class="cookie-privacy-link">Privacy Policy</a>
                </p>
            </div>
            <div class="col-md-3 col-sm-12 text-end">
                <button type="button" class="btn btn-light btn-sm" id="accept-cookies-btn">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.cookie-consent-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #1a365d;
    color: white;
    padding: 10px 0;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    font-size: 13px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.cookie-consent-text {
    line-height: 1.4;
}

.cookie-privacy-link {
    color: #87ceeb;
    text-decoration: underline;
}

.cookie-privacy-link:hover {
    color: #add8e6;
    text-decoration: underline;
}

.cookie-consent-banner .btn {
    font-size: 12px;
    padding: 5px 14px;
    border-radius: 3px;
    font-weight: 500;
}

.cookie-consent-banner .btn-outline-light {
    border-color: rgba(255,255,255,0.6);
    color: white;
    background-color: transparent;
}

.cookie-consent-banner .btn-outline-light:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: white;
    color: white;
}

.cookie-consent-banner .btn-light {
    background-color: white;
    color: #1a365d;
    border: 1px solid white;
}

.cookie-consent-banner .btn-light:hover {
    background-color: #f8f9fa;
    color: #1a365d;
    border-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cookie-consent-banner .col-sm-12 {
        text-align: center !important;
        margin-bottom: 10px;
    }

    .cookie-consent-banner .col-sm-12:last-child {
        margin-bottom: 0;
    }

    .cookie-consent-banner {
        padding: 15px 0;
    }

    .cookie-consent-banner .text-end {
        text-align: center !important;
    }
}

/* Adjust body padding when banner is shown */
body.cookie-banner-shown {
    padding-top: 50px;
}

@media (max-width: 768px) {
    body.cookie-banner-shown {
        padding-top: 90px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if user has already accepted cookies
    if (!getCookie('cookie_consent_accepted')) {
        showCookieBanner();
    }

    // Accept cookies button
    document.getElementById('accept-cookies-btn').addEventListener('click', function() {
        acceptCookies();
    });

    function showCookieBanner() {
        var banner = document.getElementById('cookie-consent-banner');
        banner.style.display = 'block';
        banner.style.opacity = '0';
        banner.style.transition = 'opacity 0.3s ease-in-out';

        setTimeout(function() {
            banner.style.opacity = '1';
        }, 100);

        document.body.classList.add('cookie-banner-shown');
    }

    function hideCookieBanner() {
        var banner = document.getElementById('cookie-consent-banner');
        banner.style.opacity = '0';

        setTimeout(function() {
            banner.style.display = 'none';
            document.body.classList.remove('cookie-banner-shown');
        }, 300);
    }

    function acceptCookies() {
        // Set cookie for 1 year
        setCookie('cookie_consent_accepted', 'true', 365);
        hideCookieBanner();
    }

    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
});
</script>
