@extends('admin.layouts.master')
@section('content')
    <div class="dashboard-main-body">
        @if (session('success'))
            <div id="snackbar"
                class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
                <div class="d-flex align-items-center gap-2">
                    <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
                    <span id="snackbar-message"> {{ session('success') }}</span>

                </div>
                <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
                    <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
                </button>
            </div>
        @endif
        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Role Permissions</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Role Permissions</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
                <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                    <div class="icon-field w-100">
                        {{-- <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Client" id="searchkey">
                        <span class="icon">
                            <iconify-icon icon="ion:search-outline"></iconify-icon>
                        </span> --}}
                     <h6> {{$role->name}} Permisssions  </h6>
                    </div>
                </div>
                <div class="d-flex flex-no-wrap align-items-center gap-3">




                </div>
            </div>
            <div class="card-body">
                <form action="{{ route('roles.assign-permissions') }}" method="POST">
                    @csrf
                    <input type="hidden" name="role_id" value="{{ $role->id }}">
                    <div class="row">
                        @foreach ($permissions as $permission)
                            <div class="col-md-3 mb-3">
                                <div class="bg-success-100 px-20 py-12 radius-8">
                                    <span class="form-check checked-success d-flex align-items-center gap-2">
                                        <input class="form-check-input" type="checkbox" name="permissions[]"
                                            id="{{ $permission->id }}" value="{{ $permission->name }}"
                                            {{ in_array($permission->name, $rolePermissions) ? 'checked' : '' }}>
                                        <label class="form-check-label line-height-1 fw-medium text-secondary-light"
                                            for="{{ $permission->id }}"> {{ $permission->name }}</label>
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    <div class="row">
                        <button type="submit" class="form-wizard-next-btn btn btn-primary-600 px-32">Update {{$role->name}} Permissions</button>

                    </div>
                </form>
            </div>
        </div>

    </div>
@stop
