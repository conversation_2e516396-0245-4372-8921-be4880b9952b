@extends('admin.layouts.master')
@section('title', 'Clients Edit- Paidash')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Edit Area</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="{{ route('areas.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Area
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Edit Area</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to area </h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard">
                    <form action="{{ route('areas.update', $area->id) }}" method="POST" id="form"
                        enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <fieldset class="wizard-fieldset show">
                            <div class="row gy-3">

                                <div class="col-sm-6">
                                    <label class="form-label">State<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="state_id">
                                        @foreach ($states as $state)
                                            <option value="{{ $state->id }}"
                                                {{ old('state_id', $area->state_id) == $state->id ? 'selected' : '' }}>
                                                {{ $state->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">District<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="district_id" id="district_id">
                                        @foreach ($districts as $district)
                                            <option value="{{ $district->id }}"
                                                {{ old('district_id', $area->district_id) == $district->id ? 'selected' : '' }}>
                                                {{ $district->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Area Name<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="name"
                                        placeholder="Enter Area Name" value="{{ old('name', $area->name) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Pincode</label>
                                    <input type="text" class="form-control" name="pincode"
                                        placeholder="Enter Pincode" value="{{ old('pincode', $area->pincode) }}">
                                </div>

                                <div class="col-sm-6">
                                    <label class="form-label">Status <span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" name="status">
                                        <option value="1" {{ $area->status == 1 ? 'selected' : '' }}>Active</option>
                                        <option value="0" {{ $area->status == 0 ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>

                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                    <a href="{{ route('areas.index') }}" class="btn btn-danger-500 border-neutral-100 px-32">Cancel</a>
                                    <button type="submit" class="btn btn-primary-600 px-32">Update</button>
                                </div>
                            </div>
                        </fieldset>
                    </form>

                </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>
@stop

@section('script')
    <script>
        $(document).ready(function() {
            // Initialize Select2 for State
            $('#state_id').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
             // Initialize Select2 for city
             $('select[name="city_id"]').select2({
                placeholder: 'Select City',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('select[name="state_id"]').val();
                        if (!stateId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a state first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "cities",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting city without state
            $('select[name="city_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });
            $('select[name="state_id"]').change(function() {
                $('#city_id').html('<option value="">Select City</option>').val(null).trigger(
                    'change.select2');
            });

            // Initialize jQuery validation
            $("form").validate({
                ignore: [], // Ensures Select2 fields are not ignored
                rules: {
                    name: {
                        required: true
                    }
                },
                messages: {
                    name: "Area name is required"

                },
                errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });


        });

         </script>
@stop
@section('css')
    <style>
        .error-border {
            border: 1px solid #dc3545 !important;
            /* Bootstrap red */
            border-radius: 5px;
        }
    </style>
@stop
