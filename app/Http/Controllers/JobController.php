<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;
use App\Models\State;
use App\Models\District;

class JobController extends Controller
{
    //
    public function index()
    {
        $states = State::where('status', 1)->orderBy('name')->get();
        // Don't load all districts at once - use AJAX loading instead
        $districts = collect(); // Empty collection
        // Don't load all areas at once - use AJAX loading instead
        $areas = collect(); // Empty collection
        return view('admin.jobs.list', compact('states', 'districts', 'areas'));
    }


    public function getjobs(Request $request)
    {
        if ($request->ajax()) {
            // Get the logged-in user
            $user = Auth::user();

            // Fetch job query
            $job = Job::select(['id', 'job_title', 'job_role', 'company_name','email', 'mobile', 'status', 'verified', 'created_at']);
//dd($job);
            // Apply filters if provided
            if ($request->has('searchtitle') && !empty($request->searchtitle)) {
                $job->where('job_title', 'like', '%' . $request->searchtitle . '%');
            }

            if ($request->has('searchjobrole') && !empty($request->searchjobrole)) {
                $job->where('job_role', $request->searchjobrole);
            }

            if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
                $job->where('verified', $request->searchVerified);
            }

            return DataTables::of($job)
                ->addColumn('action', function ($row) use ($user) {
                    $output = '';

                    // Check permissions and add action buttons
                    // if ($user->can('job-edit')) {
                    //     $output .= '
                    //         <a href="' . route('jobs.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                    //             <iconify-icon icon="lucide:edit"></iconify-icon>
                    //         </a>';
                    // }

                    if ($user->can('job-view')) {
                        $output .= '
                            <a href="' . route('jobs.show', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center ms-1">
                                <iconify-icon icon="lucide:eye"></iconify-icon>
                            </a>
                            <button class="btn btn-danger btn-sm delete-job" data-id="'. $row->id .'" title="Delete">
                                    <iconify-icon icon="lucide:trash-2"></iconify-icon>
                                </button>';
                    }

                    return $output;
                })
                ->rawColumns(['action'])
                ->make(true);
        }
    }
    public function  show($id)  {
        $job=Job::where('id',$id)->first();
        return view('admin.jobs.view',compact('job'));

    }

    /**
     * Update the status of a job.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'job_id' => 'required|exists:jobs,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the job
            $job = Job::findOrFail($request->job_id);

            // Update status
            $job->status = $request->status;
            $job->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Job status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update job status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the verification status of a job.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateVerificationStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'job_id' => 'required|exists:jobs,id',
            'verified' => 'required|in:0,1',
        ]);

        try {
            // Find the job
            $job = Job::findOrFail($request->job_id);

            // Update verification status
            $job->verified = $request->verified;
            $job->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Job verification status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update job verification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a job.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'job_id' => 'required|exists:jobs,id',
        ]);

        try {
            // Find the job
            $job = Job::findOrFail($request->job_id);

            // Delete the job
            $job->delete();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Job deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export jobs data to CSV based on filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        // Fetch jobs query with all relationships
        $jobs = Job::with([
            'area',
            'district',
            'state'
        ]);

        // Apply filters
        if ($request->has('searchtitle') && $request->searchtitle != '') {
            $jobs->where('job_title', 'like', "%{$request->searchtitle}%");
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $jobs->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $jobs->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $jobs->where('area_id', $request->searchArea);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
            $jobs->where('verified', $request->searchVerified);
        }

        // Get all jobs (no pagination)
        $jobs = $jobs->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Job Title',
            'Company Name',
            'Job Role',
            'Salary',
            'Experience',
            'Qualification',
            'Description',
            'No of Positions',
            'Area',
            'District',
            'State',
            'About Company',
            'Contact Email',
            'Contact Phone',
            'Status',
            'Verified',
            'Created At'
        ]);

        // Add data rows
        foreach ($jobs as $job) {
            $csv->insertOne([
                $job->job_title,
                $job->company_name,
                $job->job_role,
                $job->salary,
                $job->experience,
                $job->qualification,
                $job->job_description,
                $job->no_of_positions,
                $job->area->name ?? 'N/A',
                $job->district->name ?? 'N/A',
                $job->state->name ?? 'N/A',
                $job->about_company,
                $job->email,
                $job->mobile,
                $job->status ? 'Active' : 'Inactive',
                $job->verified ? 'Verified' : 'Not Verified',
                $job->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'Jobs_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }
}
