<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL to modify columns to allow NULL values
        DB::statement('ALTER TABLE properties MODIFY COLUMN no_of_bed_rooms INT NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN no_of_bath_rooms INT NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN no_of_balconies INT NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN no_of_car_parkings INT NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN no_of_floors INT NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN facing VARCHAR(255) NULL');
        DB::statement('ALTER TABLE properties MODIFY COLUMN build_year VARCHAR(255) NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            // Revert back to NOT NULL (only if you want to rollback)
            $table->integer('no_of_bed_rooms')->nullable(false)->change();
            $table->integer('no_of_bath_rooms')->nullable(false)->change();
            $table->integer('no_of_balconies')->nullable(false)->change();
            $table->integer('no_of_car_parkings')->nullable(false)->change();
            $table->integer('no_of_floors')->nullable(false)->change();
            $table->string('facing')->nullable(false)->change();
            $table->string('build_year')->nullable(false)->change();
        });
    }
};
