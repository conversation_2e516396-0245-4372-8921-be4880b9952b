@extends('admin.layouts.master')
@section('title', 'Builders')
@section('content')
<div class="dashboard-main-body">

    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">View Builder</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">
                <a href="{{ route('builders.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                    Builder
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">View Builder</li>
        </ul>
    </div>

   {{-- here database columns are name, phone, email, state, district, area, status, created_at, image --}}
   <div class="row mt-4 gy-4">
        <div class="col-md-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                    <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                    Builder
                </div>
                <div class="card-body bg-light">
                    <div class="d-flex align-items-center">
                        <img src="{{ asset('storage/'.$builder->image) }}" alt="{{ $builder->name }}" class="img-fluid rounded-circle me-3" width="50" height="50">
                        <div>
                            <h6 class="mb-0">{{ $builder->name }}</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                    <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                    Contact Information
                </div>
                <div class="card-body bg-light">
                    <ul class="list-unstyled lh-lg mb-0">
                        <li><strong>Phone:</strong> <span class="text-muted">{{ $builder->phone }}</span></li>
                        <li><strong>Email:</strong> <span class="text-muted">{{ $builder->email }}</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-4 gy-4">
        <div class="col-md-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                    <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                    Address
                </div>
                <div class="card-body bg-light">
                    <ul class="list-unstyled lh-lg mb-0">
                        <li><strong>State:</strong> <span class="text-muted">{{ @$builder->state->name }}</span></li>
                        <li><strong>District:</strong> <span class="text-muted">{{ @$builder->district->name }}</span></li>
                        <li><strong>Area:</strong> <span class="text-muted">{{ @$builder->area->name }}</span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm border-0 rounded-3">
                <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                    <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                    Status
                </div>
                <div class="card-body bg-light">
                    <ul class="list-unstyled lh-lg mb-0">
                        <li><strong>Status:</strong> <span class="text-muted">{{ $builder->status ? 'Active' : 'Inactive' }}</span></li>
                        <li><strong>Created At:</strong> <span class="text-muted">{{ $builder->created_at->format('d-m-Y') }}</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@stop
