<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\Builder;
use App\Models\Property;
use App\Models\State;
use App\Models\District;
use Illuminate\Http\Request;
use App\Models\PropertyImage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;

class PropertyController extends Controller
{
    //
    public function index()
    {
        $builders = Builder::where('status', 1)->orderBy('name')->get();
        $states = State::where('status', 1)->orderBy('name')->get();
        // Don't load all districts at once - use AJAX loading instead
        $districts = collect(); // Empty collection
        // Don't load all areas at once - use AJAX loading instead
        $areas = collect(); // Empty collection
        return view('admin.properties.list', compact('builders', 'states', 'districts', 'areas'));
    }


    public function getPropertiesData(Request $request)
    {
        if ($request->ajax()) {
            // Get the logged-in user
            $user = Auth::user();

            // Fetch properties query with all relationships
            $properties = Property::with(['state', 'district', 'area', 'builder', 'buildingType', 'areaType']);

            if ($request->has('searchtitle') && $request->searchtitle != '') {
                $properties->where('title', 'like', "%{$request->searchtitle}%");
            }

            if ($request->has('searchBuilder') && $request->searchBuilder != '') {
                $properties->where('builder_id', $request->searchBuilder);
            }

            if ($request->has('searchState') && $request->searchState != '') {
                $properties->where('state_id', $request->searchState);
            }

            if ($request->has('searchDistrict') && $request->searchDistrict != '') {
                $properties->where('district_id', $request->searchDistrict);
            }

            if ($request->has('searchArea') && $request->searchArea != '') {
                $properties->where('area_id', $request->searchArea);
            }

            if ($request->has('searchType') && $request->searchType != '') {
                $properties->where('property_type', $request->searchType);
            }

            if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
                $properties->where('verified', $request->searchVerified);
            }
        }

        return DataTables::of($properties)
            ->editColumn('property_type', function ($row) {
                    return $row->property_type == 1 ? 'Buy' : 'Rent';
                })
            ->editColumn('building_type_id', function ($row) {
                    return $row->buildingType->type ?? 'N/A';
                })
            ->editColumn('area_size', function ($row) {
                    return $row->area_size . ($row->areaType->name ?? 'SquareFeet');
                })
            ->addColumn('action', function ($row) use ($user) {
                $output = '';

                // Check permissions and add action buttons
                if ($user->can('property-edit')) {
                    $output .= '
                        <a href="' . route('properties.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                            <iconify-icon icon="lucide:edit"></iconify-icon>
                        </a>';
                }

                if ($user->can('property-view')) {
                    $output .= '
                        <a href="' . route('properties.show', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center ms-1">
                            <iconify-icon icon="lucide:eye"></iconify-icon>
                        </a>
                        <button class="btn btn-danger btn-sm delete-property" data-id="'. $row->id .'" title="Delete">
                                <iconify-icon icon="lucide:trash-2"></iconify-icon>
                            </button>';
                }

                return $output;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
    public function  show($id)  {
        $property=Property::with(['areaType'])->where('id',$id)->first();
        $property_images=PropertyImage::where('property_id',$property->id)->get();
        return view('admin.properties.view',compact('property','property_images'));

    }

    /**
     * Update the status of a property.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'property_id' => 'required|exists:properties,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the property
            $property = Property::findOrFail($request->property_id);

            // Update status
            $property->status = $request->status;
            $property->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Property status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the verification status of a property.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateVerificationStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'property_id' => 'required|exists:properties,id',
            'verified' => 'required|in:0,1',
        ]);

        try {
            // Find the property
            $property = Property::findOrFail($request->property_id);

            // Update verification status
            $property->verified = $request->verified;
            $property->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Property verification status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property verification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a property.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'property_id' => 'required|exists:properties,id',
        ]);

        try {
            // Find the property
            $property = Property::findOrFail($request->property_id);

            // Delete the property
            $property->delete();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Property deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export properties data to CSV based on filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        // Fetch properties query with all relationships
        $properties = Property::with([
            'builder',
            'area',
            'district',
            'state'
        ]);

        // Apply filters
        if ($request->has('searchtitle') && $request->searchtitle != '') {
            $properties->where('title', 'like', "%{$request->searchtitle}%");
        }

        if ($request->has('searchBuilder') && $request->searchBuilder != '') {
            $properties->where('builder_id', $request->searchBuilder);
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $properties->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $properties->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $properties->where('area_id', $request->searchArea);
        }

        if ($request->has('searchType') && $request->searchType != '') {
            $properties->where('property_type', $request->searchType);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
            $properties->where('verified', $request->searchVerified);
        }

        // Get all properties (no pagination)
        $properties = $properties->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Title',
            'Property Type',
            'Builder',
            'Advance Amount',
            'Rent',
            'Property Cost',
            'Building Type',
            'Area (sq ft)',
            'Bedrooms',
            'Bathrooms',
            'Balconies',
            'Car Parkings',
            'Floors',
            'Facing',
            'Build Year',
            'Address',
            'Area',
            'District',
            'State',
            'Pincode',
            'Amenities Parking',
            'Amenities Swimming Pool',
            'Amenities Lift',
            'Amenities Gym',
            'Amenities Spa',
            'Amenities Power Backup',
            'Amenities Water Supply',
            'Amenities Security',
            'Amenities CCTV',
            'Amenities Clubhouse',
            'Amenities Indoor Games',
            'Amenities Outdoor Games',
            'Amenities Kids Play',
            'Other Amenities',
            'Contact Name',
            'Contact Mobile',
            'Contact Email',
            'Status',
            'Verified',
            'Created At'
        ]);

        // Add data rows
        foreach ($properties as $property) {
            $csv->insertOne([
                    $property->title,
                    $property->property_type == 1 ? 'Buy' : 'Rent',
                    $property->builder->name ?? 'N/A',
                    $property->advance_amount,
                    $property->rent,
                    $property->property_cost,
                    $property->building_type_name,
                    $property->area_size.($property->areaType->name ?? 'SquareFeet'),
                    $property->no_of_bed_rooms,
                    $property->no_of_bath_rooms,
                    $property->no_of_balconies,
                    $property->no_of_car_parkings,
                    $property->no_of_floors,
                    $property->facing_name,
                    $property->build_year,
                    $property->address,
                    $property->area->name ?? 'N/A',
                    $property->district->name ?? 'N/A',
                    $property->state->name ?? 'N/A',
                    $property->pincode,
                    $property->amenities_parking? 'Yes' : 'No',
                    $property->amenities_swimming_pool? 'Yes' : 'No',
                    $property->amenities_lift? 'Yes' : 'No',
                    $property->amenities_gym? 'Yes' : 'No',
                    $property->amenities_spa? 'Yes' : 'No',
                    $property->amenities_power_backup? 'Yes' : 'No',
                    $property->amenities_water_supply? 'Yes' : 'No',
                    $property->amenities_security? 'Yes' : 'No',
                    $property->amenities_cctv? 'Yes' : 'No',
                    $property->amenities_clubhouse? 'Yes' : 'No',
                    $property->amenities_indoor_games? 'Yes' : 'No',
                    $property->amenities_outdoor_games? 'Yes' : 'No',
                    $property->amenities_kids_play? 'Yes' : 'No',
                    $property->other_amenities ?? 'N/A',
                    $property->contact_person,
                    $property->contact_mobile,
                    $property->contact_email,
                    $property->status ? 'Active' : 'Inactive',
                    $property->verified ? 'Verified' : 'Not Verified',
                    $property->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'Properties_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }
}
