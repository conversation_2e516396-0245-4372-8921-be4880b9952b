@extends('admin.layouts.master')
@section('title', 'Home Services')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Home Services List</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Home Services List</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Home Service" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">
                <select id="searchState" class="form-control form-select" title="select State">
                    <option selected value="">Select State</option>
                    <option value="">All</option>
                    @foreach ($states as $state)
                         <option value="{{$state->id}}">{{$state->name}}</option>
                    @endforeach
                </select>
                <select id="searchDistrict" class="form-control form-select" title="select District">
                    <option selected value="">Select District</option>
                    <option value="">All</option>
                    {{-- Districts will be loaded via AJAX based on selected state --}}
                </select>
                <select id="searchArea" class="form-control form-select" title="select Area">
                    <option selected value="">Select Area</option>
                    <option value="">All</option>
                    {{-- Areas will be loaded via AJAX based on selected district --}}
                </select>
                <select id="searchCategory" class="form-control form-select" title="select Category">
                    <option selected value="">Select Category</option>
                    <option value="">All</option>
                    @foreach ($categories as $category)
                         <option value="{{$category->id}}">{{$category->name}}</option>
                    @endforeach
                </select>
                <select id="searchVerified" class="form-control form-select" title="select Verification Status">
                    <option selected value="">All Verification</option>
                    <option value="1">Verified</option>
                    <option value="0">Not Verified</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="homeservicesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Catgrory</th>
                            <th>Home Service</th>
                            <th>Area</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Verified</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>

        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background: rgba(255,255,255,0.8); padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
    <div class="d-flex align-items-center">
        <span class="spinner-border text-primary me-2" role="status"></span>
        <strong>Exporting data, please wait...</strong>
    </div>
</div>

@stop
@section('script')
<script>
    $(document).ready(function () {
        $('#searchState, #searchDistrict, #searchArea, #searchCategory, #searchVerified').select2({
            width: '200px'
        });

        var table = $('#homeservicesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('homeservices.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val() || '';
                    d.searchCategory = $('#searchCategory').val() || '';
                    d.searchArea = $('#searchArea').val() || '';
                    d.searchDistrict = $('#searchDistrict').val() || '';
                    d.searchState = $('#searchState').val() || '';
                    d.searchVerified = $('#searchVerified').val() || '';
                },
                error: function(xhr, error, code) {
                    console.log('DataTable AJAX Error:', xhr, error, code);
                }
            },
            columns: [
                { data: 'category', name: 'category' },
                { data: 'business_title', name: 'business_title' },
                { data: 'area', name: 'area' },
                { data: 'phone', name: 'phone' },
                {
                    data: 'status',
                    name: 'status',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        if (type === 'display') {
                            // Convert to number to ensure proper comparison
                            let status = parseInt(data);
                            let checked = status === 1 ? 'checked' : '';
                            let statusText = status === 1 ? 'Active' : 'Inactive';

                            return `<div class="form-switch switch-success d-flex align-items-center gap-2">
                                <input class="form-check-input status-toggle" type="checkbox" role="switch"
                                    data-id="${row.id}" ${checked}>
                                <label class="form-check-label fw-medium text-secondary-light mb-0">
                                    ${statusText}
                                </label>
                            </div>`;
                        }
                        return data;
                    }
                },
                {
                    data: 'verified',
                    name: 'verified',
                    orderable: false,
                    searchable: false,
                    render: function(data, type, row) {
                        if (type === 'display') {
                            let verified = parseInt(data);
                            if (verified === 1) {
                                return `<span class="badge bg-success d-flex align-items-center justify-content-center" style="width: 100px;">
                                    <iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>
                                    Verified
                                </span>`;
                            } else {
                                return `<span class="badge bg-warning d-flex align-items-center justify-content-center" style="width: 100px;">
                                    <iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>
                                    Not Verified
                                </span>`;
                            }
                        }
                        return data;
                    }
                },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();

                // Re-initialize any plugins or event handlers after table redraw
                // This ensures the status toggles work after pagination, sorting, etc.
                $('.status-toggle').each(function() {
                    $(this).prop('disabled', false);
                });
            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            buttons: [
                {
                    text: 'Export CSV',
                    className: 'btn btn-success btn-sm bg-success-500 d-inline-flex align-items-center gap-1',
                    init: function (api, node, config) {
                        $(node).append('<iconify-icon icon="hugeicons:csv-02"></iconify-icon>');
                    },
                    action: function (e, dt, node, config) {
                        // Show loader
                        $('#exportLoader').show();

                        // Get all filter values
                        const searchkey = $("#searchkey").val();
                        const searchCategory = $("#searchCategory").val();
                        const searchArea = $("#searchArea").val();
                        const searchDistrict = $("#searchDistrict").val();
                        const searchState = $("#searchState").val();
                        const searchVerified = $("#searchVerified").val();

                        // Create form data
                        const formData = new FormData();
                        if (searchkey) formData.append('searchkey', searchkey);
                        if (searchCategory) formData.append('searchCategory', searchCategory);
                        if (searchArea) formData.append('searchArea', searchArea);
                        if (searchDistrict) formData.append('searchDistrict', searchDistrict);
                        if (searchState) formData.append('searchState', searchState);
                        if (searchVerified) formData.append('searchVerified', searchVerified);
                        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                        // Make AJAX request to export endpoint
                        $.ajax({
                            url: "{{ route('homeservices.export') }}",
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            xhrFields: {
                                responseType: 'blob' // Important for handling binary data
                            },
                            success: function(response, status, xhr) {
                                // Hide loader
                                $('#exportLoader').hide();

                                // Create a download link
                                const blob = new Blob([response], { type: xhr.getResponseHeader('Content-Type') });
                                const link = document.createElement('a');
                                link.href = window.URL.createObjectURL(blob);

                                // Get filename from Content-Disposition header or use default
                                const contentDisposition = xhr.getResponseHeader('Content-Disposition');
                                let filename = 'HomeServices_' + new Date().toISOString().slice(0, 10) + '.csv';
                                if (contentDisposition) {
                                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                                    if (filenameMatch && filenameMatch[1]) {
                                        filename = filenameMatch[1];
                                    }
                                }

                                link.download = filename;
                                link.click();
                                window.URL.revokeObjectURL(link.href);

                                // Show success message
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Export Successful',
                                    text: 'Home Services data has been exported successfully.',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            },
                            error: function(xhr) {
                                // Hide loader
                                $('#exportLoader').hide();

                                // Show error message
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Export Failed',
                                    text: xhr.responseJSON?.message || 'Something went wrong during export.',
                                });
                            }
                        });
                    }
                }
            ],
            // buttons: [
            //         {
            //             extend: 'csv',
            //             text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
            //             className: 'btn btn-success btn-sm bg-success-500',
            //             filename: 'Clients_' + new Date().toISOString().slice(0, 10),
            //             action: function (e, dt, node, config) {
            //                 $('#exportLoader').show(); // Show loader

            //                 setTimeout(() => {
            //                     $.fn.dataTable.ext.buttons.csvHtml5.action.call(this, e, dt, node, config);
            //                     $('#exportLoader').hide(); // Hide loader after export
            //                 }, 500); // Small delay for better UX
            //             }
            //         }
            //     ],

            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            }
        });

        // Custom search event triggers
        $("#searchkey").on("keyup", function () {
            table.draw();
        });

        $("#searchCategory, #searchVerified").on("change", function () {
            table.draw();
        });

        // District change handler (for Select2)
        $('#searchDistrict').on('change', function() {
            table.draw();
        });

        // Area change handler (for Select2)
        $('#searchArea').on('change', function() {
            table.draw();
        });

        // Cascading dropdowns
        $('#searchState').on('change', function() {
            var stateId = $(this).val();

            // Destroy existing Select2 instances
            if ($('#searchDistrict').hasClass('select2-hidden-accessible')) {
                $('#searchDistrict').select2('destroy');
            }
            if ($('#searchArea').hasClass('select2-hidden-accessible')) {
                $('#searchArea').select2('destroy');
            }

            // Clear districts and areas
            $('#searchDistrict').empty();
            $('#searchDistrict').append('<option value="">Select District</option>');
            $('#searchDistrict').append('<option value="">All</option>');

            $('#searchArea').empty();
            $('#searchArea').append('<option value="">Select Area</option>');
            $('#searchArea').append('<option value="">All</option>');

            if(stateId && stateId !== '') {
                // Initialize Select2 for districts with AJAX
                $('#searchDistrict').select2({
                    placeholder: 'Select District',
                    width: '200px',
                    ajax: {
                        url: '/api/get-districts/' + stateId,
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                q: params.term || '', // search term
                                page: params.page || 1
                            };
                        },
                        processResults: function (data, params) {
                            // Add "All" option at the beginning if it's the first page and no search term
                            var results = [];
                            if ((!params.term || params.term === '') && (!params.page || params.page === 1)) {
                                results.push({
                                    id: '',
                                    text: 'All Districts'
                                });
                            }

                            // Add the actual data
                            results = results.concat(data.map(function(item) {
                                return {
                                    id: item.id,
                                    text: item.name
                                };
                            }));

                            return {
                                results: results,
                                pagination: {
                                    more: false // Districts are usually manageable in number
                                }
                            };
                        },
                        cache: true
                    },
                    allowClear: true,
                    minimumInputLength: 0
                });
            }

            // Trigger table refresh
            table.draw();
        });

        $('#searchDistrict').on('change', function() {
            var districtId = $(this).val();

            // Destroy existing Select2 if it exists
            if ($('#searchArea').hasClass('select2-hidden-accessible')) {
                $('#searchArea').select2('destroy');
            }

            // Clear and reset areas dropdown
            $('#searchArea').empty();
            $('#searchArea').append('<option value="">Select Area</option>');
            $('#searchArea').append('<option value="">All</option>');

            if(districtId && districtId !== '') {
                // Initialize Select2 for areas with AJAX
                $('#searchArea').select2({
                    placeholder: 'Select Area',
                    width: '200px',
                    ajax: {
                        url: '/api/get-areas/' + districtId,
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                q: params.term || '', // search term
                                page: params.page || 1
                            };
                        },
                        processResults: function (data, params) {
                            // Add "All" option at the beginning if it's the first page and no search term
                            var results = [];
                            if ((!params.term || params.term === '') && (!params.page || params.page === 1)) {
                                results.push({
                                    id: '',
                                    text: 'All Areas'
                                });
                            }

                            // Add the actual data
                            results = results.concat(data.map(function(item) {
                                return {
                                    id: item.id,
                                    text: item.name + (item.pincode ? ' (' + item.pincode + ')' : '')
                                };
                            }));

                            return {
                                results: results,
                                pagination: {
                                    more: data.length >= 100 // More results available if we got 100 items
                                }
                            };
                        },
                        cache: true
                    },
                    allowClear: true,
                    minimumInputLength: 0
                });
            }

            // Trigger table refresh
            table.draw();
        });

        $(document).on("change", ".status-toggle", function() {
            let isChecked = $(this).prop("checked"); // Get switch status
            let homeserviceId = $(this).data("id"); // Get Home Service ID
            let status = isChecked ? 1 : 0; // 1 = Active, 0 = Inactive
            let switchElement = $(this); // Store switch reference
            let labelElement = $(this).next("label"); // Get label next to switch

            let actionText = status === 1 ? "activate" : "deactivate";
            let warningText = status === 1 ?
                "Do you want to activate this home service?" :
                "Do you want to deactivate this home service?";

            // Revert the toggle until confirmed
            switchElement.prop("checked", !isChecked);

            Swal.fire({
                title: "Are you sure?",
                text: warningText,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#28a745",
                cancelButtonColor: "#d33",
                confirmButtonText: `Yes, ${actionText} it!`,
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show processing state
                    switchElement.prop("disabled", true);

                    $.ajax({
                        url: "{{ route('homeservices.update-status') }}", // Your route to update status
                        type: "POST",
                        data: {
                            homeservice_id: homeserviceId,
                            status: status,
                            _token: $('meta[name="csrf-token"]').attr("content") // CSRF token
                        },
                        success: function(response) {
                            // Enable the switch again
                            switchElement.prop("disabled", false);

                            // Update the switch state
                            switchElement.prop("checked", isChecked);

                            // Update text dynamically
                            if (status === 1) {
                                labelElement.text("Active");
                            } else {
                                labelElement.text("Inactive");
                            }

                            // Show success message
                            Swal.fire({
                                icon: "success",
                                title: "Updated!",
                                text: "Home Service status has been changed.",
                                timer: 2000,
                                showConfirmButton: false
                            });
                        },
                        error: function(xhr) {
                            // Enable the switch again
                            switchElement.prop("disabled", false);

                            // Show error message
                            Swal.fire({
                                icon: "error",
                                title: "Error!",
                                text: xhr.responseJSON?.message || "Something went wrong!",
                            });
                        }
                    });
                }
            });
        });

        $(document).on("click", ".delete-homeservice", function() {
            let homeserviceId = $(this).data("id");
            let deleteButton = $(this);

            Swal.fire({
                title: "Are you sure?",
                text: "You won't be able to revert this! This will permanently delete the homeservice.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Yes, delete it!"
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show processing state
                    deleteButton.html('<iconify-icon icon="line-md:loading-twotone-loop"></iconify-icon>');
                    deleteButton.css('pointer-events', 'none');

                    $.ajax({
                        url: "{{ route('homeservices.delete') }}",
                        type: "POST",
                        data: {
                            homeservice_id: homeserviceId,
                            _token: $('meta[name="csrf-token"]').attr("content")
                        },
                        success: function(response) {
                            // Reload the DataTable
                            $('#homeservicesTable').DataTable().ajax.reload();

                            // Show success message
                            Swal.fire({
                                icon: "success",
                                title: "Deleted!",
                                text: "Home Service has been deleted successfully.",
                                timer: 2000,
                                showConfirmButton: false
                            });
                        },
                        error: function(xhr) {
                            // Reset the button
                            deleteButton.html('<iconify-icon icon="lucide:trash-2"></iconify-icon>');
                            deleteButton.css('pointer-events', 'auto');

                            // Show error message
                            Swal.fire({
                                icon: "error",
                                title: "Error!",
                                text: xhr.responseJSON?.message || "Something went wrong!",
                            });
                        }
                    });
                }
            });
        });
    });

    // Export button click handler
    $("#exportBtn").on("click", function() {
        // Show loader
        $('#exportLoader').show();

        // Get all filter values
        const searchkey = $("#searchkey").val();
        const searchCategory = $("#searchCategory").val();
        const searchArea = $("#searchArea").val();
        const searchDistrict = $("#searchDistrict").val();

        // Create form data
        const formData = new FormData();
        if (searchkey) formData.append('searchkey', searchkey);
        if (searchCategory) formData.append('searchCategory', searchCategory);
        if (searchArea) formData.append('searchArea', searchArea);
        if (searchDistrict) formData.append('searchDistrict', searchDistrict);
        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

        // Make AJAX request to export endpoint
        $.ajax({
            url: "{{ route('homeservices.export') }}",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: {
                responseType: 'blob' // Important for handling binary data
            },
            success: function(response, status, xhr) {
                // Hide loader
                $('#exportLoader').hide();

                // Create a download link
                const blob = new Blob([response], { type: xhr.getResponseHeader('Content-Type') });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);

                // Get filename from Content-Disposition header or use default
                const contentDisposition = xhr.getResponseHeader('Content-Disposition');
                let filename = 'homeservices_export.csv';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch && filenameMatch[1]) {
                        filename = filenameMatch[1];
                    }
                }

                link.download = filename;
                link.click();
                window.URL.revokeObjectURL(link.href);

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Export Successful',
                    text: 'Home Services data has been exported successfully.',
                    timer: 2000,
                    showConfirmButton: false
                });
            },
            error: function(xhr) {
                // Hide loader
                $('#exportLoader').hide();

                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Export Failed',
                    text: xhr.responseJSON?.message || 'Something went wrong during export.',
                });
            }
        });
    });
</script>
@stop
