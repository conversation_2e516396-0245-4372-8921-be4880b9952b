<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\State;
use App\Models\City;
use App\Models\Area;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;

class ImportLocationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:locations {file : Path to the CSV file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import states, cities, and areas from CSV file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info('Starting location data import...');
        
        try {
            // Read CSV file
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0); // First row contains headers
            
            $records = $csv->getRecords();
            
            $stateCache = [];
            $cityCache = [];
            $processedCount = 0;
            $skippedCount = 0;
            
            DB::beginTransaction();
            
            foreach ($records as $record) {
                try {
                    // Clean and validate data
                    $stateName = trim($record['state'] ?? '');
                    $cityName = trim($record['district'] ?? ''); // Using district as city
                    $areaName = trim($record['area name'] ?? '');
                    $pincode = trim($record['pincode'] ?? '');
                    
                    if (empty($stateName) || empty($cityName) || empty($areaName)) {
                        $this->warn("Skipping row with missing data: State: {$stateName}, City: {$cityName}, Area: {$areaName}");
                        $skippedCount++;
                        continue;
                    }
                    
                    // Get or create state
                    if (!isset($stateCache[$stateName])) {
                        $state = State::firstOrCreate(
                            ['name' => $stateName],
                            ['status' => 1]
                        );
                        $stateCache[$stateName] = $state->id;
                        $this->info("State: {$stateName} (ID: {$state->id})");
                    }
                    $stateId = $stateCache[$stateName];
                    
                    // Get or create city
                    $cityKey = $stateName . '|' . $cityName;
                    if (!isset($cityCache[$cityKey])) {
                        $city = City::firstOrCreate(
                            ['name' => $cityName, 'state_id' => $stateId],
                            ['status' => 1]
                        );
                        $cityCache[$cityKey] = $city->id;
                        $this->info("City: {$cityName} in {$stateName} (ID: {$city->id})");
                    }
                    $cityId = $cityCache[$cityKey];
                    
                    // Create or update area
                    $areaData = [
                        'name' => $areaName,
                        'city_id' => $cityId,
                        'state_id' => $stateId,
                        'status' => 1
                    ];
                    
                    // Add pincode if available
                    if (!empty($pincode) && is_numeric($pincode)) {
                        $areaData['pincode'] = $pincode;
                    }
                    
                    $area = Area::updateOrCreate(
                        [
                            'name' => $areaName,
                            'city_id' => $cityId,
                            'state_id' => $stateId
                        ],
                        $areaData
                    );
                    
                    $processedCount++;
                    
                    if ($processedCount % 100 == 0) {
                        $this->info("Processed {$processedCount} records...");
                    }
                    
                } catch (\Exception $e) {
                    $this->error("Error processing record: " . $e->getMessage());
                    $this->error("Record data: " . json_encode($record));
                    $skippedCount++;
                }
            }
            
            DB::commit();
            
            $this->info("\n=== Import Summary ===");
            $this->info("Total processed: {$processedCount}");
            $this->info("Total skipped: {$skippedCount}");
            $this->info("States created/updated: " . count($stateCache));
            $this->info("Cities created/updated: " . count($cityCache));
            $this->info("Import completed successfully!");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
