!function(o){Array.prototype.forEach||(o.forEach=o.forEach||function(o,e){for(var t=0,r=this.length;t<r;t++)t in this&&o.call(e,this[t],t,this)})}(Array.prototype);var mapObject,marker,markers=[],markersData={Marker:[{location_latitude:48.866024,location_longitude:2.340041,locationURL:"single-property-2.html",locationImg:"assets/img/p-1.jpg",propertyprice:"$220",propertytype:"For Rent",propertyname:"Green Vally Resort",propertytime:"mo"},{location_latitude:48.86856,location_longitude:2.349427,locationURL:"single-property-2.html",locationImg:"assets/img/p-2.jpg",propertyprice:"$920",propertytype:"For Sale",propertyname:"Nestled Real Estate",propertytime:"mo"},{location_latitude:48.870824,location_longitude:2.333005,locationURL:"single-property-2.html",locationImg:"assets/img/p-3.jpg",propertyprice:"$280",propertytype:"For Rent",propertyname:"Shipwright Realty",propertytime:"mo"},{location_latitude:48.864642,location_longitude:2.345837,locationURL:"single-property-2.html",locationImg:"assets/img/p-4.jpg",propertyprice:"$240",propertytype:"For Rent",propertyname:"Seekers Realty",propertytime:"mo"},{location_latitude:48.861753,location_longitude:2.338402,locationURL:"single-property-2.html",locationImg:"assets/img/p-5.jpg",propertyprice:"$820",propertytype:"For Sale",propertyname:"Agile Real Estate Group",propertytime:"mo"},{location_latitude:48.872111,location_longitude:2.345151,locationURL:"single-property-2.html",locationImg:"assets/img/p-6.jpg",propertyprice:"$260",propertytype:"For Rent",propertyname:"Bluebell Real Estate",propertytime:"mo"},{location_latitude:48.865881,location_longitude:2.341507,locationURL:"single-property-2.html",locationImg:"assets/img/p-7.jpg",propertyprice:"$320",propertytype:"For Rent",propertyname:"Corsair Real Estate",propertytime:"mo"},{location_latitude:48.867236,location_longitude:2.34361,locationURL:"single-property-2.html",locationImg:"assets/img/p-8.jpg",propertyprice:"$150",propertytype:"For Sale",propertyname:"Banyon Tree Realty",propertytime:"mo"}]},mapOptions={zoom:15,center:new google.maps.LatLng(48.867236,2.34361),mapTypeId:google.maps.MapTypeId.satellite,mapTypeControl:!1,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU,position:google.maps.ControlPosition.LEFT_CENTER},panControl:!1,panControlOptions:{position:google.maps.ControlPosition.TOP_RIGHT},zoomControl:!0,zoomControlOptions:{position:google.maps.ControlPosition.RIGHT_BOTTOM},scrollwheel:!1,scaleControl:!1,scaleControlOptions:{position:google.maps.ControlPosition.TOP_LEFT},streetViewControl:!0,streetViewControlOptions:{position:google.maps.ControlPosition.LEFT_TOP}};for(var key in mapObject=new google.maps.Map(document.getElementById("map"),mapOptions),markersData)markersData[key].forEach(function(o){marker=new google.maps.Marker({position:new google.maps.LatLng(o.location_latitude,o.location_longitude),map:mapObject,icon:"assets/img/marker.png"}),void 0===markers[key]&&(markers[key]=[]),markers[key].push(marker),google.maps.event.addListener(marker,"click",function(){closeInfoBox(),getInfoBox(o).open(mapObject,this),mapObject.setCenter(new google.maps.LatLng(o.location_latitude,o.location_longitude))})});function hideAllMarkers(){for(var o in markers)markers[o].forEach(function(o){o.setMap(null)})}function closeInfoBox(){$("div.infoBox").remove()}function getInfoBox(o){return new InfoBox({content:'<div class="map-popup-wrap"><div class="map-popup"><div class="property-listing property-2"><div class="listing-img-wrapper"><div class="list-single-img"><a href="'+o.locationURL+'"><img src="'+o.locationImg+'" class="img-fluid mx-auto" alt="" /></a></div><span class="property-type">'+o.propertytype+'</span></div><div class="listing-detail-wrapper pb-0"><div class="listing-short-detail"><h4 class="listing-name"><a href="'+o.locationURL+'">'+o.propertyname+'</a><i class="list-status ti ti-check"></i></h4></div></div><div class="price-features-wrapper"><div class="listing-price-fx"><h6 class="listing-card-info-price price-prefix">'+o.propertyprice+'<span class="price-suffix">/'+o.propertytime+"</span></h6></div></div></div></div></div>",disableAutoPan:!1,maxWidth:0,pixelOffset:new google.maps.Size(10,92),closeBoxMargin:"",closeBoxURL:"assets/img/close.png",isHidden:!1,alignBottom:!0,pane:"floatPane",enableEventPropagation:!0})}function onHtmlClick(o,e){google.maps.event.trigger(markers[o][e],"click")}new MarkerClusterer(mapObject,markers[key]);