/* === reset css start === */
// google font url
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

*:where(:not(html, iframe, canvas, img, svg, video, audio):not(svg *, symbol *)) {
  all: unset;
  display: revert;
}

:where([hidden]) {
  display: none;
}

:where([contenteditable]:not([contenteditable=false])) {
  -moz-user-modify: read-write;
  -webkit-user-modify: read-write;
  overflow-wrap: break-word;
  -webkit-line-break: after-white-space;
  -webkit-user-select: auto;
}

:where([draggable=true]) {
  -webkit-user-drag: element;
}

:where(dialog:modal) {
  all: revert;
}

*,
::before,
::after {
  box-sizing: border-box;
  border-style: solid;
  border-width: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--bg-color);
  color: var(--text-secondary-light);
  font-family: "Inter", sans-serif;
  font-size: rem(16px);
  padding: 0;
  margin: 0;
  font-weight: 400;
  position: relative;
  line-height: 1.7;
  overflow-x: hidden;
  &.overlay-active {
    &::after {
      width: 100%;
    }
  }
  &::after {
    position: absolute;
    content: '';
    top: 0;
    inset-inline-start: 0;
    width: 0;
    height: 100%;
    background-color: #000;
    opacity: 0.65;
    transition: all 0.3s;
    z-index: 2;
  }
}

main {
  display: block;
}

footer {
  margin-top: auto;
}

img {
  max-width: 100%;
  height: auto;
  user-select: none;
}

select {
  cursor: pointer;
}

dt {
  margin-left: 0;
}

hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  border-top-width: 1px;
  margin: 0;
  clear: both;
  color: inherit;
  opacity: 0.15;
}

pre {
  font-family: monospace, monospace;
  font-size: inherit;
}

address {
  font-style: inherit;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

button {
  cursor: pointer;
}

*:focus {
  outline: none;
}

button {
  border: none;
}

button:focus {
  outline: none;
}

span {
  display: inline-block;
}

a {
  text-decoration: none;
  display: inline-block;
  background-color: transparent;
  color: inherit;
}

a, button {
  cursor: revert;
}

a:hover {
  text-decoration: none;
}

strong {
  font-weight: 700;
}

a:hover {
  color: var(--primary);
}

abbr[title] {
  text-decoration: underline dotted;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: inherit;
}

small {
  font-size: 80%;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

svg,
img,
embed,
object,
iframe {
  vertical-align: bottom;
}

button,
input,
optgroup,
select,
textarea {
  -webkit-appearance: none;
  appearance: none;
  vertical-align: middle;
  color: inherit;
  font: inherit;
  background: transparent;
  padding: 0;
  margin: 0;
  border-radius: 0;
  text-align: inherit;
  text-transform: inherit;
}

button,
[type=button],
[type=reset],
[type=submit] {
  cursor: pointer;
  -webkit-appearance: button;
}

button:disabled,
[type=button]:disabled,
[type=reset]:disabled,
[type=submit]:disabled {
  cursor: default;
}

:-moz-focusring {
  outline: auto;
}

select:disabled {
  opacity: inherit;
}

option {
  padding: 0;
}

fieldset {
  margin: 0;
  padding: 0;
  min-width: 0;
}

legend {
  padding: 0;
}

progress {
  vertical-align: baseline;
}

textarea {
  overflow: auto;
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
}

[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

[type=number] {
  -moz-appearance: textfield;
}

label[for] {
  cursor: pointer;
}

details {
  display: block;
}

summary {
  display: list-item;
}

[contenteditable]:focus {
  outline: auto;
}

table {
  border-color: inherit;
  border-collapse: collapse;
}

caption {
  text-align: left;
}

td,
th {
  vertical-align: top;
  padding: 0;
}

th {
  text-align: left;
  font-weight: bold;
}
/* === reset css end === */