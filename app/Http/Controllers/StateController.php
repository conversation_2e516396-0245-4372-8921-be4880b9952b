<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\State;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class StateController extends Controller
{

    //
    public function index()
    {
        return view('admin.states.list');
    }
    public function add()
    {
        return view('admin.states.add');
    }
    //
    // Store method to save state data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
        ]);
        $data = $request->all();

        // Save data to database
        State::create($data);

        // Redirect to state list page with success message
        return redirect()->route('states.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $state = State::where('id', $id)->first();


        return view('admin.states.edit', compact('state'));
    }
    public function update(Request $request, State $state)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
        ]);

       // $data = $request->except(['image']); // Exclude logo from mass update initially

       $data = $request->all();
        // Handle file upload

        // Update state  data
        $state->update($data);

        // Redirect to state  list page with success message
        return redirect()->route('states.index')->with('success', $state->name . ' updated successfully!');
    }

    public function getstates(Request $request)
    {
        // dd('h');
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();
            // Fetch base state  query
            $states = State::select(['id', 'name', 'status', 'created_at']);



            return DataTables::of($states)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Active</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Inactive</span>';
                    }
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'state-edit' permission
                    if ($user->can('state-edit')) {
                        $output .= '
                            <a href="' . route('states.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }


                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ;
                        });
                    }
                })

                ->rawColumns(['status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }

}
