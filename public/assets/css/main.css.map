{"version": 3, "sources": ["main.scss", "base/_index.scss", "base/_reset.scss", "base/_color.scss", "base/_bg.scss", "base/_base.scss", "abstracts/_mixins.scss", "base/_spacing.scss", "base/_extend.scss", "base/_list-style.scss", "base/_animation.scss", "base/_typography.scss", "components/_index.scss", "components/_preloader.scss", "components/_buttons.scss", "components/_badge.scss", "components/_table.scss", "components/_form.scss", "components/_card.scss", "components/_modal.scss", "components/_accordion.scss", "components/_nav-tabs.scss", "components/_pagination.scss", "layout/_index.scss", "layout/_header.scss", "layout/_footer.scss", "layout/_inner-banner.scss", "sections/_index.scss", "sections/_banner.scss", "sections/_service.scss", "sections/_service-details.scss", "sections/_ticker.scss", "sections/_about.scss", "sections/_project.scss", "sections/_project-details.scss", "sections/_message.scss", "sections/_brand.scss", "sections/_newsletter.scss", "sections/_testimonial.scss", "sections/_team.scss", "sections/_team-details.scss", "sections/_faq.scss", "sections/_blog.scss", "sections/_blog-details.scss", "sections/_pricing.scss", "sections/_overview.scss", "sections/_gallery.scss", "sections/_video.scss", "sections/_choose.scss", "sections/_contact.scss"], "names": [], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;ACCA;ACDA;AACQ;AAER;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;AAAA;AAAA;EAGE;EACA;EACA;;;AAGF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAGF;AAAA;EAEE;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;AAAA;EAEE;;;AAGF;AAAA;AAAA;EAGE;EACA;;;AAGF;EACE;;;AAGF;AAAA;EAEE;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;AAAA;AAAA;AAAA;AAAA;EAKE;;;AAGF;AAAA;AAAA;AAAA;AAAA;EAKE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;AAAA;AAAA;AAAA;EAIE;EACA;;;AAGF;AAAA;AAAA;AAAA;EAIE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;AAAA;EAEE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAGF;AAAA;EAEE;EACA;;;AAGF;EACE;EACA;;;AAEF;ACtUA;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;ACbF;AACA;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAEF;ACXA;EACE;;;AC4EA;ED7EF;IAGI;;;AC0EF;ED7EF;IAMI;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;ACwDA;ED3DF;IAKI;;;ACsDF;ED3DF;IAQI;;;ACmDF;ED3DF;IAWI;;;AAIJ;EChCE;EACA;EACA;EACA;EACA;;;ALPF;AAEA;AMLA;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;ADqEA;ECtEF;IAGI;;;ADmEF;ECtEF;IAMI;;;ADgEF;ECtEF;IASI;;;AD6DF;ECtEF;IAYI;;;AAIJ;EACE;;;ADqDA;ECtDF;IAGI;;;ADmDF;ECtDF;IAMI;;;ADgDF;ECtDF;IASI;;;AD6CF;ECtDF;IAYI;;;AAIJ;EACE;;;ADqCA;ECtCF;IAGI;;;ADmCF;ECtCF;IAMI;;;ADgCF;ECtCF;IASI;;;AD6BF;ECtCF;IAYI;;;ANjDJ;AAEA;AOSA;EACE;EACA;EACA;;;AAQF;EACE;;;AAWF;EACE;;;AAQF;EF3BE;EACA;EACA;EACA;;;AE2BF;EACE;;;AAEF;EACE;;;AA0BF;EF7EE;EACA;EACA;EACA;EACA;;;ALCF;AAEA;AQZE;EACE;;;AAEF;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMJ;EACE;;;AAEF;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKN;EACE;EACA;;;AACA;EACE;EACA;EACA;;;AAEE;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EHvDJ;EACA;EACA;EACA;EACA;;;AGsDE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ARzDN;AAEA;ASjBA;EACE;EACA;EACA;EACA;;;AAGF;EACE;EAEA;;;AJ6EA;EI1EA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJ6DF;EI7EA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJgEF;EIhFA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJmEF;EInFA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AAIJ;EACE;EACA;;;AJoDA;EIjDA;IACE;IACA;IACA;;EAGF;IACE;IACA;IACA;;;AJ2CF;EIpDA;IACE;IACA;IACA;;EAGF;IACE;IACA;IACA;;;AJ8CF;EIvDA;IACE;IACA;IACA;;EAGF;IACE;IACA;IACA;;;AJiDF;EI1DA;IACE;IACA;IACA;;EAGF;IACE;IACA;IACA;;;AAIJ;EACE;EAEA;;;AJiCA;EI9BA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJiBF;EIjCA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJoBF;EIpCA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJuBF;EIvCA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AAGJ;EACE;EAEA;;;AJQA;EILA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJRF;EIRA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJLF;EIXA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AJFF;EIdA;IACE;IAEA;IAEA;IAEA;;EAEF;IACE;IAEA;IAEA;IAEA;;;AT/EJ;AAEA;AUrBA;EACE;;;AACA;EAFF;IAGI;;;AAEF;EALF;IAMI;;;AAEF;EARF;IASI;;;AAGJ;EACE;;;AACA;EAFF;IAGI;;;AAEF;EALF;IAMI;;;AAIJ;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;EACA;EACA;EACA;EACA;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;EACA;EACA;EL/CA;EACA;EACA;EK+CA;EACA;;;AAEF;AAAA;AAAA;EAGE;;;AAEF;EACE;EACA;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AAGF;EACE;;;AV9FF;AWvBA;ACEI;EACE;EACA;;;AAKN;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EPLA;EACA;EACA;;;AOOF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;IACI;;EAGJ;IACI;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;IACI;;EAGJ;IACI;IACA;;EAGJ;IACI;;EAGJ;IACI;;EAGJ;IACI;IACA;;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EPhXA;EACA;EACA;EACA;EACA;;;AMRF;AAEA;AEFE;EACE;;;AAIJ;EACE;EACA;EACA;;;ARwEA;EQ3EF;IAKI;;;ARsEF;EQ3EF;IAQI;;;ARmEF;EQ3EF;IAWI;;;AAEF;EACE;EACA;;;AAIJ;EACE;EACA;;;AACA;EAEE;EACA;;;AAIJ;EACE;EACA;;;AACA;EAEE;EACA;;;AAIJ;EACE;EACA;;;AACA;EAEE;EACA;EACA;;;AAKF;EACE;EACA;;;AAEF;EACE;EACA;;;AF1DJ;AAEA;AGJE;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AAHF;EACE;EACA;EACA;;;AHGJ;AAEA;AIVE;EACE;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;;;AAKJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;;;AAKA;EACE;;;AAQV;EACE;;;AACA;EACE;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;;;AVuBF;EUlBE;IACE;;EAIE;IACE;;EAGA;IACE;;EAGJ;IACE;;EACA;IACE;;EAMN;AAAA;IAEE;IACA;IACA;;EACA;AAAA;IACE;;EAKJ;IACE;;;AVhBN;EUuBE;IACE;;EAIE;IACE;;EAEF;IACE;;EACA;IACE;;EAMN;AAAA;IAEE;IACA;IACA;IACA;;EACA;AAAA;IACE;;EAKJ;IACE;;;AJzHR;AAEA;AKfA;EACE;EACA;EACA;EACA;EXCA;EACA;EACA;EACA;EACA;EWHA;;;AX8FA;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AWtGF;EACE;EACA;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;EX1BA;EACA;EACA;EACA;EACA;EWwBA;;;AACA;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAIJ;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAEF;AAAA;EAEE;EACA;EACA;EACA;;;AAGA;EACE;EACA;;;AAGJ;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;;AAGI;EACE;EACA;;;AAEF;EACE;;;AAKR;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EXrGJ;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;;;AWgGE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EXhHJ;EACA;EACA;EACA;EACA;EW8GI;EX3GJ;EACA;EACA;;;AW8GF;EACE;;;AACA;EACE;;;AAGI;EACE;EACA;;;AAKR;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ALtIN;AAEA;AMnBA;EACE;EAIA;;;AACA;EACE;EACA;EACA;;;AAEF;EACE;;;AZqEF;EYtEA;IAGI;;;ANON;AAEA;AOvBA;EACE;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAIA;EACE;;;AAEF;EACE;;;AAKA;EACE;;;AAIF;EACE;;;APNN;AAEA;AQ5BA;EACE;EdKA;EACA;EACA;EACA;EACA;EcPA;EACA;;;AACA;EACE;;;AAGA;EdHF;EACA;EACA;EACA;EACA;EcCI;EACA;;;AdsEJ;EczEE;IAKI;;;AdoEN;EczEE;IAQI;;;AAEF;EACE;EACA;EdfN;EACA;EACA;EACA;EACA;;;AccI;EACE;;;AAEF;EACE;EACA;EACA;EACA;;;AAIN;EACE;;;AAMA;EACE;EACA;EACA;;;AAEE;EACE;EACA;EACA;Ed5CR;EACA;EACA;EACA;EACA;Ec0CQ;;;Ad8BR;EcnCM;IAOI;;;Ad4BV;EcnCM;IAUI;IACA;;;AAEF;EACE;EACA;EACA;EACA;Ed1DV;EACA;EACA;EACA;EACA;EcwDU;EACA;EACA;EACA;;;AdaV;EctBQ;IAWI;IACA;IACA;;;AAGJ;EACE;EACA;EdxEV;EACA;EACA;EACA;EACA;EcsEU;;;AACA;EACE;EACA;EACA;EACA;;;AAKR;EACE;;;AAEF;EACE;;;ARhER;AAEA;AShCA;EACE;;;AAEE;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;;;ATwBR;AAEA;AUpCA;EACE;EACA;EACA;EACA;EACA;EACA;;;AhB4EA;EgBlFF;IAQI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EhBVF;EACA;EACA;EACA;EACA;EgBQE;EACA;EACA;;;AhB8DF;EgBxEA;IAYI;IACA;IACA;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;;AVGN;AWtCA;ACAA;AAAA;AAAA;EAGE;EACA;EACA;EACA;EACA;;;AAIA;EACE;EACA;;;AACA;EACE;;;AAKN;EACE;EACA;;;AACA;EACE;;;AAEF;EACE;;;AlBuDF;EkBxDA;IAGI;;;AAGJ;EACE;;;AlBiDF;EkBlDA;IAGI;;;AlB+CJ;EkBlDA;IAMI;;;AAMJ;EACE;;;AlBqCF;EkBtCA;IAGI;;;AlBmCJ;EkBtCA;IAMI;;;AlBgCJ;EkBtCA;IASI;IACA;;;AlB4BJ;EkB1BE;IAEI;IACA;IACA;IACA;IACA;;;AlBoBN;EkB1BE;IASI;IACA;;;AlBgBN;EkBbE;IAEI;;;AlBWN;EkBbE;IAKI;;;AAKN;EACE;EACA;EACA;;;AlBAF;EkBHA;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AlBbJ;EkBIE;IAWI;;;AlBfN;EkBIE;IAcI;;;AAOA;EACE;;;AAGJ;EACE;;;AAON;EACE;;;AAEF;EACE;;;AAGA;EACE;;;AACA;EACE;;;AAMF;EACE;;;AAEF;EACE;;;AACA;EACE;;;AAQF;EACE;;;AAGJ;EACE;;;AAIN;EACE;;;AlB7EF;EkB4EA;IAGI;;;AlB/EJ;EkB4EA;IAMI;;;AlBlFJ;EkBoFE;IAEI;;;AAIN;AAAA;AAAA;EAGE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;;AlBtGA;EkBiGF;IAOI;;;AlBxGF;EkBiGF;IAUI;;;AlB3GF;EkBiGF;IAaI;;;AAEF;EACE;;;AlBjHF;EkBgHA;IAGI;;;AlBnHJ;EkBgHA;IAMI;;;AlBtHJ;EkBgHA;IASI;;;AAKN;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AlBrIA;EkB8HF;IASI;;;AAGJ;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAIN;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;;;AAEE;EACE;;;AAMR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AlBjLA;EkB0KF;IASI;;;AAIJ;EACE;EACA;EACA;;;AlB1LA;EkBuLF;IAKI;;;AAGA;EACE;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGA;EACE;EACA;EACA;;;AAIN;EACE;EACA;EACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ElBrSF;EACA;EACA;;;AkBqSE;EACE;;;AACA;ElBzSJ;EACA;EACA;EkBySM;;;AACA;EACE;;;AAOV;EACE;EACA;EACA;EACA;;;AlBnPA;EkB+OF;IAMI;;;AlBrPF;EkB+OF;IASI;;;AAEF;EACE;;;AlB3PF;EkB0PA;IAGI;;;AAKN;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;;;AAEE;EACE;EACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;ElBxWF;EACA;EACA;EACA;EACA;EkBsWE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AACA;EACE;EACA;;;AAKA;EACE;EACA;;;AAGJ;EACE;EACA;;;AAEF;EACE;;;AAKN;EACE;EACA;EACA;EACA;;;AlBlUA;EkB8TF;IAMI;IACA;IACA;;;AlBtUF;EkBwUA;IAEI;;;AAKN;EACE;EACA;EACA;ElBnaA;EACA;EACA;EkBmaA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ElBhaA;EACA;EACA;EkBgaA;;;AACA;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;ElBvbF;EACA;EACA;EkBubE;EACA;EACA;ElBtbF;EACA;EACA;EACA;EACA;EkBobE;EACA;EACA;;;AACA;EACE;;;AlBhXJ;EkBmWA;IAgBI;;;AAGJ;EACE;EACA;;;AlBxXF;EkBsXA;IAII;;;AAEF;EACE;EACA;EACA;EACA;ElBrcJ;EACA;EACA;EkBqcI;;;AACA;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKN;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ElBheA;EACA;EACA;EkBgeA;EACA;EACA;EACA;;;AlBhaA;EkBmZF;IAeI;IACA;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;ElB1gBJ;EACA;EACA;EACA;EACA;;;AkB0gBA;EACE;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAIN;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;;;AAEE;EACE;;;AAQR;EACE;;;AACA;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACE;EACA;EACA;;;AACA;EACE;;;AACA;EACE;;;ADplBV;AAEA;AEJA;EACE;EACA;EACA;EACA;EACA;;;AnB6EA;EmBlFF;IAOI;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKF;EACE;;;AAEF;EACE;EACA;EACA;;;AAEE;EACE;;;AAGJ;EACE;EACA;EACA;EnBpCJ;EACA;EACA;EACA;EACA;EmBkCI;EACA;EACA;EACA;EnBlCJ;EACA;EACA;;;AmBmCE;EACE;EACA;EACA;;;AACA;EACE;;;AAMR;EACE;EACA;EACA;EACA;;;AAEE;EACE;EACA;EACA;EACA;EnBjEJ;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EmB2DI;EACA;EACA;;;AACA;EACE;;;AAON;EACE;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EnB7FF;EACA;EACA;EACA;EACA;;;AmB4FA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAIJ;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EnBvHF;EACA;EACA;EACA;EACA;;;AmBqHE;EACE;;;AAEE;EnBjIN;EACA;EACA;;;AmBmII;EACE;EACA;EnB5FN;EACA;EACA;EACA;EAlCA;EACA;EACA;;;AmBkIA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AnB7EA;EmBoEF;IAWI;;;AnB/EF;EmBoEF;IAcI;;;AAEF;AAAA;EAEE;;;AAGA;EACE;EACA;EACA;EACA;EACA;;;AAGJ;EACE;;;AAEE;EACE;;;AAGJ;EACE;EACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EnB9LF;EACA;EACA;EACA;EACA;EAkCA;EACA;EACA;EACA;EmBwJE;;;AAIJ;EACE;EACA;;;AAGF;EACE;;;AAGF;EACE;EACA;;;AnBpIA;EmBkIF;IAII;;;AAKF;EACE;;;AAGA;EACE;EACA;EACA;EACA;EnBxNJ;EACA;EACA;;;AmBwNI;EACE;;;AAEF;EACE;;;AAON;EACE;;;AAEF;EACE;EACA;;;AACA;EACE;EACA;EnBpPJ;EACA;EACA;EACA;EACA;EmBkPI;;;AACA;EACE;EACA;EnBnNN;EACA;EACA;EACA;;;AmBoNE;EACE;EACA;;;AAEE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAIN;EACE;EACA;;;AAMR;EACE;EACA;EACA;EACA;;;AAEE;EACE;EACA;EnBvRJ;EACA;EACA;;;AmBuRI;EACE;;;AAMR;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AnB7OF;EmByOA;IAMI;;;AnB/OJ;EmByOA;IASI;;;AnBlPJ;EmByOA;IAYI;;;AnBrPJ;EmByOA;IAeI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AAGJ;EACE;;;AACA;EACE;EnB/VJ;EACA;EACA;EACA;EACA;EmB6VI;;;AFjWN;AAEA;AGRA;EACE;EACA;EACA;EACA;;;ApB8EA;EoBlFF;IAMI;IACA;;;ApB2EF;EoBlFF;IAUI;;;ApBwEF;EoBlFF;IAaI;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EpBYF;EACA;EACA;EACA;EoBbE;;;AAEF;EACE;EACA;EACA;EACA;;;AACA;EALF;IAMI;;;AAEF;EARF;IASI;;;AAEF;EAXF;IAYI;;;AAKN;EACE;EACA;EACA;EACA;;;ApByBA;EoB7BF;IAMI;;;AAEF;EACE;EACA;EACA;;;ApBkBF;EoBrBA;IAKI;;;AAGA;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;;;AHvEN;AIVA;ACAA;EACE;EACA;EACA;EACA;EACA;EACA;;;AtB4EA;EsBlFF;IAQI;IACA;;;AtByEF;EsBlFF;IAYI;IACA;;;AtBqEF;EsBlFF;IAgBI;;;AtBkEF;EsBlFF;IAmBI;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AtBoDF;EsBzDA;IAOI;;;AtBkDJ;EsBzDA;IAUI;;;AtB+CJ;EsBzDA;IAaI;;;AtB4CJ;EsBzDA;IAgBI;IACA;;;AtBwCJ;EsBzDA;IAoBI;;;AAGJ;EACE;EACA;EACA;;;AtB+BF;EsBlCA;IAKI;;;AtB6BJ;EsBlCA;IAQI;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AtBgBF;EsBrBA;IAOI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AtBDF;EsBHA;IAMI;;;AtBHJ;EsBHA;IASI;;;AtBNJ;EsBHA;IAYI;;;AtBTJ;EsBHA;IAeI;IACA;;;AtBbJ;EsBHA;IAmBI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AtB3BF;EsBoBA;IASI;;;AtB7BJ;EsBoBA;IAYI;;;AtBhCJ;EsBoBA;IAeI;IACA;;;AAEF;EACE;EACA;EACA;EACA;;;AtB1CJ;EsBoBA;IAyBI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AtBzDF;EsBgDA;IAWI;;;AtB3DJ;EsBgDA;IAcI;;;AtB9DJ;EsBgDA;IAiBI;IACA;;;AAKN;EACE;EACA;EACA;;;AAEE;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EtBnKF;EACA;EACA;EACA;EACA;EsBiKE;EtB9JF;EACA;EACA;;;AsBgKE;EACE;EACA;;;AAEF;EACE;EACA;;;AAKA;AAAA;EAEE;;;AAMR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AtBvHA;EsBgHF;IASI;IACA;;;AtB1HF;EsBgHF;IAaI;;;AtB7HF;EsBgHF;IAgBI;IACA;;;AAEF;EACE;EACA;EACA;EACA;;;AtBvIF;EsBmIA;IAMI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGJ;EACE;EACA;EACA;EACA;;;AtBzJF;EsBqJA;IAMI;;;AtB3JJ;EsBqJA;IASI;;;AtB9JJ;EsBqJA;IAYI;;;AtBjKJ;EsBqJA;IAeI;IACA;;;AtBrKJ;EsBqJA;IAmBI;;;AAIF;EACE;;;AAEF;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EtBrOF;EACA;EACA;EACA;EsBoOE;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AtBlNA;EsB4MF;IAQI;IACA;;;AtBrNF;EsB4MF;IAYI;IACA;;;AtBzNF;EsB4MF;IAgBI;;;AtB5NF;EsB4MF;IAmBI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AtBrPF;EsBgPA;IAOI;;;AtBvPJ;EsBgPA;IAUI;;;AtB1PJ;EsBgPA;IAaI;;;AtB7PJ;EsBgPA;IAgBI;IACA;;;AtBjQJ;EsBgPA;IAoBI;;;AAGJ;EACE;EACA;EACA;;;AtB1QF;EsBuQA;IAKI;;;AtB5QJ;EsBuQA;IAQI;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AtBzRF;EsBoRA;IAOI;IACA;;;AAGJ;EACE;EACA;EACA;;;AtBlSF;EsB+RA;IAKI;;;AtBpSJ;EsB+RA;IAQI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;;;AACA;EACE;EACA;;;AAIN;EACE;EACA;EACA;EACA;EACA;EtB9WF;EACA;EACA;EACA;EsB6WE;;;AAEF;EACE;EACA;EACA;EACA;;;AtBhVF;EsB4UA;IAMI;;;AtBlVJ;EsB4UA;IASI;;;AtBrVJ;EsB4UA;IAYI;IACA;;;AtBzVJ;EsB4UA;IAgBI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;;;AtBpWF;EsBgWA;IAMI;;;AtBtWJ;EsBgWA;IASI;;;AtBzWJ;EsBgWA;IAYI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AtBvXF;EsBgXA;IASI;;;AtBzXJ;EsBgXA;IAYI;;;AtB5XJ;EsBgXA;IAeI;IACA;;;AAEF;EACE;EACA;EACA;EACA;;;AtBtYJ;EsBgXA;IAyBI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AtBrZF;EsB4YA;IAWI;;;AtBvZJ;EsB4YA;IAcI;;;AtB1ZJ;EsB4YA;IAiBI;IACA;;;AAKN;EACE;EACA;EACA;;;AAGE;EACE;;;AAEF;EACE;;;AAGJ;EACE;EACA;EACA;EtB/fF;EACA;EACA;EACA;EACA;EsB6fE;EACA;EACA;EACA;EACA;;;AAIJ;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACE;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACC;;;AtB1dA;EsB6cF;IAeI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;;;AAKN;EACC;IACC;;EAED;IACC;;;AD7kBF;AAEA;AEDM;EACE;;;AAGJ;EACE;;;AAGJ;EACE;EACA;EvBPF;EACA;EACA;EACA;EACA;;;AAwEA;EuBvEA;IAKI;;;AvBkEJ;EuBvEA;IAQI;;;AAEF;EACE;EACA;EvBqBJ;EACA;EACA;EACA;EAlCA;EACA;EACA;;;AuBaA;EACE;EACA;EACA;EACA;EACA;EvB3BF;EACA;EACA;EACA;EACA;EuByBE;EACA;EACA;;;AvB6CF;EuBtDA;IAWI;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EvB1CF;EACA;EACA;EACA;EACA;EuBwCE;EACA;EACA;EvBvCF;EACA;EACA;;;AuBuCE;EACE;;;AAGJ;EACE;EACA;;;AAIJ;EACE;EvB3DA;EACA;EACA;EACA;EACA;EuByDA;;;AAGI;EACE;;;AAIF;EACE;;;AAIN;EACE;EACA;EvB5EF;EACA;EACA;EACA;EACA;;;AAwEA;EuBFA;IAKI;;;AvBHJ;EuBFA;IAQI;;;AAEF;EACE;EACA;EvBhDJ;EACA;EACA;EACA;;;AuBiDA;EACE;EACA;EACA;;;AvBjBF;EuBcA;IAKI;;;AAEF;EACE;EACA;EvBnGJ;EACA;EACA;EACA;EACA;EuBiGI;EACA;EACA;EACA;EACA;EACA;EvBnGJ;EACA;EACA;;;AuBqGA;EACE;;;AvBnCF;EuBkCA;IAGI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AvB7CF;EuBwCA;IAOI;;;AAKN;EACE;EACA;EACA;;;AAGF;EACE;EACA;EvBxIA;EACA;EACA;EACA;EACA;EuBsIA;EACA;;;AvB/DA;EuB0DF;IAOI;;;AvBjEF;EuB0DF;IAUI;;;AAEF;EACE;EACA;EACA;EvBrJF;EACA;EACA;EACA;EACA;EuBmJE;EACA;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EvBvKF;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EuBiKE;;;AACA;EACE;EACA;;;AAKN;EACE;EACA;EvBpLA;EACA;EACA;EACA;EACA;EuBkLA;EACA;EvBhLA;EACA;EACA;EuBgLA;EACA;EACA;;;AvB/GA;EuBsGF;IAWI;;;AvBjHF;EuBsGF;IAcI;IACA;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EvB5MF;EACA;EACA;EACA;EACA;EuB0ME;;;AACA;EACE;;;AAGJ;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AF/NN;AAEA;AGRA;EACE;EACA;ExBIA;EACA;EACA;EACA;EACA;;;AAwEA;EwBlFF;IAKI;;;AAEF;EACE;EACA;ExBHF;EACA;EACA;EACA;EACA;EwBCE;;;AHDJ;AAEA;AIZA;EACE;EACA;EACA;;;AzB+EA;EyBlFF;IAKI;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AzBwDF;EyB/DA;IASI;;;AzBsDJ;EyB/DA;IAYI;;;AzBmDJ;EyB/DA;IAeI;;;AAEF;EACE;;;AzBkDJ;EyB5CA;IACE;;;AzB8CF;EyB/CA;IACE;;;AzBiDF;EyBlDA;IACE;;;AzBoDF;EyBrDA;IACE;;;AJ9BJ;AAEA;AKhBA;EACE;EACA;;;AACA;EACE;EACA;EACA;;;A1B4EF;E0B/EA;IAKI;;;AAKN;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;E1B7BF;EACA;EACA;EACA;EACA;E0B2BE;EACA;EACA;;;AAEF;EACE;EACA;E1BrCF;EACA;EACA;EACA;EACA;;;A0BsCF;EACE;EACA;EACA;EACA;EACA;;;A1B6BA;E0BlCF;IAOI;;;AAIJ;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;;;AAIJ;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E1BnFF;EACA;EACA;EACA;EACA;E0BiFE;;;AAEF;E1BvFA;EACA;EACA;EACA;EACA;E0BqFE;EACA;E1BpDF;EACA;EACA;EACA;;;A0BoDA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;E1BvGF;EACA;EACA;EACA;EACA;;;A0BqGE;EACE;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAMJ;EACE;;;AAEF;EACE;;;AACA;EACE;EACA;;;AAEF;EACE;;;ALzHN;AAEA;AMpBA;EAIE;EACA;EACA;EACA;EACA;E3BFA;EACA;EACA;EACA;EACA;E2BAA;EACA;;;AAVA;EACE;;;A3BgFF;E2BlFF;IAaI;IACA;;;A3BoEF;E2BlFF;IAiBI;IACA;IACA;;;A3B+DF;E2BlFF;IAsBI;;;AAGA;EACE;;;AAEF;EACE;;;AAIJ;EACE;EACA;EACA;E3B9BF;EACA;EACA;EACA;EACA;E2B4BE;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;A3BiCF;E2BpCA;IAKI;;;A3B+BJ;E2BpCA;IAQI;;;A3B4BJ;E2BpCA;IAWI;IACA;;;AAIJ;EACE;;;A3BmBF;E2BpBA;IAGI;;;AAIJ;EACE;EACA;E3BjEF;EACA;EACA;EACA;EACA;E2B+DE;EACA;EACA;E3B9DF;EACA;EACA;;;AAmEA;E2BbA;IASI;IACA;;;AAGJ;EACE;E3BtEF;EACA;EACA;;;AAmEA;E2BAA;IAII;;;ANhEN;AAEA;AOxBA;EACE;EACA;EACA;E5BGA;EACA;EACA;EACA;EACA;E4BLA;EACA;EACA;;;A5B2EA;E4BlFF;IASI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;E5BlBF;EACA;EACA;EACA;EACA;E4BgBE;;;AACA;EACE;EACA;E5BeJ;EACA;EACA;EACA;;;A4BdA;EACE;EACA;;;A5B+CF;E4BjDA;IAII;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKN;EACE;EACA;EACA;E5BxDA;EACA;EACA;EACA;EACA;;;AAwEA;E4BvBF;IAMI;IACA;;;AAEF;EACE;;;A5BaF;E4BdA;IAGI;;;AAMJ;EACE;;;AAEF;EACE;EACA;E5B5EF;EACA;EACA;EACA;EACA;E4B0EE;EACA;EACA;;;A5BJF;E4BFA;IAQI;;;AAGA;EACE;EACA;EACA;EACA;;;AAEF;EACE;;;AAGA;EACE;EACA;;;AP7EV;AAEA;AQ5BA;EACE;EACA;;;AACA;EACE;EACA;;;AACA;EACE;EACA;;;AAGF;EACE;EACA;;;AAKN;EACE;EACA;E7BdA;EACA;EACA;EACA;EACA;E6BYA;EACA;;;A7B2DA;E6BhEF;IAOI;;;A7ByDF;E6BhEF;IAUI;;;AAEF;EACE;;;A7BmDF;E6BpDA;IAGI;;;A7BiDJ;E6BpDA;IAMI;;;AAGJ;EACE;EACA;EACA;EACA;;;A7BuCF;E6B3CA;IAMI;;;A7BqCJ;E6B3CA;IASI;;;A7BkCJ;E6B3CA;IAYI;IACA;IACA;;;A7B6BJ;E6B1BA;IAEI;;;AAKN;EACE;;;A7BkBA;E6BnBF;IAGI;;;AAEF;EACE;;;AAKJ;EACE;EACA;EACA;;;A7BKA;E6BRF;IAKI;;;A7BGF;E6BRF;IAQI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;E7BpFF;EACA;EACA;EACA;EACA;E6BkFE;;;A7BVF;E6BEA;IAUI;IACA;;;AAEF;EACE;EACA;E7BvDJ;EACA;EACA;EACA;;;A6B0DF;EACE;EACA;E7BrGA;EACA;EACA;EACA;EACA;E6BmGA;;;A7B3BA;E6BuBF;IAMI;;;A7B7BF;E6BuBF;IASI;;;AAEF;EACE;;;AAEF;EACE;;;AAIJ;EACE;EACA;EACA;EACA;;;A7B9CA;E6B0CF;IAMI;IACA;;;A7BjDF;E6B0CF;IAUI;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E7B1IF;EACA;EACA;EACA;EACA;E6BwIE;;;A7BhEF;E6BuDA;IAWI;;;AAGJ;EACE;EACA;E7B7GF;EACA;EACA;EACA;EAzCA;EACA;EACA;EACA;EACA;;;AAwEA;E6BqEA;IAMI;IACA;;;A7B5EJ;E6BqEA;IAUI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E7BtKF;EACA;EACA;EACA;EACA;E6BoKE;EACA;EACA;EACA;EACA;;;A7BhGF;E6BmFA;IAeI;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;E7BzLF;EACA;EACA;EACA;EACA;E6BuLE;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;E7BtMJ;EACA;EACA;EACA;EACA;E6BoMI;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AR5LN;AAEA;AShCA;EACE;;;AAGF;EACE;EACA;EACA;EACA;;;AACA;EACE;;;AAKF;EACE;;;AAEF;EACE;;;ATeJ;AAEA;AUpCA;EACE;EACA;EACA;E/BGA;EACA;EACA;EACA;EACA;E+BLA;;;A/B6EA;E+BlFF;IAOI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;E/BoBF;EACA;EACA;EACA;E+BrBE;;;AAEF;EACE;EACA;EACA;;;A/BmDF;E+BtDA;IAKI;;;AAGJ;EACE;;;A/B6CF;E+B9CA;IAGI;;;AAIJ;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;A/B4BF;E+BnCA;IASI;;;A/B0BJ;E+BnCA;IAYI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;E/BzEJ;EACA;EACA;E+ByEI;;;AAKN;EACE;;;AACA;EACE;EACA;E/B/EF;EACA;EACA;EACA;EACA;E+B6EE;EACA;EACA;E/B5EF;EACA;EACA;;;AAsFA;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;A+BnBA;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E/BjGF;EACA;EACA;EACA;EACA;E+B+FE;EACA;;;AVpEJ;AAEA;AWxCA;EACE;;;AACA;EACE;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;;;AhCsEF;EgCnEA;IAEI;;;AhCiEJ;EgC/DE;IAEI;IACA;;;AAON;EACE;EhCxBF;EACA;EACA;EACA;EACA;EgCsBE;;;AhCkDF;EgCrDA;IAKI;IACA;;;AAGA;EACE;EACA;;;AhC0CN;EgC5CI;IAII;;;AhCwCR;EgC5CI;IAOI;;;AAIN;EACE;EACA;;;AhC+BJ;EgCjCE;IAII;;;AhC6BN;EgCjCE;IAOI;;;AhC0BN;EgCjCE;IAUI;;;AAIN;EACE;EACA;;;AhCiBF;EgCnBA;IAII;IACA;;;AAEF;EACE;EACA;EhClEJ;EACA;EACA;EACA;EACA;EgCgEI;;;AhCQJ;EgCZE;IAMI;IACA;;;AAEF;EACE;EACA;EhCrCN;EACA;EACA;EACA;;;AgCyCF;EACE;;;AhCPA;EgCMF;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EhCjGF;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EgC2FE;;;AhCxBF;EgCWA;IAeI;IACA;;;AAEF;EACE;;;AhC9BJ;EgC6BE;IAGI;;;AhChCN;EgCmCE;IAEI;;;AAGJ;EACE;EACA;;;AAKN;EACE;EACA;;;AhCjDA;EgC+CF;IAII;;;AAEF;EACE;EACA;EACA;EACA;EACA;EhCtIF;EACA;EACA;EACA;EACA;EgCoIE;;;AhC5DF;EgCqDA;IASI;;;AAEF;EACE;EACA;EhCxGJ;EACA;EACA;EACA;;;AgCyGA;EACE;EACA;EACA;EACA;EhCtJF;EACA;EACA;EACA;EACA;;;AAwEA;EgCsEA;IAOI;;;AAGA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;;;AAEF;EACE;EACA;;;AhChGR;EgC8FM;IAII;IACA;IACA;;;AAQZ;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EhC1JF;EACA;EACA;EACA;;;AgC4JF;EACE;EACA;EACA;EACA;EhCzMA;EACA;EACA;EACA;EACA;;;AAwEA;EgCyHF;IAOI;;;AhChIF;EgCyHF;IAUI;;;AAGA;EACE;EACA;;;AACA;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;;;AAEF;EACE;EACA;;;AhC1JN;EgCwJI;IAII;IACA;IACA;;;AAEF;EACE;;;AAMN;EACE;EACA;;;AACA;EACE;EACA;;;AAQJ;EACE;;;AACA;EACE;;;AAKJ;EACE;EACA;;;AACA;EACE;EACA;;;AXzOR;AAEA;AY5CA;EACE;EACA;;;AjCgFA;EiClFF;IAII;;;AjC8EF;EiClFF;IAOI;;;AAIJ;EACE;EACA;EACA;EjCRA;EACA;EACA;EACA;EACA;EiCMA;EjCHA;EACA;EACA;;;AAmEA;EiCvEF;IAQI;;;AAEF;EACE;;;AAEE;EACE;EACA;;;AACA;EACE;;;AAKR;EACE;EACA;EjC7BF;EACA;EACA;EACA;EACA;EiC2BE;;;AjC6CF;EiCjDA;IAMI;;;AjC2CJ;EiCjDA;IASI;;;AAEF;EACE;EACA;EjCFJ;EACA;EACA;EACA;;;AiCEE;EACE;EACA;EACA;EACA;EACA;EjCzCJ;EACA;EACA;;;AiC0CM;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAKR;EACE;EACA;EACA;EACA;;;AACA;EjChEF;EACA;EACA;;;AiCgEI;EACE;EACA;EACA;EACA;EjC7EN;EACA;EACA;EACA;EACA;EiC2EM;EACA;EACA;EACA;EjC3EN;EACA;EACA;;;AiC2EM;EACE;EACA;;;AAKR;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;;AjCzBF;EiCmBA;IAQI;;;AAGA;EACE;EACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EjCtHJ;EACA;EACA;EACA;EACA;EiCoHI;EACA;EjClHJ;EACA;EACA;;;AiCmHE;EACE;;;AjCjDJ;EiCgDE;IAGI;;;AAGJ;EACE;EACA;;;AACA;EACE;;;AAMF;EACE;EACA;;;AAMR;EACE;EACA;EACA;EACA;EACA;EACA;;;AjC9EA;EiCwEF;IAQI;;;AjChFF;EiCwEF;IAWI;;;AAGA;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EjC7KF;EACA;EACA;EACA;EACA;EiC2KE;EjCxKF;EACA;EACA;;;AAmEA;EiC0FA;IAYI;;;AAGJ;EACE;EACA;EACA;EjCxLF;EACA;EACA;EACA;EACA;;;AAwEA;EiCyGA;IAMI;IACA;;;AjChHJ;EiCyGA;IAUI;IACA;;;AAEF;EACE;EACA;EjC9JJ;EACA;EACA;EACA;;;AiC+JA;EACE;EACA;;;AjC9HF;EiC4HA;IAII;;;AjChIJ;EiC4HA;IAOI;IACA;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EjCzNF;EACA;EACA;EACA;EACA;EiCuNE;;;AACA;EACE;;;AAEE;EACE;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;;;AAQN;AAAA;EACE;;;AAEF;AAAA;EACE;;;AZnNJ;AAEA;AahDA;EACE;EACA;EACA;;;AACA;EACE;;;AlC6EF;EkC9EA;IAGI;;;AAEF;EACE;EACA;ElCiCJ;EACA;EACA;EACA;;;AkChCA;EACE;EACA;;;AlCiEF;EkCnEA;IAII;;;AlC+DJ;EkCnEA;IAOI;;;AAEF;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;ElC7BN;EACA;EACA;EACA;EACA;;;AkCgCF;EACC;;;AAGD;EACE;EACA;EACA;EACA;EACA;ElC7CA;EACA;EACA;EACA;EACA;;;AkC2CA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;ElCtDF;EACA;EACA;EACA;EACA;;;AkCqDA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ElCtEF;EACA;EACA;;;AqB+CF;AAEA;AcpDA;EACE;EACA;EACA;;;AnC+EA;EmClFF;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EnCXA;EACA;EACA;EACA;EACA;;;AmCUA;EACE;EACA;EACA;EACA;EACA;EACA;EnCpBF;EACA;EACA;EACA;EACA;EAkCA;EACA;EACA;EACA;;;AAmCA;EmC9DA;IAUI;IACA;IACA;;;AAKN;EACE;EACA;;;AdeF;AAEA;AexDA;EACE;EpCKA;EACA;EACA;EACA;EACA;EoCPA;EACA;;;AACA;EACE;EACA;EpCDF;EACA;EACA;EACA;EACA;EoCDE;;;ApCyEF;EoC7EA;IAMI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EpChBJ;EACA;EACA;EACA;EACA;EoCcI;EACA;EACA;EACA;EACA;;;ApCsDJ;EoCrEE;IAiBI;IACA;IACA;;;AAGJ;EACE;EACA;EpCOJ;EACA;EACA;EACA;;;AoCNA;EACE;;;ApCwCF;EoCzCA;IAGI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;;;AAIN;EACE;;;AAEF;EACE;;;AAGA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EpC1EJ;EACA;EACA;EACA;EACA;;;AoC8EA;EACE;;;AACA;EACE;;;AAEF;EpCvFF;EACA;EACA;EACA;EACA;EoCqFI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ApCvBJ;EoCeE;IAUI;;;AAIN;EACE;EACA;EACA;EACA;EACA;;;ApClCF;EoC6BA;IAOI;;;AAGJ;EACE;EACA;EpCrHF;EACA;EACA;EACA;EACA;;;AAwEA;EoCuCA;IAKI;;;AAGJ;EACE;;;AACA;EACE;EACA;EACA;;;AACA;EAJF;IAKI;;;AAEF;EAPF;IAQI;;;AAEF;EAVF;IAWI;;;AAIN;EACE;;;AAIJ;EACE;;;ApCtEA;EoCqEF;IAGI;;;AfhGJ;AAEA;AgB5DA;EACE;ErCKA;EACA;EACA;EACA;EACA;;;AqCPA;EACE;EACA;EACA;EACA;EACA;;;ArC0EF;EqC/EA;IAOI;;;AAGJ;EACE;;;ArCoEF;EqCrEA;IAGI;;;AAGJ;EACE;;;AAGA;EACE;;;AAEF;EACE;;;AAKN;EACE;EACA;EACA;EACA;;;AAEE;EACE;EACA;;;AAEF;EACE;EACA;ErCtCJ;EACA;EACA;EACA;EACA;;;AqCwCF;EACE;EACA;EACA;ErC/CA;EACA;EACA;EACA;EACA;;;AAwEA;EqChCF;IAMI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ArCeF;EqCxBA;IAWI;;;AAGJ;EACE;;;ArCSF;EqCVA;IAGI;;;AAGJ;EACE;EACA;EACA;;;ArCCF;EqCJA;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ArCXJ;EqCGE;IAUI;;;AAMR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ErCvGA;EACA;EACA;EACA;EACA;EqCqGA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ArCtCF;EqC8BA;IAUI;;;AAKN;EACE;EACA;EACA;EACA;;;ArCjDA;EqC6CF;IAMI;;;AAEF;EACE;EACA;EACA;EACA;EACA;ErCtIF;EACA;EACA;EACA;EACA;EqCoIE;EACA;EACA;EACA;;;ArC/DF;EqCqDA;IAYI;IACA;;;AAEF;EACE;EACA;;;AAGJ;EACE;;;AAGA;EACE;;;AAKN;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;;;ArC5FA;EqCuFF;IAOI;;;AAEF;EACE;EACA;;;AACA;ErC/KF;EACA;EACA;EACA;EACA;;;AqC+KA;EACE;EACA;;;ArCzGF;EqCuGA;IAII;IACA;IACA;;;AAKN;EACE;EACA;EACA;EACA;EACA;EACA;ErCpMA;EACA;EACA;EACA;EACA;;;AqCkMA;EACE;;;AAIJ;EACE;;;AACA;EACE;;;AACA;AAAA;EAEE;;;AAKN;EACE;EACA;;;AAGF;EACE;EACA;ErC7NA;EACA;EACA;EACA;EACA;EqC2NA;;;ArCnJA;EqC+IF;IAMI;;;AAEF;EACE;EACA;;;AAIJ;EACE;;;AACA;EACE;EACA;ErC7OF;EACA;EACA;EACA;EACA;EqC2OE;EACA;;;AAEF;EACE;EACA;EACA;ErC1PF;EACA;EACA;EqC0PE;;;AAKF;EACE;;;AAIJ;EACE;EACA;;;AACA;EACE;EACA;EACA;;;AACA;EACE;EACA;ErCpOJ;EACA;EACA;EACA;EAzCA;EACA;EACA;EACA;EACA;;;AqC2QA;EACE;EACA;;;AAEF;EACE;EACA;;;AACA;EACE;EACA;;;AAKN;EACE;ErC9RA;EACA;EACA;EACA;EACA;EqC4RA;;;AACA;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AACA;EACE;;;AAMR;EACE;EACA;EACA;;;AAEE;EACE;EACA;ErCpUJ;EACA;EACA;EACA;EACA;EqCkUI;;;AACA;EACE;EACA;;;AhBjRR;AAEA;AiB9DA;EACE;EACA;EACA;EACA;;;AACA;EtCDA;EACA;EACA;EACA;EACA;EsCDE;EACA;;;AACA;EACE;EACA;EACA;;;AAKN;EACE;EtCdA;EACA;EACA;EACA;EACA;EsCYA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EtCSF;EACA;EACA;EACA;;;AsCTA;EACE;EACA;EACA;EACA;EACA;EACA;;;AtCsCF;EsC5CA;IAQI;;;AAEF;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;;;AtC2BN;EsC9BI;IAKI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AtCeN;EsCrBI;IAQI;IACA;;;AtCYR;EsCRE;IAEI;;;AAKN;EACE;EACA;EACA;EACA;;;AtCHF;EsCDA;IAMI;;;AAGA;EACE;EACA;EACA;EACA;EtCxFN;EACA;EACA;EACA;EACA;EsCsFM;EACA;EACA;EACA;;;AAMR;EtCnGE;EACA;EACA;EACA;EACA;EsCiGA;EACA;EACA;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EtC9GF;EACA;EACA;EACA;EACA;;;AAwEA;EsC8BA;IAOI;;;AAEF;EACE;EACA;;;AtCzCJ;EsCuCE;IAII;;;AAIN;EACE;;;AtChDF;EsC+CA;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AtCzDJ;EsCoDE;IAOI;;;AAEF;EACE;EACA;EACA;EACA;;;AAEF;EACE;;;AAIN;EACE;;;AACA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AtClFJ;EsC6EE;IAOI;;;AAIN;EACE;EACA;;;AtC1FF;EsCwFA;IAII;;;AAMJ;EACE;;;AACA;EACE;EACA;;;AAKN;EACE;EACA;EtCzLA;EACA;EACA;EACA;EACA;EsCuLA;EACA;;;AACA;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EtCvMF;EACA;EACA;EACA;EACA;;;AAwEA;EsCoHA;IAUI;;;AAEF;EACE;EACA;EACA;EACA;;;AAEF;EACE;;;AAGJ;EACE;EACA;;;AACA;EACE;;;AAEF;EACE;;;AACA;EACE;EACA;;;AtCpJN;EsCkJI;IAII;;;AAGJ;EACE;;;AtC1JN;EsCyJI;IAGI;;;AAKR;EACE;EACA;;;AACA;EACE;EACA;;;AAIE;EACE;;;AjB3LV;AAEA;AkBpEA;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AvCwEF;EuC/EA;IASI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;;AvC8DF;EuC1DF;IAEI;;;AAEF;EACE;;;AAEF;EACE;EACA;;;AAEF;EACE;;;AvC8CF;EuC/CA;IAGI;;;AAKN;EACE;EACA;EACA;EACA;EACA;EvC1CA;EACA;EACA;EACA;EACA;;;AAwEA;EuCvCF;IAQI;;;AAEF;EACE;EACA;EACA;EvClDF;EACA;EACA;EACA;EACA;EuCgDE;EACA;EACA;EACA;;;AAEF;EACE;EACA;;;AvCiBF;EuCnBA;IAII;;;AvCeJ;EuCnBA;IAOI;IACA;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;;;AvCGJ;EuCPE;IAMI;;;AvCCN;EuCPE;IASI;;;AAGJ;EACE;EACA;;;AAKN;EACE;EACA;EACA;EACA;;;AvChBA;EuCYF;IAMI;;;AvClBF;EuCYF;IASI;;;AAEF;EACE;EACA;EACA;EvCtGF;EACA;EACA;EACA;EACA;EuCoGE;EACA;EACA;;;AvC9BF;EuCuBA;IASI;;;AvChCJ;EuCuBA;IAYI;;;AAGJ;EACE;;;AAEF;EACE;;;AvC1CF;EuCyCA;IAGI;;;AAGJ;EACE;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AvC1DF;EuCqDA;IAOI;;;AlBxEN;AAEA;AmBvEA;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACE;;;AAGI;ExClBJ;EACA;EACA;;;AwCoBE;EACE;EACA;;;AAGJ;EACE;EACA;ExCxBF;EACA;EACA;EACA;EACA;;;AAwEA;EwCtDA;IAKI;;;AxCiDJ;EwCtDA;IAQI;;;AxC8CJ;EwCtDA;IAWI;;;AAEF;EACE;EACA;ExCCJ;EACA;EACA;EACA;EAlCA;EACA;EACA;;;AwCiCA;EACE;EACA;EACA;EACA;EACA;EACA;ExChDF;EACA;EACA;EACA;EACA;EwC8CE;EACA;EACA;EACA;EACA;EACA;ExChDF;EACA;EACA;;;AAmEA;EwClCA;IAgBI;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;ExClEJ;EACA;EACA;EACA;EACA;EwCgEI;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;ExCjFJ;EACA;EACA;EACA;EACA;EwC+EI;EACA;ExC7EJ;EACA;EACA;;;AwC+EI;EACE;;;AAGJ;EACE;;;AxCjBJ;EwCgBE;IAGI;;;AxCnBN;EwCgBE;IAMI;;;AxCtBN;EwCgBE;IASI;;;AAGJ;EACE;EACA;EACA;;;AACA;EACE;;;AAGA;EACE;;;AACA;EACE;;;AAIN;EACE;EACA;EACA;;;AAUA;EACE;EACA;;;AAKJ;EACE;EACA;EACA;;;AACA;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAQR;EACE;;;AxCxGF;EwCuGA;IAGI;;;AAGJ;EACE;;;AxC9GF;EwC6GA;IAGI;;;AAIE;ExChMN;EACA;EACA;EACA;EACA;;;AwCmMM;ExCvMN;EACA;EACA;EACA;EACA;;;AqBgEF;AAEA;AoB5EA;EACE;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AzCuEF;EyC/EA;IAUI;;;AzCqEJ;EyC/EA;IAaI;;;AAKN;EzCfE;EACA;EACA;EACA;EACA;EyCaA;EACA;;;AACA;EACE;EACA;EzCiBF;EACA;EACA;EACA;;;AyCjBA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EzC/BF;EACA;EACA;EACA;EACA;EyC6BE;EACA;EACA;EACA;EACA;;;AzCuCF;EyCpDA;IAeI;IACA;IACA;;;ApB+BN;AAEA;AqBhFA;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;;;AAIJ;EACE;EACA;E1CTA;EACA;EACA;EACA;EACA;E0COA;;;AACA;EACE;EACA;;;AAEF;EACE;;;AAIJ;EACE;EACA;;;AACA;E1CxBA;EACA;EACA;EACA;EACA;;;A0CuBA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;E1ClCF;EACA;EACA;EACA;EACA;;;A0CmCF;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;E1CjDF;EACA;EACA;EACA;EACA;;;A0C+CE;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAGJ;EACE;E1C3BF;EACA;EACA;EACA;EAzCA;EACA;EACA;EACA;EACA;;;AqBwEF;AAEA;AsBpFA;EACE;EACA;EACA;;;AACA;EACE;EACA;EACA;EACA;E3CFF;EACA;EACA;EACA;EACA;E2CAE;EACA;EACA;EACA;;;AAEF;EACE;;;AACA;EACE;;;AAEF;EACE;;;AAKN;EACE;;;AACA;EACE;EACA;EACA;;;A3CmDF;E2CtDA;IAKI;;;A3CiDJ;E2CtDA;IAQI;;;A3C8CJ;E2CtDA;IAWI;;;AtB+CN", "file": "main.css", "sourcesContent": ["/*\n===============================================\n           CSS Table of Content\n===============================================\n1)  reset css\n2)  global css\n3)  spacing css\n4)  extend css\n5)  list-style css\n6)  animation css\n7)  typography css\n8)  preloader css\n9)  buttons css\n10) badge css\n11) table css\n12) form css\n13) card css\n14) modal css\n15) accordion css\n16) nav-tabs css\n17) pagination css\n18) header css\n19) footer css\n20) inner-banner css\n21) banner css\n22) service css\n23) service-details css\n24) ticker css\n25) about css\n26) project css\n27) project-details css\n28) message css\n29) brand css\n30) newsletter css\n31) testimonial css\n32) team css\n33) team-details css\n34) faq css\n35) blog css\n36) blog-details css\n37) pricing css\n38) overview css\n39) gallery css\n40) video css\n41) choose css\n42) contact css\n=========================================== */\n\n// abstracts sass\n@import './abstracts/_index';\n\n// base sass\n@import './base/_index';\n\n// components sass\n@import './components/_index';\n\n// layout sass \n@import './layout/_index';\n\n// sections sass \n@import './sections/_index';", "// base sass\n/* === global css start === */\n@import './base/base';\n/* === global css end === */\n\n/* === spacing css start === */\n@import './base/spacing';\n/* === spacing css end === */\n\n/* === extend css start === */\n@import './base/extend';\n/* === extend css end === */\n\n/* === list-style css start === */\n@import './base/list-style';\n/* === list-style css end === */\n\n/* === animation css start === */\n@import './base/animation';\n/* === animation css end === */\n\n/* === typography css start === */\n@import './base/typography';\n/* === typography css end === */", "/* === reset css start === */\n@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400..900&family=Roboto:wght@400;500;600;700&family=Prata&display=swap');\n\n:root {\n  --primary: #FA7D09;\n  --secondary: #FF4301;\n  --dark-700: #171931;\n  --dark-800: #4A3F35;\n  --dark-900: #0B0D26;\n  --light-100: #FFF8F3;\n  --light-200: #D0D0D0;\n  --border-color: #D0D0D0;\n\n  --display-font: \"Playfair Display\", serif;\n  --body-font: \"Roboto\", sans-serif;\n}\n\n*:where(:not(html, iframe, canvas, img, svg, video, audio):not(svg *, symbol *)) {\n  all: unset;\n  display: revert;\n}\n\n:where([hidden]) {\n  display: none;\n}\n\n:where([contenteditable]:not([contenteditable=false])) {\n  -moz-user-modify: read-write;\n  -webkit-user-modify: read-write;\n  overflow-wrap: break-word;\n  -webkit-line-break: after-white-space;\n  -webkit-user-select: auto;\n}\n\n:where([draggable=true]) {\n  -webkit-user-drag: element;\n}\n\n:where(dialog:modal) {\n  all: revert;\n}\n\n*,\n::before,\n::after {\n  box-sizing: border-box;\n  border-style: solid;\n  border-width: 0;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\nbody {\n  font-family: var(--body-font);\n  color: var(--dark-800);\n  font-size: rem(16px);\n  padding: 0;\n  margin: 0;\n  font-weight: 400;\n  position: relative;\n  line-height: 1.7;\n  background-color: #fff;\n  overflow-x: hidden;\n  @extend %transition;\n}\nmain {\n  display: block;\n}\n\nfooter {\n  margin-top: auto;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n  user-select: none;\n}\n\nselect {\n  cursor: pointer;\n}\n\ndt {\n  margin-left: 0;\n}\n\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n  border-top-width: 1px;\n  margin: 0;\n  clear: both;\n  color: inherit;\n  opacity: 0.15;\n}\n\npre {\n  font-family: monospace, monospace;\n  font-size: inherit;\n}\n\naddress {\n  font-style: inherit;\n}\n\nul,\nol {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n\nbutton {\n  cursor: pointer;\n}\n\n*:focus {\n  outline: none;\n}\n\nbutton {\n  border: none;\n}\n\nbutton:focus {\n  outline: none;\n}\n\nspan {\n  display: inline-block;\n}\n\np, li, span {\n  margin: 0;\n}\n\na {\n  text-decoration: none;\n  display: inline-block;\n  background-color: transparent;\n  color: inherit;\n}\n\na, button {\n  cursor: revert;\n}\n\na:hover {\n  text-decoration: none;\n}\n\nstrong {\n  font-weight: 700;\n}\n\na:hover {\n  color: var(--primary);\n}\n\nabbr[title] {\n  text-decoration: underline dotted;\n}\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace;\n  font-size: inherit;\n}\n\nsmall {\n  font-size: 80%;\n}\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\nsvg,\nimg,\nembed,\nobject,\niframe {\n  vertical-align: bottom;\n}\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  -webkit-appearance: none;\n  appearance: none;\n  vertical-align: middle;\n  color: inherit;\n  font: inherit;\n  background: transparent;\n  padding: 0;\n  margin: 0;\n  border-radius: 0;\n  text-align: inherit;\n  text-transform: inherit;\n}\n\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  cursor: pointer;\n  -webkit-appearance: button;\n}\n\nbutton:disabled,\n[type=button]:disabled,\n[type=reset]:disabled,\n[type=submit]:disabled {\n  cursor: default;\n}\n\n:-moz-focusring {\n  outline: auto;\n}\n\nselect:disabled {\n  opacity: inherit;\n}\n\noption {\n  padding: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n  min-width: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\ntextarea {\n  overflow: auto;\n}\n\n[type=number]::-webkit-inner-spin-button,\n[type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n[type=search] {\n  outline-offset: -2px;\n}\n\n[type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  font: inherit;\n}\n\n[type=number] {\n  -moz-appearance: textfield;\n}\n\nlabel[for] {\n  cursor: pointer;\n}\n\ndetails {\n  display: block;\n}\n\nsummary {\n  display: list-item;\n}\n\n[contenteditable]:focus {\n  outline: auto;\n}\n\ntable {\n  border-color: inherit;\n  border-collapse: collapse;\n}\n\ncaption {\n  text-align: left;\n}\n\ntd,\nth {\n  vertical-align: top;\n  padding: 0;\n}\n\nth {\n  text-align: left;\n  font-weight: bold;\n}\n/* === reset css end === */", ".text-primary {\n  color: var(--primary) !important;\n}\n.text-dark {\n  color: var(--dark-900) !important;\n}\n.text-h {\n  color: var(--dark-900) !important;\n}\n.text-p {\n  color: var(--dark-800) !important;\n}\n.text-light {\n  color: var(--light-200) !important;\n}", "/* background color css start */\n.bg-primary {\n  background-color: var(--primary) !important;\n}\n\n.bg-secondary {\n  background-color: var(--secondary) !important;\n}\n\n.bg-dark {\n  background-color: var(--dark-900) !important;\n}\n\n.bg-light {\n  background-color: var(--light-100) !important;\n}\n/* background color css end */", "@import './_reset';\n\n@import './_color';\n@import './_bg';\n\n.section-top {\n  padding-bottom: rem(60px);\n  @include media(1399px) {\n    padding-bottom: rem(50px);\n  }\n  @include media(991px) {\n    padding-bottom: rem(40px);\n  }\n}\n\n.section-top-title-two {\n  font-size: rem(16px);\n  color: var(--primary);\n  font-weight: 500;\n  text-transform: uppercase;\n  margin-bottom: rem(10px);\n}\n\n.section-title {\n  font-family: var(--display-font);\n  font-size: rem(42px);\n  font-weight: 700;\n  @include media(1399px) {\n    font-size: rem(36px);\n  }\n  @include media(991px) {\n    font-size: rem(32px);\n  }\n  @include media(575px) {\n    font-size: rem(26px);\n  }\n}\n\n.rounded-sm {\n  @include border-radius(5px);\n}", "@mixin transform($property) {\n  -webkit-transform: $property;\n  -ms-transform: $property;\n  transform: $property;\n}\n@mixin border-radius($property) {\n  border-radius: $property;\n  -webkit-border-radius: $property;\n  -moz-border-radius: $property;\n  -ms-border-radius: $property;\n  -o-border-radius: $property;\n}\n@mixin transition($property) {\n  -webkit-transition: $property;\n  -o-transition: $property;\n  transition: $property;\n}\n@mixin flexWidth($property) {\n  flex: 0 0 $property;\n  -ms-flex: 0 0 $property;\n  max-width: $property;\n}\n@mixin d-flex {\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n  flex-wrap: wrap;\n}\n@mixin position {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n@mixin positionTwo {\n  position: absolute;\n  content: '';\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n@mixin object-fit {\n  object-fit: cover;\n  -o-object-fit: cover;\n  object-position: center;\n  -o-object-position: center;\n}\n@mixin animation($property) {\n  animation: $property;\n  -webkit-animation: $property;\n  -moz-animation: $property;\n}\n@mixin font($size, $weight, $transform) {\n  font-size: $size;\n  font-weight: $weight;\n  text-transform: $transform;\n}\n@mixin text-line-1 {\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n@mixin text-line-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n@mixin text-line-3 {\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n// media query mixin\n@mixin media($value) {\n  @media (max-width: $value) {\n    @content;\n  }\n}// keyframes mixin\n@mixin keyframes($name) {\n  @-webkit-keyframes #{$name} {\n    @content;\n  }\n  @-moz-keyframes #{$name} {\n    @content;\n  }\n  @-ms-keyframes #{$name} {\n    @content;\n  }\n  @keyframes #{$name} {\n    @content;\n  }\n}\n@mixin placeholder($value) {\n  &::-webkit-input-placeholder {\n    color: $value;\n  }\n  &::-moz-placeholder {\n    color: $value;\n  }\n  &:-ms-input-placeholder {\n    color: $value;\n  }\n  &:-moz-placeholder {\n    color: $value;\n  }\n}\n@mixin color($bg-color, $text-color) {\n  background-color: $bg-color;\n  color: $text-color;\n}\n@mixin grad-one {\n  background: $base-color;\n\n  background: -webkit-linear-gradient(135deg,#ff690f 0%,#ee4719 100%);\n  background: linear-gradient(135deg,#ff690f 0%,#ee4719 100%);\n}", ".mt-30 {\n  margin-top: rem(30px);\n}\n\n.mt-40 {\n  margin-top: rem(40px);\n}\n\n.mt-60 {\n  margin-top: rem(60px);\n}\n\n.pt-120 {\n  padding-top: rem(120px);\n  @include media(1399px) {\n    padding-top: rem(100px);\n  }\n  @include media(991px) {\n    padding-top: rem(80px);\n  }\n  @include media(767px) {\n    padding-top: rem(70px);\n  }\n  @include media(575px) {\n    padding-top: rem(50px);\n  }\n}\n\n.pb-120 {\n  padding-bottom: rem(120px);\n  @include media(1399px) {\n    padding-bottom: rem(100px);\n  }\n  @include media(991px) {\n    padding-bottom: rem(80px);\n  }\n  @include media(767px) {\n    padding-bottom: rem(70px);\n  }\n  @include media(575px) {\n    padding-bottom: rem(50px);\n  }\n}\n\n.pb-100 {\n  padding-bottom: rem(100px);\n  @include media(1399px) {\n    padding-bottom: rem(80px);\n  }\n  @include media(991px) {\n    padding-bottom: rem(70px);\n  }\n  @include media(767px) {\n    padding-bottom: rem(60px);\n  }\n  @include media(575px) {\n    padding-bottom: rem(50px);\n  }\n}", "%position-relative {\n  position: relative;\n}\n%position-absolute {\n  position: absolute;\n}\n%position {\n  @include position;\n}\n%positionTwo {\n  @include positionTwo;\n}\n%z-index-p {\n  z-index: 1;\n}\n%z-index-c {\n  z-index: -1;\n}\n%transition {\n  -webkit-transition: all 0.3s;\n  -o-transition: all 0.3s;\n  transition: all 0.3s;\n}\n%base-color {\n  background-color: $base-color;\n}\n%base-color-two {\n  background-color: $base-color-two;\n}\n%bg-white {\n  background-color: #fff;\n}\n%bg-gradi {\n  @include grad-one;\n}\n%text-base {\n  color: $base-color;\n}\n%text-white {\n  color: #fff;\n}\n%text-h {\n  color: $heading-color;\n}\n%text-p {\n  color: $para-color;\n}\n%obj-fit {\n  @include object-fit;\n}\n%d-flex {\n  @include d-flex;\n}\n%justify-center {\n  justify-content: center;\n}\n%align-center {\n  align-items: center;\n}\n%d-inline-block {\n  display: inline-block;\n}\n%d-inline-flex {\n  display: inline-flex;\n}\n%overflow-hidden {\n  overflow: hidden;\n}\n%w-100 {\n  width: 100%;\n}\n%h-100 {\n  height: 100%;\n}\n%trans-y {\n  @include transform(translateY(-5px));\n}\n%text-center {\n  text-align: center;\n}\n%bs-5 {\n  @include border-radius(5px);\n}\n%bs-8 {\n  @include border-radius(8px);\n}\n%bs-10 {\n  @include border-radius(10px);\n}\n%bs-50 {\n  @include border-radius(50%);\n}", ".check-list {\n  li + li {\n    margin-top: rem(20px);\n  }\n  li {\n    position: relative;\n    padding-left: rem(30px);\n    &::before {\n      position: absolute;\n      top: rem(5px);\n      left: 0;\n      font-family: \"Font Awesome 6 Free\";\n      font-weight: 900;\n      content: \"\\f058\";\n      font-size: rem(16px);\n      color: var(--primary);\n      line-height: 1;\n    }\n  }\n}\n\n.arrow-list {\n  li + li {\n    margin-top: rem(20px);\n  }\n  li {\n    position: relative;\n    padding-left: rem(30px);\n    &::before {\n      position: absolute;\n      top: rem(5px);\n      left: 0;\n      font-family: \"Font Awesome 6 Free\";\n      font-weight: 900;\n      content: \"\\f101\";\n      font-size: rem(16px);\n      color: var(--primary);\n      line-height: 1;\n    }\n  }\n}\n\n.line-dot-list {\n  position: relative;\n  margin-top: rem(-10px);\n  li {\n    position: relative;\n    padding-left: rem(30px);\n    margin-block: rem(10px);\n    &:last-child {\n      &::after {\n        display: none;\n      }\n    }\n    &::before {\n      position: absolute;\n      content: '';\n      top: 11px;\n      left: 0;\n      width: 6px;\n      height: 6px;\n      background-color: var(--primary);\n      @include border-radius(50%);\n    }\n    &::after {\n      position: absolute;\n      content: '';\n      top: 11px;\n      left: 3px;\n      width: 1px;\n      height: 145%;\n      background-color: var(--border-color);\n      z-index: -1;\n    }\n  }\n}", ".animated {\n  -webkit-animation-duration: 1s;\n  animation-duration: 1s;\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n\n.fadeInUp {\n  -webkit-animation-name: fadeInUp;\n\n  animation-name: fadeInUp;\n}\n@include keyframes (fadeInUp) {\n  0% {\n    opacity: 0;\n\n    -webkit-transform: translateY(20px);\n\n    -ms-transform: translateY(20px);\n\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n\n    -webkit-transform: translateY(0);\n\n    -ms-transform: translateY(0);\n\n    transform: translateY(0);\n  }\n}\n\n.fadeInDown {\n  -webkit-animation-name: fadeInDown;\n  animation-name: fadeInDown;\n}\n@include keyframes (fadeInDown) {\n  0% {\n    opacity: 0;\n    -webkit-transform: translateY(-20px);\n    transform: translateY(-20px);\n  }\n\n  100% {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n.fadeInLeft {\n  -webkit-animation-name: fadeInLeft;\n\n  animation-name: fadeInLeft;\n}\n@include keyframes (fadeInLeft) {\n  0% {\n    opacity: 0;\n\n    -webkit-transform: translateX(-20px);\n\n    -ms-transform: translateX(-20px);\n\n    transform: translateX(-20px);\n  }\n  100% {\n    opacity: 1;\n\n    -webkit-transform: translateX(0);\n\n    -ms-transform: translateX(0);\n\n    transform: translateX(0);\n  }\n}\n.fadeInRight {\n  -webkit-animation-name: fadeInRight;\n\n  animation-name: fadeInRight;\n}\n@include keyframes (fadeInRight) {\n  0% {\n    opacity: 0;\n\n    -webkit-transform: translateX(20px);\n\n    -ms-transform: translateX(20px);\n\n    transform: translateX(20px);\n  }\n  100% {\n    opacity: 1;\n\n    -webkit-transform: translateX(0);\n\n    -ms-transform: translateX(0);\n\n    transform: translateX(0);\n  }\n}", "h1 {\n  font-size: rem(42px);\n  @media (max-width: 1399px) {\n    font-size: rem(36px);\n  }\n  @media (max-width: 1199px) {\n    font-size: rem(32px);\n  }\n  @media (max-width: 575px) {\n    font-size: rem(28px);\n  }\n}\nh2 {\n  font-size: rem(30px);\n  @media (max-width: 991px) {\n    font-size: rem(28px);\n  }\n  @media (max-width: 575px) {\n    font-size: rem(24px);\n  }\n}\n\nh3 {\n  font-size: rem(20px);\n}\n\nh4 {\n  font-size: rem(18px);\n}\n\nh5 {\n  font-size: rem(16px);\n}\n\nh5 {\n  font-size: rem(14px);\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-family: var(--body-font);\n  color: var(--dark-900);\n  font-weight: 500;\n  margin: 0;\n  line-height: 1.3;\n  word-break: break-word;\n}\n\nh1 > a,\nh2 > a,\nh3 > a,\nh4 > a,\nh5 > a,\nh6 > a {\n  font-family: var(--body-font);\n  color: var(--dark-900);\n  font-weight: 500;\n  @include transition(all 0.3s);\n  line-height: 1.4;\n  word-break: break-word;\n}\np,\nli,\nspan {\n  margin: 0;\n}\na {\n  text-decoration: none;\n  display: inline-block;\n  font-family: var(--body-font);\n  font-weight: 400;\n}\na:hover {\n  text-decoration: none;\n}\nstrong {\n  font-weight: 500;\n}\n.fs-18px {\n  font-size: rem(18px) !important;\n}\n.fs-16px {\n  font-size: rem(16px) !important;\n}\n.fs-15px {\n  font-size: rem(15px) !important;\n}\n.fs-14px {\n  font-size: rem(14px) !important;\n}\n.fs-12px {\n  font-size: rem(12px) !important;\n}\n.h-font {\n  font-family: var(--display-font) !important;\n}\n.p-font {\n  font-family: var(--body-font) !important;\n}\n\n.max-18ch {\n  max-width: 18ch;\n}\n\n.max-20ch {\n  max-width: 20ch;\n}\n\n.max-55ch {\n  max-width: 55ch;\n}\n\n.max-65ch {\n  max-width: 65ch;\n}", "/* === preloader css start === */\n@import 'preloader';\n/* === preloader css end === */\n\n/* === buttons css start === */\n@import 'buttons';\n/* === buttons css end === */\n\n/* === badge css start === */\n@import 'badge';\n/* === badge css end === */\n\n/* === table css start === */\n@import 'table';\n/* === table css end === */\n\n/* === form css start === */\n@import 'form';\n/* === form css end === */\n\n/* === card css start === */\n@import 'card';\n/* === card css end === */\n\n/* === modal css start === */\n@import 'modal';\n/* === modal css end === */\n\n/* === accordion css start === */\n@import 'accordion';\n/* === accordion css end === */\n\n/* === nav-tabs css start === */\n@import 'nav-tabs';\n/* === nav-tabs css end === */\n\n/* === pagination css start === */\n@import 'pagination';\n/* === pagination css end === */", "body {\r\n  &.loaded {\r\n    .preloader-holder {\r\n      opacity: 0;\r\n      visibility: hidden;\r\n    }\r\n  }\r\n}\r\n\r\n.preloader-holder {\r\n  position: fixed;\r\n  left: 0px;\r\n  top: 0px;\r\n  bottom: 0px;\r\n  right: 0px;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #fff;\r\n  z-index: 9999;\r\n  @include transition(all 0.3s);\r\n}\r\n\r\n.preloader {\r\n  width: 100px;\r\n  height: 100px;\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 50%;\r\n  transform: translateX(-50%) translateY(-50%);\r\n  animation: rotatePreloader 2s infinite ease-in;\r\n}\r\n\r\n.preloader div {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  opacity: 0;\r\n}\r\n\r\n.preloader div:before {\r\n  content: \"\";\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 0%;\r\n  width: 10%;\r\n  height: 10%;\r\n  transform: translateX(-50%);\r\n  border-radius: 50%;\r\n  background-color: var(--primary);\r\n}\r\n\r\n.preloader div:nth-child(1) {\r\n  transform: rotateZ(0deg);\r\n  animation: rotateCircle1 2s infinite linear;\r\n  z-index: 9;\r\n}\r\n\r\n.preloader div:nth-child(2) {\r\n  transform: rotateZ(36deg);\r\n  animation: rotateCircle2 2s infinite linear;\r\n  z-index: 8;\r\n}\r\n\r\n.preloader div:nth-child(3) {\r\n  transform: rotateZ(72deg);\r\n  animation: rotateCircle3 2s infinite linear;\r\n  z-index: 7;\r\n}\r\n\r\n.preloader div:nth-child(4) {\r\n  transform: rotateZ(108deg);\r\n  animation: rotateCircle4 2s infinite linear;\r\n  z-index: 6;\r\n}\r\n\r\n.preloader div:nth-child(5) {\r\n  transform: rotateZ(144deg);\r\n  animation: rotateCircle5 2s infinite linear;\r\n  z-index: 5;\r\n}\r\n\r\n.preloader div:nth-child(6) {\r\n  transform: rotateZ(180deg);\r\n  animation: rotateCircle6 2s infinite linear;\r\n  z-index: 4;\r\n}\r\n\r\n.preloader div:nth-child(7) {\r\n  transform: rotateZ(216deg);\r\n  animation: rotateCircle7 2s infinite linear;\r\n  z-index: 3;\r\n}\r\n\r\n.preloader div:nth-child(8) {\r\n  transform: rotateZ(252deg);\r\n  animation: rotateCircle8 2s infinite linear;\r\n  z-index: 2;\r\n}\r\n\r\n.preloader div:nth-child(9) {\r\n  transform: rotateZ(288deg);\r\n  animation: rotateCircle9 2s infinite linear;\r\n  z-index: 1;\r\n}\r\n\r\n.preloader div:nth-child(10) {\r\n  transform: rotateZ(324deg);\r\n  animation: rotateCircle10 2s infinite linear;\r\n  z-index: 0;\r\n}\r\n\r\n@keyframes rotatePreloader {\r\n  0% {\r\n      transform: translateX(-50%) translateY(-50%) rotateZ(0deg);\r\n  }\r\n\r\n  100% {\r\n      transform: translateX(-50%) translateY(-50%) rotateZ(-360deg);\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle1 {\r\n  0% {\r\n      opacity: 0;\r\n  }\r\n\r\n  0% {\r\n      opacity: 1;\r\n      transform: rotateZ(36deg);\r\n  }\r\n\r\n  7% {\r\n      transform: rotateZ(0deg);\r\n  }\r\n\r\n  57% {\r\n      transform: rotateZ(0deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle2 {\r\n  5% {\r\n      opacity: 0;\r\n  }\r\n\r\n  5.0001% {\r\n      opacity: 1;\r\n      transform: rotateZ(0deg);\r\n  }\r\n\r\n  12% {\r\n      transform: rotateZ(-36deg);\r\n  }\r\n\r\n  62% {\r\n      transform: rotateZ(-36deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle3 {\r\n  10% {\r\n      opacity: 0;\r\n  }\r\n\r\n  10.0002% {\r\n      opacity: 1;\r\n      transform: rotateZ(-36deg);\r\n  }\r\n\r\n  17% {\r\n      transform: rotateZ(-72deg);\r\n  }\r\n\r\n  67% {\r\n      transform: rotateZ(-72deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle4 {\r\n  15% {\r\n      opacity: 0;\r\n  }\r\n\r\n  15.0003% {\r\n      opacity: 1;\r\n      transform: rotateZ(-72deg);\r\n  }\r\n\r\n  22% {\r\n      transform: rotateZ(-108deg);\r\n  }\r\n\r\n  72% {\r\n      transform: rotateZ(-108deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle5 {\r\n  20% {\r\n      opacity: 0;\r\n  }\r\n\r\n  20.0004% {\r\n      opacity: 1;\r\n      transform: rotateZ(-108deg);\r\n  }\r\n\r\n  27% {\r\n      transform: rotateZ(-144deg);\r\n  }\r\n\r\n  77% {\r\n      transform: rotateZ(-144deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle6 {\r\n  25% {\r\n      opacity: 0;\r\n  }\r\n\r\n  25.0005% {\r\n      opacity: 1;\r\n      transform: rotateZ(-144deg);\r\n  }\r\n\r\n  32% {\r\n      transform: rotateZ(-180deg);\r\n  }\r\n\r\n  82% {\r\n      transform: rotateZ(-180deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle7 {\r\n  30% {\r\n      opacity: 0;\r\n  }\r\n\r\n  30.0006% {\r\n      opacity: 1;\r\n      transform: rotateZ(-180deg);\r\n  }\r\n\r\n  37% {\r\n      transform: rotateZ(-216deg);\r\n  }\r\n\r\n  87% {\r\n      transform: rotateZ(-216deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle8 {\r\n  35% {\r\n      opacity: 0;\r\n  }\r\n\r\n  35.0007% {\r\n      opacity: 1;\r\n      transform: rotateZ(-216deg);\r\n  }\r\n\r\n  42% {\r\n      transform: rotateZ(-252deg);\r\n  }\r\n\r\n  92% {\r\n      transform: rotateZ(-252deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle9 {\r\n  40% {\r\n      opacity: 0;\r\n  }\r\n\r\n  40.0008% {\r\n      opacity: 1;\r\n      transform: rotateZ(-252deg);\r\n  }\r\n\r\n  47% {\r\n      transform: rotateZ(-288deg);\r\n  }\r\n\r\n  97% {\r\n      transform: rotateZ(-288deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes rotateCircle10 {\r\n  45% {\r\n      opacity: 0;\r\n  }\r\n\r\n  45.0009% {\r\n      opacity: 1;\r\n      transform: rotateZ(-288deg);\r\n  }\r\n\r\n  52% {\r\n      transform: rotateZ(-324deg);\r\n  }\r\n\r\n  102% {\r\n      transform: rotateZ(-324deg);\r\n  }\r\n\r\n  100% {\r\n      transform: rotateZ(-324deg);\r\n      opacity: 1;\r\n  }\r\n}\r\n\r\n.scroll-to-top {\r\n  position: fixed;\r\n  bottom: 30px;\r\n  inset-inline-end: 30px;\r\n  z-index: 9;\r\n  width: 55px;\r\n  height: 55px;\r\n  line-height: 55px;\r\n  text-align: center;\r\n  display: none;\r\n  color: #fff;\r\n  font-size: 1.25rem;\r\n  cursor: pointer;\r\n  background: var(--primary);\r\n  @include border-radius(50%);\r\n}", "\nbutton {\n  &:focus {\n    outline: none;\n  }\n}\n\n.btn {\n  padding: rem(17px) rem(30px);\n  font-weight: 500;\n  text-transform: uppercase;\n  @include media(1399px) {\n    padding: rem(15px) rem(30px);\n  }\n  @include media(1199px) {\n    padding: rem(12px) rem(30px);\n  }\n  @include media(575px) {\n    padding: rem(10px) rem(25px);\n  }\n  &:focus {\n    box-shadow: none;\n    outline: none;\n  }\n}\n\n.btn-primary {\n  background-color: var(--primary);\n  border-color: var(--primary);\n  &:hover,\n  &:focus {\n    border-color: var(--primary);\n    background-color: var(--primary);\n  }\n}\n\n.btn-outline-primary {\n  color: var(--dark-900);\n  border-color: var(--primary);\n  &:hover,\n  &:focus {\n    background-color: var(--primary);\n    border-color: var(--primary);\n  }\n}\n\n.btn-outline-secondary-primary {\n  color: var(--dark-900);\n  border-color: var(--border-color);\n  &:hover,\n  &:focus {\n    background-color: var(--primary);\n    border-color: var(--primary);\n    color: #fff;\n  }\n}\n\n[class*=\"btn\"] {\n  &.btn-md {\n    padding: rem(10px) rem(20px);\n    font-size: rem(15px);\n  }\n  &.btn-sm {\n    padding: rem(6px) rem(10px);\n    font-size: rem(14px);\n  }\n}\n", "// Add badges class name and color through sass loop\n$colors: \"primary\" $primary, \"secondary\" $secondary, \"success\" $success, \"danger\" $danger, \"warning\" $warning, \"info\" $info, \"light\" $light, \"dark\" $dark, \"base\" $base-color;\n\n@each $name, $color in $colors {\n  .badge-#{$name} {\n    background-color: rgba($color, 0.15);\n    border: 1px solid $color;\n    color: $color;\n  }\n}", "\n.table {\n  thead {\n    background-color: #f6f9fc;\n    th {\n      border-top: 1px solid #e9ecef !important;\n      border-bottom: 1px solid #e9ecef !important;\n      padding: rem(12px) rem(20px);\n      color: #7991ad;\n      font-size: rem(14px);\n      text-transform: uppercase;\n      text-align: center;\n      font-weight: 500;\n      vertical-align: middle;\n      &:first-child {\n        text-align: left;\n      }\n      &:last-child {\n        text-align: right;\n      }\n    }\n  }\n  tbody {\n    td {\n      border-top: none;\n      border-bottom: 1px solid rgba(#000, 0.08);\n      padding: 15px 20px;\n      color: $para-color;\n      text-align: center;\n      vertical-align: middle;\n      &:first-child {\n        text-align: left;\n      }\n      &:last-child {\n        text-align: right;\n      }\n    }\n    tr {\n      &:last-child {\n        td {\n          border-bottom: none;\n        }\n      }\n    }\n  }\n}\n\n// table responsive\n[data-label] {\n  position: relative;\n  &::before {\n    position: absolute;\n    content: attr(data-label);\n    font-weight: 700;\n    @extend %text-h;\n    top: 0;\n    left: 0;\n    padding: rem(13px) rem(15px);\n    display: none;\n    font-size: rem(14px);\n  }\n}\n.table-responsive--md {\n  @include media(991px) {\n    thead {\n      display: none;\n    }\n    tbody {\n      tr {\n        &:nth-child(odd) {\n          background-color: darken(#fff, 4%);\n        }\n        &:last-child {\n          td {\n            border-top: 1px solid rgba(#000, 0.08);\n          }\n        }\n        td {\n          padding-right: 15px;\n          &:last-child {\n            padding-right: 15px;\n          }\n        }\n      }\n    }\n    tr {\n      th,\n      td {\n        display: block;\n        padding-left: 45% !important;\n        text-align: right !important;\n        &:first-child {\n          border-top: none !important;\n        }\n      }\n    }\n    [data-label] {\n      &::before {\n        display: block;\n      }\n    }\n  }\n}\n.table-responsive--sm {\n  @include media(767px) {\n    thead {\n      display: none;\n    }\n    tbody {\n      tr {\n        &:nth-child(odd) {\n          background-color: f7f7f7;\n        }\n        td {\n          padding-right: 15px;\n          &:last-child {\n            padding-right: 15px;\n          }\n        }\n      }\n    }\n    tr {\n      th,\n      td {\n        display: block;\n        padding-left: 45% !important;\n        text-align: right !important;\n        border-top: 1px solid rgba(#000000, 0.08) !important;\n        &:first-child {\n          border-top: none !important;\n        }\n      }\n    }\n    [data-label] {\n      &::before {\n        display: block;\n      }\n    }\n  }\n}", "\n.form-control {\n  padding: rem(16px) rem(20px);\n  border: 1px solid var(--border-color);\n  width: 100%;\n  background-color: transparent;\n  @include border-radius(30px);\n  height: rem(58px);\n  @include placeholder(var(--dark-800));\n  &:focus {\n    background-color: #fff;\n    border-color: var(--primary) !important;\n    box-shadow: 0 0 5px rgba(var(--primary), 0.35);\n    color: #000;\n  }\n  &[readonly] {\n    background-color: #fafaf7;\n  }\n  &.form-control-md {\n    height: 45px;\n  }\n  &.form-control-sm {\n    height: 35px;\n  }\n}\n\n.form-select {\n  padding: rem(16px) rem(20px);\n  width: 100%;\n  border: 1px solid form;\n  cursor: pointer;\n  background-color: transparent;\n  height: rem(58px);\n  @include border-radius(30px);\n  color: var(--dark-800);\n  &:focus {\n    background-color: #fff;\n    border-color: var(--primary);\n    box-shadow: none;\n  }\n  &.select-sm {\n    height: rem(35px);\n    font-size: rem(14px);\n    padding: rem(5px);\n  }\n}\n\ntextarea {\n  min-height: rem(130px) !important;\n  resize: none;\n  width: 100%;\n}\n\nlabel {\n  color: #000;\n  margin-bottom: rem(10px);\n  font-size: rem(15px);\n  font-weight: 500;\n}\n.input-group > .form-control,\n.input-group > .select {\n  position: relative;\n  flex: 1 1 auto;\n  width: 1%;\n  min-width: 0;\n}\n.input-group {\n  select {\n    background-color: transparent;\n    border: none;\n  }\n}\n.custom-radio {\n  position: relative;\n  padding-left: 0;\n  input[type=radio] {\n    width: 100%;\n    position: absolute;\n    top: 0;\n    left: 0;\n    visibility: hidden;\n    cursor: pointer;\n    &:checked {\n      ~ label {\n        &::before {\n          border-width: 2px;\n          border-color: $base-color;\n        }\n        &::after {\n          opacity: 1;\n        }\n      }\n    }\n  }\n  label {\n    margin-bottom: 0;\n    position: relative;\n    padding-left: 20px;\n    font-size: rem(14px);\n    font-weight: 400;\n    &::before {\n      position: absolute;\n      content: '';\n      top: 4px;\n      left: 0;\n      width: 15px;\n      height: 15px;\n      border: 1px solid #888888;\n      @include border-radius(50%);\n      @include transition(all 0.3s);\n    }\n    &::after {\n      position: absolute;\n      content: '';\n      top: 8px;\n      left: 4px;\n      width: 7px;\n      height: 7px;\n      background-color: $base-color;\n      @include border-radius(50%);\n      opacity: 0;\n      @include transition(all 0.3s);\n    }\n  }\n}\n.custom-checkbox {\n  padding-left: rem(25px);\n  input {\n    display: none;\n    &:checked {\n      ~ label {\n        &::before {\n          content: \"\\f14a\";\n          color: $base-color;\n        }\n      }\n    }\n  }\n  label {\n    position: relative;\n    font-size: rem(15px);\n    font-weight: 400;\n    cursor: pointer;\n    margin-bottom: 0;\n    &::before {\n      position: absolute;\n      content: \"\\f04d\";\n      font-family: 'Line Awesome Free';\n      font-weight: 900;\n      top: 3px;\n      left: rem(-25px);\n      font-size: rem(20px);\n      line-height: 1;\n      @extend %transition;\n    }\n  }\n}\n", "\n.card {\n  box-shadow: 0 3px 15px #8898aa26;\n  @extend %bg-white;\n  // border: 1px solid $border-color\n  @extend %bs-8;\n  overflow: hidden;\n  .card-header {\n    background-color: #fff;\n    padding: rem(15px) rem(20px);\n    border-color: $border-color;\n  }\n  .card-body {\n    padding: rem(20px);\n    @include media(575px) {\n      padding: rem(15px);\n    }\n  }\n}", "\n.modal {\n  z-index: 99;\n}\n.modal-open {\n  overflow: hidden;\n  overflow-y: auto;\n  padding-right: 0 !important;\n}\n.btn-close {\n  width: 18px;\n  height: 18px;\n  background-color: $danger;\n  opacity: 1;\n  @extend %d-flex;\n  @extend %align-center;\n  @extend %justify-center;\n  &:hover {\n    opacity: 1;\n  }\n  &:focus {\n    box-shadow: none;\n  }\n}\n.modal {\n  &.fade {\n    .modal-dialog {\n      transform: scale(0.85, 0.85) translate(0);\n    }\n  }\n  &.show {\n    .modal-dialog {\n      transform: scale(1, 1) translate(0);\n    }\n  }\n}\n", ".accordion-item {\r\n  box-shadow: 0 4px 20px -1px rgba(#131022, 0.05);\r\n  @include border-radius(30px !important);\r\n  border: none;\r\n  --bs-accordion-btn-icon-transform: rotate(90deg);\r\n  & + & {\r\n    margin-top: rem(30px);\r\n  }\r\n  .accordion-header {\r\n    .accordion-button {\r\n      @include border-radius(30px);\r\n      font-size: rem(20px);\r\n      padding: rem(15px) rem(30px);\r\n      @include media(1199px) {\r\n        font-size: rem(18px);\r\n      }\r\n      @include media(575px) {\r\n        font-size: rem(16px);\r\n      }\r\n      &:not(.collapsed) {\r\n        background-color: var(--primary);\r\n        color: #fff;\r\n        @include border-radius(30px 30px 0 0);\r\n      }\r\n      &:focus {\r\n        box-shadow: none;\r\n      }\r\n      &::after {\r\n        background-image: none;\r\n        content: \"\\f054\";\r\n        font-family: \"Font Awesome 6 Free\";\r\n        font-weight: 900;\r\n      }\r\n    }\r\n  }\r\n  .accordion-body {\r\n    padding: rem(20px) rem(30px);\r\n  }\r\n}\r\n\r\n.accordion {\r\n  &.style-pill {\r\n    .accordion-item {\r\n      box-shadow: none;\r\n      background-color: transparent;\r\n      overflow: hidden;\r\n      .accordion-header {\r\n        .accordion-button {\r\n          background-color: transparent;\r\n          box-shadow: none;\r\n          border: 1px solid var(--border-color);\r\n          @include border-radius(30px);\r\n          padding: rem(10px) rem(10px) rem(10px) rem(30px);\r\n          @include media(1399px) {\r\n            font-size: rem(18px);\r\n          }\r\n          @include media(575px) {\r\n            font-size: rem(16px);\r\n            padding-inline-start: rem(20px);\r\n          }\r\n          &::after {\r\n            content: \"\\2b\";\r\n            width: rem(40px);\r\n            height: rem(40px);\r\n            background-color: #fff;\r\n            @include border-radius(50%);\r\n            display: inline-flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            font-size: rem(18px);\r\n            @include media(575px) {\r\n              width: rem(30px);\r\n              height: rem(30px);\r\n              font-size: rem(16px);\r\n            }\r\n          }\r\n          &:not(.collapsed) {\r\n            color: var(--dark-900);\r\n            background-color: #fff;\r\n            @include border-radius(30px 30px 0 0);\r\n            border-color: transparent;\r\n            &::after {\r\n              content: \"\\f068\";\r\n              transform: rotate(0deg);\r\n              background-color: var(--primary);\r\n              color: #fff;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .accordion-collapse {\r\n        background-color: #fff;\r\n      }\r\n      .accordion-body {\r\n        padding-top: rem(10px);\r\n      }\r\n    }\r\n  }\r\n}", ".basic-tabs {\n  border-bottom: none;\n  .nav-item {\n    .nav-link {\n      border: none;\n      padding: rem(10px) rem(20px);\n      font-weight: 800;\n      color: var(--dark-900);\n      font-size: rem(18px);\n      &.active {\n        color: var(--primary);\n      }\n    }\n  }\n}", ".pagination {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n  gap: rem(15px);\n  margin-top: rem(80px);\n  @include media(991px) {\n    margin-top: rem(50px);\n  }\n  li {\n    width: rem(60px);\n    height: rem(60px);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    border: 1px solid var(--border-color);\n    @include border-radius(5px);\n    color: var(--dark-900);\n    font-weight: 700;\n    font-size: rem(30px);\n    @include media(991px) {\n      width: 40px;\n      height: 40px;\n      font-size: rem(20px);\n    }\n    &.active {\n      border-color: var(--primary);\n    }\n    a {\n      width: 100%;\n      height: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      font-weight: 700;\n    }\n  }\n}", "/* === header css start === */ \n@import 'header';\n/* === header css end === */ \n\n/* === footer css start === */ \n@import 'footer';\n/* === footer css end === */ \n\n/* === inner-banner css start === */ \n@import 'inner-banner';\n/* === inner-banner css end === */ ", ".header-one,\r\n.header-two,\r\n.header-three {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  z-index: 2;\r\n}\r\n\r\n.site-header {\r\n  &.site-header-fixed {\r\n    position: fixed;\r\n    box-shadow: 0 5px 5px rgba(#000, 0.1);\r\n    .header-top {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.header-one {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .header-right {\r\n    flex-grow: 1;\r\n  }\r\n  .header-top {\r\n    padding-inline: rem(60px);\r\n    @include media(1399px) {\r\n      padding-inline: rem(30px);\r\n    }\r\n  }\r\n  .header-bottom {\r\n    padding: rem(20px) rem(60px);\r\n    @include media(1399px) {\r\n      padding: rem(10px) rem(30px);\r\n    }\r\n    @include media(575px) {\r\n      padding: rem(5px) rem(15px);\r\n    }\r\n  }\r\n}\r\n\r\n.header-two {\r\n  .header-call-btn {\r\n    margin-inline-start: rem(100px);\r\n    @include media(1399px) {\r\n      margin-inline-start: rem(40px);\r\n    }\r\n    @include media(1199px) {\r\n      padding-inline-start: rem(60px);\r\n    }\r\n    @include media(575px) {\r\n      padding-inline-start: 0;\r\n      margin-inline-start: rem(15px);\r\n    }\r\n    i {\r\n      @include media(1199px) {\r\n        border-color: var(--border-color) !important;\r\n        color: var(--dark-900) !important;\r\n        width: rem(50px);\r\n        height: rem(50px);\r\n        margin-top: rem(-25px);\r\n      }\r\n      @include media(575px) {\r\n        position: static;\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    span {\r\n      @include media(1199px) {\r\n        color: var(--dark-900) !important;\r\n      }\r\n      @include media(575px) {\r\n        display: none;\r\n      }\r\n    }\r\n    \r\n  }\r\n  .header-bottom {\r\n    background-color: var(--primary);\r\n    position: relative;\r\n    z-index: 1;\r\n    @include media(1199px) {\r\n      background-color: #fff;\r\n    }\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 0;\r\n      left: 0;\r\n      width: 33vw;\r\n      height: 100%;\r\n      background-color: #fff;\r\n      z-index: -1;\r\n      clip-path: polygon(0 0, 100% 0%, 88% 100%, 0% 100%);\r\n      @include media(1550px) {\r\n        width: 30vw;\r\n      }\r\n      @include media(1399px) {\r\n        width: 25vw;\r\n      }\r\n    }\r\n  }\r\n  .header-menu {\r\n    > li {\r\n      &.has-dropmenu {\r\n        &::after {\r\n          color: #fff;\r\n        }\r\n      }\r\n      > a {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.header-three {\r\n  .header-top {\r\n    background-color: var(--primary);\r\n  }\r\n  .header-bottom {\r\n    background-color: var(--dark-900);\r\n  }\r\n  .header-info {\r\n    li {\r\n      color: #fff;\r\n      i {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n  .header-social {\r\n    li {\r\n      span {\r\n        color: #fff;\r\n      }\r\n      a {\r\n        color: #fff;\r\n        &:hover {\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .header-menu {\r\n    >li {\r\n      &.has-dropmenu {\r\n        &::after {\r\n          color: #fff;\r\n        }\r\n      }\r\n      >a {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n  .header-bottom-action {\r\n    margin-inline-start: rem(60px);\r\n    @include media(1199px) {\r\n      margin-inline-start: rem(40px);\r\n    }\r\n    @include media(767px) {\r\n      margin-inline-start: rem(20px);\r\n    }\r\n    .header-search-btn {\r\n      @include media(575px) {\r\n        padding-inline-end: 0;\r\n      }\r\n    }\r\n  }\r\n  .header-search-btn,\r\n  .btn-outline-primary,\r\n  .mobile-menu-open-btn {\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.bg-header-logo {\r\n  width: 315px;\r\n  background-color: var(--primary);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  @include media(1399px) {\r\n    width: 250px;\r\n  }\r\n  @include media(767px) {\r\n    width: 200px;\r\n  }\r\n  @include media(575px) {\r\n    width: 120px;\r\n  }\r\n  img {\r\n    max-width: 280px;\r\n    @include media(1399px) {\r\n      max-width: 220px;\r\n    }\r\n    @include media(767px) {\r\n      max-width: 170px;\r\n    }\r\n    @include media(575px) {\r\n      max-width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n.header-top {\r\n  background-color: #fff;\r\n  padding: rem(13px) 0;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: rem(10px);\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  @include media(1199px) {\r\n    display: none;\r\n  }\r\n}\r\n.header-info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(30px);\r\n  li {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: rem(10px);\r\n    font-size: rem(14px);\r\n    color: var(--dark-900);\r\n    i {\r\n      color: var(--primary);\r\n    }\r\n  }\r\n}\r\n.header-social {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(15px);\r\n  li {\r\n    font-size: rem(14px);\r\n    color: var(--dark-900);\r\n    a {\r\n      &:hover {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.header-bottom {\r\n  background-color: var(--light-100);\r\n  padding: rem(20px) 0;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: rem(30px);\r\n  @include media(1199px) {\r\n    padding: rem(10px) 0;\r\n  }\r\n}\r\n\r\n.header-menu {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin: rem(-10px) rem(-20px);\r\n  @include media(1199px) {\r\n    display: none;\r\n  }\r\n  li {\r\n    &.has-dropmenu {\r\n      position: relative;\r\n      &::after {\r\n        position: absolute;\r\n        content: \"\\f078\";\r\n        font-family: \"Font Awesome 6 Free\";\r\n        font-weight: 900;\r\n        top: 12px;\r\n        right: 2px;\r\n        font-size: 13px;\r\n      }\r\n      &:hover {\r\n        .dropmenu {\r\n          top: 100%;\r\n          opacity: 1;\r\n          visibility: visible;\r\n        }\r\n      }\r\n    }\r\n    a {\r\n      color: var(--dark-900);\r\n      font-weight: 500;\r\n      padding: rem(10px) rem(20px);\r\n    }\r\n  }\r\n  .dropmenu {\r\n    position: absolute;\r\n    top: 110%;\r\n    left: 0;\r\n    background-color: #fff;\r\n    width: 220px;\r\n    box-shadow: 0 5px 10px rgba(#000, 0.1);\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    @include transition(all 0.3s);\r\n    li {\r\n      border-bottom: 1px solid #e5e5e5;\r\n      a {\r\n        @include transition(all 0.3s);\r\n        color: var(--dark-800);\r\n        &:hover {\r\n          color: var(--primary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.header-bottom-action {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(20px);\r\n  @include media(1199px) {\r\n    gap: rem(8px);\r\n  }\r\n  @include media(575px) {\r\n    gap: rem(0px);\r\n  }\r\n  .btn {\r\n    padding: rem(14px) rem(25px);\r\n    @include media(575px) {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.header-search-btn {\r\n  color: var(--dark-900);\r\n  font-size: rem(18px);\r\n  padding: rem(15px);\r\n}\r\n\r\n.header-call-btn {\r\n  position: relative;\r\n  display: inline-flex;\r\n  flex-flow: column;\r\n  justify-content: center;\r\n  min-height: rem(60px);\r\n  padding-inline-start: rem(75px);\r\n  &:hover {\r\n    i {\r\n      background-color: var(--primary);\r\n      color: #fff;\r\n    }\r\n  }\r\n  i {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 0;\r\n    margin-top: rem(-30px);\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(50%);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: rem(20px);\r\n    transition: all 0.3s;\r\n  }\r\n  span {\r\n    font-size: rem(14px);\r\n    display: block;\r\n    color: #fff;\r\n    &.header-call-btn-number {\r\n      font-size: rem(18px);\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  &.style-white {\r\n    &:hover {\r\n      i {\r\n        background-color: #fff;\r\n        color: var(--primary);\r\n      }\r\n    }\r\n    i {\r\n      border-color: #fff;\r\n      color: #fff;\r\n    }\r\n    span {\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n.mobile-menu-open-btn {\r\n  color: var(--dark-900);\r\n  font-weight: 500;\r\n  padding: rem(10px) 0;\r\n  display: none;\r\n  @include media(1199px) {\r\n    display: inline-flex;\r\n    gap: 6px;\r\n    align-items: center;\r\n  }\r\n  span {\r\n    @include media(575px) {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.header-search-main {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  @include transform(translate(-50%, -50%));\r\n  width: 100%;\r\n  height: 0;\r\n  background-color: var(--light-100);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  @include transition(all 0.5s);\r\n  z-index: 9;\r\n  &.active {\r\n    height: 100vh;\r\n    opacity: 1;\r\n    visibility: visible;\r\n  }\r\n  .header-search-close-btn {\r\n    position: absolute;\r\n    top: 30px;\r\n    right: 50px;\r\n    @include transform(translateX(-50%));\r\n    width: rem(35px);\r\n    height: rem(35px);\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(50%);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    &:hover {\r\n      color: var(--primary);\r\n    }\r\n    @include media(575px) {\r\n      right: 30px;\r\n    }\r\n  }\r\n  .header-search-form {\r\n    display: flex;\r\n    width: 500px;\r\n    @include media(575px) {\r\n      width: 300px;\r\n    }\r\n    input {\r\n      height: 52px;\r\n      border: 1px solid var(--border-color);\r\n      border-right: none;\r\n      flex-grow: 1;\r\n      @include transition(all 0.3s);\r\n      padding: rem(10px) rem(20px);\r\n      &:focus {\r\n        border-color: var(--primary);\r\n      }\r\n    }\r\n    button {\r\n      width: 50px;\r\n      height: 52px;\r\n      background-color: var(--primary);\r\n      color: #fff;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n  }\r\n}\r\n\r\n.mobile-menu-sidebar {\r\n  position: fixed;\r\n  inset-block-start: 0;\r\n  inset-inline-end: -350px;\r\n  width: 320px;\r\n  height: 100vh;\r\n  background-color: var(--dark-900);\r\n  display: flex;\r\n  flex-flow: column;\r\n  @include transition(all 0.3s);\r\n  padding-top: 75px;\r\n  padding-bottom: 50px;\r\n  overflow-y: auto;\r\n  z-index: 9;\r\n  @include media(575px) {\r\n    inset-inline-end: -105%;\r\n    width: 100%;\r\n  }\r\n  &.active {\r\n    inset-inline-end: 0;\r\n  }\r\n  .logo-area {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 75px;\r\n    padding: rem(5px) rem(20px);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    background-color: var(--primary);\r\n    img {\r\n      max-width: 180px;\r\n    }\r\n    .mobile-menu-close-btn {\r\n      color: #fff;\r\n      width: 30px;\r\n      height: 30px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: 1px solid rgba(#ffff, 0.1);\r\n      @include border-radius(50%);\r\n    }\r\n  }\r\n  .mobile-menu-bottom {\r\n    padding: rem(20px);\r\n    margin-top: auto;\r\n  }\r\n\r\n  .header-info {\r\n    display: flex;\r\n    flex-flow: column;\r\n    gap: rem(10px);\r\n    align-items: flex-start;\r\n    li {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: rem(10px);\r\n      font-size: rem(14px);\r\n      color: var(--light-100);\r\n      i {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n  .header-social {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: rem(15px);\r\n    margin-top: rem(15px);\r\n    li {\r\n      font-size: rem(14px);\r\n      color: var(--light-100);\r\n      a {\r\n        &:hover {\r\n          color: var(--primary);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.mobile-menu {\r\n  li {\r\n    border-bottom: 1px solid rgba(#fff, 0.1);\r\n    a {\r\n      padding: rem(8px) rem(20px);\r\n      color: var(--light-100);\r\n      position: relative;\r\n      display: block;\r\n      .dropmenu-arrow {\r\n        position: absolute;\r\n        width: 40px;\r\n        height: 100%;\r\n        inset-inline-end: 0;\r\n        inset-block-start: 0;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-left: 1px solid rgba(#fff, 0.1);\r\n      }\r\n    }\r\n    .dropmenu {\r\n      display: none;\r\n      background-color: rgba(#fff, 0.05);\r\n      padding: rem(5px) rem(10px) rem(5px) rem(20px);\r\n      li {\r\n        border-bottom: none;\r\n        a {\r\n          font-size: rem(14px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", ".footer-top {\r\n  padding: rem(80px) 0;\r\n  background-color: var(--dark-900);\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n  @include media(575px) {\r\n    padding: rem(60px) 0;\r\n  }\r\n  .footer-logo {\r\n    margin-bottom: rem(30px);\r\n  }\r\n  .footer-info-list {\r\n    margin-top: rem(20px);\r\n  }\r\n  .footer-top-line {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    max-inline-size: initial;\r\n    max-block-size: initial;\r\n    opacity: 0.4;\r\n    z-index: -1;\r\n  }\r\n}\r\n\r\n.footer-info-list {\r\n  li + li {\r\n    margin-top: rem(20px);\r\n  }\r\n  li {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    &:hover {\r\n      .icon {\r\n        background-color: var(--primary);\r\n      }\r\n    }\r\n    .icon {\r\n      width: rem(40px);\r\n      height: rem(40px);\r\n      background-color: rgba(#fff, 0.1);\r\n      @include border-radius(5px);\r\n      color: #fff;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      @include transition(all 0.3s);\r\n    }\r\n    p {\r\n      color: var(--light-200);\r\n      width: calc(100% - 40px);\r\n      padding-inline-start: rem(20px);\r\n      a {\r\n        color: var(--light-200);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.dark-social-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(10px);\r\n  li {\r\n    a {\r\n      width: rem(30px);\r\n      height: rem(30px);\r\n      background-color: rgba(#fff, 0.1);\r\n      color: #fff;\r\n      @include border-radius(5px);\r\n      @include transition(all 0.3s);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      &:hover {\r\n        background-color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-contact-list {\r\n  li + li {\r\n    margin-top: rem(20px);\r\n  }\r\n  li {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n  .icon {\r\n    width: rem(30px);\r\n    height: rem(30px);\r\n    background-color: rgba(#fff, 0.1);\r\n    color: #fff;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    @include border-radius(50%);\r\n  }\r\n  .caption {\r\n    width: calc(100% - 30px);\r\n    padding-inline-start: rem(10px);\r\n    color: #fff;\r\n    font-size: rem(18px);\r\n    text-transform: uppercase;\r\n    font-weight: 600;\r\n    margin-bottom: rem(10px);\r\n  }\r\n  p:not(.caption) {\r\n    width: 100%;\r\n    color: #fff;\r\n    margin-top: rem(5px);\r\n  }\r\n}\r\n\r\n.footer-img-post-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: rem(10px);\r\n  li {\r\n    width: rem(80px);\r\n    height: rem(80px);\r\n    overflow: hidden;\r\n    @include border-radius(10px);\r\n    a {\r\n      display: block;\r\n      &:hover {\r\n        img {\r\n          @include transform(scale(1.1, 1.1));\r\n        }\r\n      }\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        @include object-fit;\r\n        @include transition(all 0.3s);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-section-two {\r\n  .footer-top {\r\n    padding-top: 215px;\r\n  }\r\n}\r\n\r\n.subscription-wrapper {\r\n  position: relative;\r\n  z-index: 2;\r\n  padding: rem(70px) rem(60px);\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: -135px;\r\n  @include media(1199px) {\r\n    padding: rem(50px) rem(40px);\r\n  }\r\n  @include media(575px) {\r\n    padding: rem(40px) rem(20px);\r\n  }\r\n  .section-top-title-two,\r\n  .section-title {\r\n    color: #fff;\r\n  }\r\n  .section-title {\r\n    span {\r\n      display: block;\r\n      color: #fff;\r\n      -webkit-text-fill-color: transparent;\r\n      -webkit-text-stroke-width: 1px;\r\n      -webkit-text-stroke-color: white;\r\n    }\r\n  }\r\n  .subscribe-form {\r\n    width: 500px;\r\n    input {\r\n      &:focus {\r\n        border-color: #fff;\r\n      }\r\n    }\r\n    button {\r\n      background-color: #fff;\r\n      color: var(--dark-900);\r\n    }\r\n  }\r\n  &-bg-img {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    @include border-radius(20px);\r\n    @include object-fit;\r\n    z-index: -1;\r\n  }\r\n}\r\n\r\n.footer-bottom {\r\n  padding: rem(30px) 0;\r\n  background-color: var(--dark-700);\r\n}\r\n\r\n.footer-logo {\r\n  max-height: 95px;\r\n}\r\n\r\n.footer-title {\r\n  color: #fff;\r\n  margin-bottom: rem(40px);\r\n  @include media(991px) {\r\n    margin-bottom: rem(25px);\r\n  }\r\n}\r\n\r\n.footer-menu {\r\n  li + li {\r\n    margin-top: rem(15px);\r\n  }\r\n  li {\r\n    a {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: rem(8px);\r\n      color: var(--light-200);\r\n      @include transition(all 0.3s);\r\n      &:hover {\r\n        color: var(--primary);\r\n      }\r\n      i {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-post-list {\r\n  .footer-single-post + .footer-single-post {\r\n    margin-top: rem(25px);\r\n  }\r\n  .footer-single-post {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    .thumb {\r\n      width: 80px;\r\n      height: 80px;\r\n      @include border-radius(5px);\r\n      overflow: hidden;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        @include object-fit;\r\n      }\r\n    }\r\n    .content {\r\n      width: calc(100% - 80px);\r\n      padding-inline-start: rem(20px);\r\n      .title {\r\n        a {\r\n          font-size: rem(16px);\r\n          color: #fff;\r\n          display: -webkit-box;\r\n          -webkit-line-clamp: 2;\r\n          -webkit-box-orient: vertical;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          &:hover {\r\n            color: var(--primary);\r\n          }\r\n        }\r\n      }\r\n      span {\r\n        color: var(--light-200);\r\n        font-size: rem(14px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-inline-menu {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin: rem(-5px) rem(-15px);\r\n  li {\r\n    a {\r\n      color: var(--light-200);\r\n      padding: rem(5px) rem(15px);\r\n      @include transition(all 0.3s);\r\n      &:hover {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.footer-section-three {\r\n  position: relative;\r\n  z-index: 1;\r\n  background-color: var(--dark-900);\r\n  &-el {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: -1;\r\n  }\r\n  .footer-top {\r\n    background-color: transparent;\r\n    position: static;\r\n  }\r\n  .footer-three-subscription-tile {\r\n    font-size: rem(80px);\r\n    font-weight: 700;\r\n    color: #fff;\r\n    font-family: var(--display-font);\r\n    @include media(1399px) {\r\n      font-size: rem(66px);\r\n    }\r\n    @include media(1199px) {\r\n      font-size: rem(54px);\r\n    }\r\n    @include media(991px) {\r\n      font-size: rem(42px);\r\n    }\r\n    @include media(575px) {\r\n      font-size: rem(32px);\r\n    }\r\n    span {\r\n      display: block;\r\n      color: #fff;\r\n      -webkit-text-fill-color: transparent;\r\n      -webkit-text-stroke-width: 1px;\r\n      -webkit-text-stroke-color: white;\r\n    }\r\n  }\r\n  .subscribe-form {\r\n    input {\r\n      background-color: #0F1133;\r\n      border-color: rgba(#fff, 0.1);\r\n      height: 66px;\r\n    }\r\n    button {\r\n      width: 46px;\r\n      padding: 0;\r\n      display: inline-flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n  }\r\n  .footer-bottom {\r\n    background-color: transparent;\r\n    &-area {\r\n      background-color: var(--dark-700);\r\n      @include border-radius(20px);\r\n      padding: rem(30px);\r\n    }\r\n  }\r\n}", ".inner-banner {\r\n  padding-top: rem(300px);\r\n  padding-bottom: rem(150px);\r\n  position: relative;\r\n  z-index: 1;\r\n  @include media(1399px) {\r\n    padding-top: rem(230px);\r\n    padding-bottom: rem(100px);\r\n  }\r\n  @include media(1199px) {\r\n    padding-top: rem(180px);\r\n  }\r\n  @include media(575px) {\r\n    padding-top: rem(120px);\r\n    padding-bottom: rem(60px);\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: var(--dark-900);\r\n    opacity: 0.8;\r\n    z-index: -1;\r\n  }\r\n  &-bg-img {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n    z-index: -2;\r\n  }\r\n  .title {\r\n    font-family: var(--display-font);\r\n    color: #fff;\r\n    font-size: rem(42px);\r\n    font-weight: 700;\r\n    @media (max-width: 1399px) {\r\n      font-size: rem(36px);\r\n    }\r\n    @media (max-width: 1199px) {\r\n      font-size: rem(32px);\r\n    }\r\n    @media (max-width: 575px) {\r\n      font-size: rem(28px);\r\n    }\r\n  }\r\n}\r\n\r\n.page-breadcrumb {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  margin-top: rem(20px);\r\n  @include media(575px) {\r\n    margin-top: rem(10px);\r\n  }\r\n  li {\r\n    color: #fff;\r\n    font-weight: 500;\r\n    font-size: rem(18px);\r\n    @include media(575px) {\r\n      font-size: rem(16px);\r\n    }\r\n    &:last-child {\r\n      &::after {\r\n        display: none;\r\n      }\r\n    }\r\n    &::after {\r\n      content: \"\\f105\";\r\n      font-family: \"Font Awesome 6 Free\";\r\n      font-weight: 900;\r\n      font-size: rem(14px);\r\n      margin: 0 rem(15px);\r\n    }\r\n    a {\r\n      color: #fff;\r\n    }\r\n  }\r\n}", "/* === banner css start === */\r\n@import 'banner';\r\n/* === banner css end === */\r\n\r\n/* === service css start === */\r\n@import 'service';\r\n/* === service css end === */\r\n\r\n/* === service-details css start === */\r\n@import 'service-details';\r\n/* === service-details css end === */\r\n\r\n/* === ticker css start === */\r\n@import 'ticker';\r\n/* === ticker css end === */\r\n\r\n/* === about css start === */\r\n@import 'about';\r\n/* === about css end === */\r\n\r\n/* === project css start === */\r\n@import 'project';\r\n/* === project css end === */\r\n\r\n/* === project-details css start === */\r\n@import 'project-details';\r\n/* === project-details css end === */\r\n\r\n/* === message css start === */\r\n@import 'message';\r\n/* === message css end === */\r\n\r\n/* === brand css start === */\r\n@import 'brand';\r\n/* === brand css end === */\r\n\r\n/* === newsletter css start === */\r\n@import 'newsletter';\r\n/* === newsletter css end === */\r\n\r\n/* === testimonial css start === */\r\n@import 'testimonial';\r\n/* === testimonial css end === */\r\n\r\n/* === team css start === */\r\n@import 'team';\r\n/* === team css end === */\r\n\r\n/* === team-details css start === */\r\n@import 'team-details';\r\n/* === team-details css end === */\r\n\r\n/* === faq css start === */\r\n@import 'faq';\r\n/* === faq css end === */\r\n\r\n/* === blog css start === */\r\n@import 'blog';\r\n/* === blog css end === */\r\n\r\n/* === blog-details css start === */\r\n@import 'blog-details';\r\n/* === blog-details css end === */\r\n\r\n/* === pricing css start === */\r\n@import 'pricing';\r\n/* === pricing css end === */\r\n\r\n/* === overview css start === */\r\n@import 'overview';\r\n/* === overview css end === */\r\n\r\n/* === gallery css start === */\r\n@import 'gallery';\r\n/* === gallery css end === */\r\n\r\n/* === video css start === */\r\n@import 'video';\r\n/* === video css end === */\r\n\r\n/* === choose css start === */\r\n@import 'choose';\r\n/* === choose css end === */\r\n\r\n/* === contact css start === */\r\n@import 'contact';\r\n/* === contact css end === */", ".banner-section {\r\n  padding-top: rem(300px);\r\n  padding-bottom: rem(175px);\r\n  background-color: var(--dark-900);\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n  @include media(1750px) {\r\n    padding-top: rem(250px);\r\n    padding-bottom: rem(120px);\r\n  }\r\n  @include media(992px) {\r\n    padding-top: rem(150px);\r\n    padding-bottom: rem(70px);\r\n  }\r\n  @include media(991px) {\r\n    padding-bottom: rem(0);\r\n  }\r\n  @include media(575px) {\r\n    padding-top: rem(120px);\r\n  }\r\n  .banner-top-title {\r\n    color: #fff;\r\n    text-transform: uppercase;\r\n  }\r\n  .banner-title {\r\n    color: #fff;\r\n    font-size: 80px;\r\n    font-family: var(--display-font);\r\n    max-width: 15ch;\r\n    font-weight: 700;\r\n    @include media(1750px) {\r\n      font-size: rem(66px);\r\n    }\r\n    @include media(1399px) {\r\n      font-size: rem(54px);\r\n    }\r\n    @include media(1199px) {\r\n      font-size: rem(48px);\r\n    }\r\n    @include media(991px) {\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n    @include media(575px) {\r\n      font-size: rem(36px);\r\n    }\r\n  }\r\n  .banner-description {\r\n    color: #fff;\r\n    max-width: 65ch;\r\n    margin-top: rem(40px);\r\n    @include media(1750px) {\r\n      max-width: 55ch;\r\n    }\r\n    @include media(991px) {\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n      margin-top: rem(25px);\r\n    }\r\n  }\r\n  .banner-btns {\r\n    margin-top: rem(45px);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: 30px;\r\n    @include media(991px) {\r\n      justify-content: center;\r\n      margin-top: rem(25px);\r\n    }\r\n  }\r\n  .banner-left-line-el {\r\n    position: absolute;\r\n    inset-inline-start: 0;\r\n    bottom: 0;\r\n    opacity: 0.65;\r\n    z-index: -3;\r\n  }\r\n  .banner-man-img {\r\n    position: absolute;\r\n    inset-inline-end: 145px;\r\n    inset-block-end: 0;\r\n    z-index: -1;\r\n    @include media(1850px) {\r\n      inset-inline-end: 105px;\r\n    }\r\n    @include media(1750px) {\r\n      max-height: 550px;\r\n    }\r\n    @include media(1399px) {\r\n      max-height: 455px;\r\n    }\r\n    @include media(1199px) {\r\n      max-height: 420px;\r\n      inset-inline-end: 0;\r\n    }\r\n    @include media(991px) {\r\n      position: static;\r\n      margin-top: rem(40px);\r\n    }\r\n  }\r\n  .banner-angle-img {\r\n    position: absolute;\r\n    width: 695px;\r\n    height: 101%;\r\n    inset-inline-end: 135px;\r\n    inset-block-end: 0;\r\n    z-index: -2;\r\n    clip-path: polygon(35% 0%, 100% 0%, 65% 100%, 0% 100%);\r\n    @include media(1850px) {\r\n      inset-inline-end: 95px;\r\n    }\r\n    @include media(1750px) {\r\n      inset-inline-end: 0;\r\n    }\r\n    @include media(1399px) {\r\n      width: 490px;\r\n      inset-inline-end: 35px;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      -o-object-fit: cover;\r\n    }\r\n    @include media(991px) {\r\n      opacity: 0.35;\r\n    }\r\n  }\r\n  .line-shape {\r\n    position: absolute;\r\n    top: 0;\r\n    inset-inline-end: 630px;\r\n    width: 325px;\r\n    height: 100%;\r\n    background-color: #fff;\r\n    opacity: 0.1;\r\n    clip-path: polygon(75% 0%, 100% 0%, 25% 100%, 0% 100%);\r\n    z-index: -2;\r\n    @include media(1850px) {\r\n      inset-inline-end: 590px;\r\n    }\r\n    @include media(1750px) {\r\n      inset-inline-end: 480px;\r\n    }\r\n    @include media(1399px) {\r\n      inset-inline-end: 375px;\r\n      width: 235px;\r\n    }\r\n  }\r\n}\r\n\r\n.call-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: rem(12px);\r\n  &:hover {\r\n    i {\r\n      background-color: var(--secondary);\r\n    }\r\n  }\r\n  i {\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    color: #fff;\r\n    border: 1px solid var(--secondary);\r\n    @include border-radius(50%);\r\n    font-size: rem(20px);\r\n    @include transition(all 0.3s);\r\n  }\r\n  span {\r\n    small {\r\n      font-size: rem(14px);\r\n      display: block;\r\n    }\r\n    strong {\r\n      font-weight: 500;\r\n      font-size: rem(18px);\r\n    }\r\n  }\r\n  &.call-btn-white {\r\n    span {\r\n      small,\r\n      strong {\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.banner-section-two {\r\n  position: relative;\r\n  padding-top: rem(300px);\r\n  padding-bottom: rem(140px);\r\n  position: relative;\r\n  background-color: var(--dark-900);\r\n  z-index: 1;\r\n  overflow: hidden;\r\n  @include media(1399px) {\r\n    padding-top: rem(230px);\r\n    padding-bottom: rem(100px);\r\n  }\r\n  @include media(1199px) {\r\n    padding-top: rem(170px);\r\n  }\r\n  @include media(575px) {\r\n    padding-top: rem(130px);\r\n    padding-bottom: rem(60px);\r\n  }\r\n  .banner-top-title {\r\n    font-weight: 500;\r\n    text-transform: uppercase;\r\n    position: relative;\r\n    color: #fff;\r\n    @include media(575px) {\r\n      font-size: rem(14px);\r\n    }\r\n    &::after {\r\n      position: absolute;\r\n      content: '';\r\n      bottom: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 2px;\r\n      background-color: var(--primary);\r\n    }\r\n  }\r\n  .banner-title {\r\n    font-size: rem(80px);\r\n    font-weight: 700;\r\n    color: #fff;\r\n    font-family: var(--display-font);\r\n    @include media(1750px) {\r\n      font-size: rem(66px);\r\n    }\r\n    @include media(1399px) {\r\n      font-size: rem(54px);\r\n    }\r\n    @include media(1199px) {\r\n      font-size: rem(48px);\r\n    }\r\n    @include media(991px) {\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n    @include media(575px) {\r\n      font-size: rem(36px);\r\n    }\r\n  }\r\n  .check-list {\r\n    li + li {\r\n      margin-top: rem(10px);\r\n    }\r\n    li {\r\n      color: var(--light-200);\r\n    }\r\n  }\r\n  .banner-btns {\r\n    margin-top: rem(45px);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: 30px;\r\n  }\r\n  .banner-section-two-img {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n    z-index: -1;\r\n  }\r\n  .banner-section-two-angle-img {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: -1;\r\n    max-width: 9vw;\r\n  }\r\n}\r\n\r\n.banner-section-three {\r\n  padding-top: rem(260px);\r\n  padding-bottom: rem(200px);\r\n  background-color: var(--dark-900);\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n  @include media(1399px) {\r\n    padding-top: rem(220px);\r\n    padding-bottom: rem(170px);\r\n  }\r\n  @include media(1199px) {\r\n    padding-top: rem(150px);\r\n    padding-bottom: rem(160px);\r\n  }\r\n  @include media(991px) {\r\n    padding-bottom: rem(0);\r\n  }\r\n  @include media(575px) {\r\n    padding-top: rem(120px);\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: var(--dark-900);\r\n    opacity: 0.85;\r\n    z-index: -2;\r\n  }\r\n  .banner-top-title {\r\n    color: var(--primary);\r\n    text-transform: uppercase;\r\n  }\r\n  .banner-title {\r\n    color: #fff;\r\n    font-size: 80px;\r\n    font-family: var(--display-font);\r\n    max-width: 15ch;\r\n    font-weight: 700;\r\n    @include media(1750px) {\r\n      font-size: rem(66px);\r\n    }\r\n    @include media(1399px) {\r\n      font-size: rem(54px);\r\n    }\r\n    @include media(1199px) {\r\n      font-size: rem(48px);\r\n    }\r\n    @include media(991px) {\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n    }\r\n    @include media(575px) {\r\n      font-size: rem(36px);\r\n    }\r\n  }\r\n  .banner-description {\r\n    color: #fff;\r\n    max-width: 65ch;\r\n    margin-top: rem(40px);\r\n    @include media(1750px) {\r\n      max-width: 55ch;\r\n    }\r\n    @include media(991px) {\r\n      margin-left: auto;\r\n      margin-right: auto;\r\n      margin-top: rem(25px);\r\n    }\r\n  }\r\n  .banner-btns {\r\n    margin-top: rem(45px);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: 30px;\r\n    @include media(991px) {\r\n      justify-content: center;\r\n      margin-top: rem(25px);\r\n    }\r\n  }\r\n  .banner-three-social-list {\r\n    position: absolute;\r\n    top: 52%;\r\n    left: 9vw;\r\n    @include media(1700px) {\r\n      left: 30px;\r\n    }\r\n    @include media(1399px) {\r\n      display: none;\r\n    }\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: -100px;\r\n      left: 50%;\r\n      width: 1px;\r\n      height: 72px;\r\n      background-color: var(--primary);\r\n    }\r\n    &::after {\r\n      position: absolute;\r\n      content: '';\r\n      bottom: -100px;\r\n      left: 50%;\r\n      width: 1px;\r\n      height: 72px;\r\n      background-color: var(--primary);\r\n    }\r\n    li {\r\n      margin: 10px 0;\r\n      a {\r\n        color: #fff;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n  .banner-three-bg-img {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n    z-index: -3;\r\n  }\r\n  .banner-man-img {\r\n    position: absolute;\r\n    inset-inline-end: 22%;\r\n    inset-block-end: 0;\r\n    z-index: 1;\r\n    @include media(1750px) {\r\n      max-height: 550px;\r\n    }\r\n    @include media(1399px) {\r\n      max-height: 455px;\r\n    }\r\n    @include media(1199px) {\r\n      max-height: 420px;\r\n      inset-inline-end: 0;\r\n    }\r\n    @include media(991px) {\r\n      position: static;\r\n      margin-top: rem(40px);\r\n    }\r\n  }\r\n  .banner-truck-img {\r\n    position: absolute;\r\n    bottom: 0;\r\n    inset-inline-end: 0;\r\n    z-index: -1;\r\n    @include media(1750px) {\r\n      max-height: 550px;\r\n    }\r\n    @include media(1399px) {\r\n      max-height: 405px;\r\n    }\r\n    @include media(1199px) {\r\n      max-height: 300px;\r\n      inset-inline-end: -100px;\r\n    }\r\n  }\r\n  .banner-angle-img {\r\n    position: absolute;\r\n    width: 695px;\r\n    height: 101%;\r\n    inset-inline-end: 135px;\r\n    inset-block-end: 0;\r\n    z-index: -1;\r\n    clip-path: polygon(35% 0%, 100% 0%, 65% 100%, 0% 100%);\r\n    @include media(1850px) {\r\n      inset-inline-end: 95px;\r\n    }\r\n    @include media(1750px) {\r\n      inset-inline-end: 0;\r\n    }\r\n    @include media(1399px) {\r\n      width: 490px;\r\n      inset-inline-end: 35px;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      -o-object-fit: cover;\r\n    }\r\n    @include media(991px) {\r\n      opacity: 0.35;\r\n    }\r\n  }\r\n  .line-shape {\r\n    position: absolute;\r\n    top: 0;\r\n    inset-inline-end: 630px;\r\n    width: 325px;\r\n    height: 100%;\r\n    background-color: #fff;\r\n    opacity: 0.1;\r\n    clip-path: polygon(75% 0%, 100% 0%, 25% 100%, 0% 100%);\r\n    z-index: -1;\r\n    @include media(1850px) {\r\n      inset-inline-end: 590px;\r\n    }\r\n    @include media(1750px) {\r\n      inset-inline-end: 480px;\r\n    }\r\n    @include media(1399px) {\r\n      inset-inline-end: 375px;\r\n      width: 235px;\r\n    }\r\n  }\r\n}\r\n\r\n.video-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: rem(25px);\r\n\r\n  &.video-btn-white {\r\n    i {\r\n      background-color: #fff;\r\n    }\r\n    span {\r\n      color: #fff;\r\n    }\r\n  }\r\n  i {\r\n    width: rem(40px);\r\n    height: rem(40px);\r\n    background-color: var(--dark-900);\r\n    @include border-radius(50%);\r\n    color: var(--primary);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    box-shadow: 0 0 0 10px rgba($primary, 0.15);\r\n  }\r\n}\r\n\r\n.circle-contact-btn-main {\r\n  position: relative;\r\n  display: inline-flex;\r\n  .circle-contact-btn-link {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 2;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.circle-contact-btn {\r\n  --radius: 11vmin;\r\n\t--frame-size: calc(var(--radius) / 3);\r\n\t--d-outer: calc(var(--radius) * 2);\r\n\t--d-inner: calc(var(--d-outer) - var(--frame-size));\r\n\t--font-size: calc(var(--radius) / 10);\r\n\tposition: relative;\r\n\twidth: var(--d-outer);\r\n\theight: var(--d-outer);\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n  z-index: 1;\r\n  @include media(575px) {\r\n    --radius: 25vmin;\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    border: 2px solid var(--primary);\r\n    border-radius: 100vmax;\r\n    z-index: -1;\r\n  }\r\n  .circle-contact-btn-text {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: 100%;\r\n    animation: rotateText 10s linear infinite;\r\n    span {\r\n      position: absolute;\r\n      left: 50%;\r\n      font-size: 1.2em;\r\n      transform-origin: 0 var(--radius);\r\n      color: var(--primary);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes rotateText {\r\n\t0% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n\t100% {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n}", ".service-one {\r\n  &:hover {\r\n    .thumb {\r\n      img {\r\n        transform: scale(1.1, 1.1);\r\n      }\r\n    }\r\n    .icon {\r\n      background-color: var(--primary);\r\n    }\r\n  }\r\n  .thumb {\r\n    height: rem(345px);\r\n    overflow: hidden;\r\n    @include border-radius(20px);\r\n    @include media(1399px) {\r\n      height: rem(300px);\r\n    }\r\n    @include media(1199px) {\r\n      height: rem(250px);\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n      @include transition(all 0.5s);\r\n    }\r\n  }\r\n  .content {\r\n    margin-left: 30px;\r\n    margin-right: 30px;\r\n    margin-top: rem(-40px);\r\n    padding: rem(40px) rem(30px);\r\n    box-shadow: 0 0 10px rgba(#000, 0.1);\r\n    @include border-radius(20px);\r\n    text-align: center;\r\n    position: relative;\r\n    background-color: #fff;\r\n    @include media(1199px) {\r\n      margin-left: 15px;\r\n      margin-right: 15px;\r\n      padding: rem(30px) rem(20px);\r\n    }\r\n  }\r\n  .icon {\r\n    width: 65px;\r\n    height: 65px;\r\n    position: relative;\r\n    background-color: var(--light-100);\r\n    @include border-radius(20px);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    @include transition(all 0.5s);\r\n    img {\r\n      max-height: 40px;\r\n    }\r\n  }\r\n  .title {\r\n    margin-top: rem(15px);\r\n    margin-bottom: rem(10px);\r\n  }\r\n}\r\n\r\n.service-two {\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  &:hover {\r\n    .content {\r\n      .icon {\r\n        background-color: var(--primary);\r\n      }\r\n    }\r\n    .service-two-btn {\r\n      i {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n  .thumb {\r\n    height: rem(328px);\r\n    overflow: hidden;\r\n    @include border-radius(20px 20px 0 0);\r\n    @include media(1399px) {\r\n      height: rem(280px);\r\n    }\r\n    @include media(1199px) {\r\n      height: rem(250px);\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .content {\r\n    padding: 0 rem(40px) rem(40px) rem(40px);\r\n    position: relative;\r\n    z-index: 1;\r\n    @include media(1199px) {\r\n      padding: 0 rem(30px) rem(30px) rem(30px);\r\n    }\r\n    .icon {\r\n      width: rem(80px);\r\n      height: rem(80px);\r\n      @include border-radius(20px);\r\n      background-color: var(--light-100);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: rem(-40px);\r\n      margin-bottom: rem(20px);\r\n      @include transition(all 0.3s);\r\n    }\r\n  }\r\n  .title {\r\n    margin-bottom: rem(20px);\r\n    @include media(1199px) {\r\n      margin-bottom: rem(15px);\r\n    }\r\n  }\r\n  .service-two-btn {\r\n    font-weight: 500;\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: rem(8px);\r\n    margin-top: rem(30px);\r\n    @include media(1199px) {\r\n      margin-top: rem(20px);\r\n    }\r\n  }\r\n}\r\n\r\n.service-section-three {\r\n  margin-top: -90px;\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n.service-three {\r\n  padding: rem(40px) rem(40px) 0 rem(40px);\r\n  background-color: #fff;\r\n  @include border-radius(5px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  margin-bottom: rem(30px);\r\n  @include media(991px) {\r\n    text-align: center;\r\n  }\r\n  @include media(575px) {\r\n    padding: rem(30px) rem(30px) 0 rem(30px);\r\n  }\r\n  .icon {\r\n    width: rem(80px);\r\n    height: rem(80px);\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(50%);\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: rem(20px);\r\n  }\r\n  p {\r\n    margin-top: rem(20px);\r\n  }\r\n  &-icon-btn {\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    background-color: var(--primary);\r\n    color: #fff;\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    font-size: rem(20px);\r\n    @include border-radius(50%);\r\n    @include transition(all 0.3s);\r\n    transform: translateY(rem(30px));\r\n    &:hover {\r\n      box-shadow: 0 5px 15px var(--primary);\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n.service-four {\r\n  padding: rem(40px);\r\n  background-color: #fff;\r\n  @include border-radius(5px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  border: 1px solid transparent;\r\n  @include transition(all 0.3s);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-flow: column;\r\n  @include media(1199px) {\r\n    padding: rem(25px);\r\n  }\r\n  @include media(767px) {\r\n    align-items: center;\r\n    text-align: center;\r\n  }\r\n  &:hover {\r\n    border-color: var(--primary);\r\n  }\r\n  .icon {\r\n    width: 80px;\r\n    height: 80px;\r\n    background-color: var(--light-100);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @include border-radius(5px);\r\n    margin-bottom: rem(20px);\r\n    img {\r\n      height: 45px;\r\n    }\r\n  }\r\n  p {\r\n    margin-top: rem(20px);\r\n    margin-bottom: rem(30px);\r\n  }\r\n  &-icon-btn {\r\n    font-weight: 500;\r\n    color: var(--dark-900);\r\n    display: inline-flex;\r\n    align-items: center;\r\n    margin-top: auto;\r\n    gap: 8px;\r\n    i {\r\n      color: var(--primary);\r\n    }\r\n  }\r\n}", ".service-details-sidebar {\r\n  padding: rem(40px);\r\n  background-color: var(--light-100);\r\n  @include border-radius(5px);\r\n  @include media(575px) {\r\n    padding: rem(25px);\r\n  }\r\n  &-box {\r\n    padding: rem(25px) rem(30px) rem(30px) rem(30px);\r\n    background-color: #fff;\r\n    @include border-radius(5px);\r\n    margin-top: rem(30px);\r\n  }\r\n}", ".ticker-section {\r\n  padding: rem(60px) 0;\r\n  background-color: var(--primary);\r\n  overflow: hidden;\r\n  @include media(991px) {\r\n    padding: rem(30px) 0;\r\n  }\r\n}\r\n\r\n.ticker-list {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: rem(40px);\r\n  width: max-content;\r\n  flex-wrap: nowrap;\r\n  animation: scroll 40s forwards linear infinite;\r\n  &:hover {\r\n    animation-play-state: paused;\r\n  }\r\n  li {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: rem(40px);\r\n    font-family: var(--display-font);\r\n    font-size: rem(42px);\r\n    font-weight: 700;\r\n    color: #fff;\r\n    @include media(1399px) {\r\n      font-size: rem(36px);\r\n    }\r\n    @include media(991px) {\r\n      font-size: rem(32px);\r\n    }\r\n    @include media(991px) {\r\n      font-size: rem(26px);\r\n    }\r\n    img {\r\n      max-height: rem(35px);\r\n    }\r\n  }\r\n}\r\n\r\n@include keyframes(scroll) {\r\n  to {\r\n    transform: translate(calc(-50% - 0.5rem));\r\n  }\r\n}", ".about-section-one {\r\n  position: relative;\r\n  overflow: hidden;\r\n  .about-section-one-el {\r\n    position: absolute;\r\n    bottom: 0;\r\n    inset-inline-end: 0;\r\n    @include media(1399px) {\r\n      inset-inline-end: rem(-175px);\r\n    }\r\n  }\r\n}\r\n\r\n.about-section-two {\r\n  position: relative;\r\n  z-index: 1;\r\n  .about-section-two-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: -1;\r\n  }\r\n}\r\n\r\n.about-two-item {\r\n  padding-top: rem(35px);\r\n  position: relative;\r\n  .icon {\r\n    position: absolute;\r\n    top: 0;\r\n    inset-inline-start: rem(30px);\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    background-color: var(--primary);\r\n    @include border-radius(20px);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n  .content {\r\n    padding: rem(45px) rem(30px) rem(30px) rem(30px);\r\n    background-color: #fff;\r\n    @include border-radius(20px);\r\n  }\r\n}\r\n\r\n.about-two-bottom {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(50px);\r\n  margin-top: rem(40px);\r\n  @include media(575px) {\r\n    gap: rem(20px);\r\n  }\r\n}\r\n\r\n.about-bottom-users {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(10px);\r\n}\r\n\r\n.about-section-three {\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n  .about-section-three-el {\r\n    position: absolute;\r\n    bottom: 0;\r\n    inset-inline-end: 0;\r\n    z-index: -1;\r\n  }\r\n}\r\n\r\n.about-three-thumb {\r\n  position: relative;\r\n  z-index: 1;\r\n  padding: 27px 0 27px 27px;\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: 0;\r\n    width: calc(100% - 35px);\r\n    height: 100%;\r\n    border: 4.5px solid var(--primary);\r\n    @include border-radius(65px 0 0 65px);\r\n    z-index: -1;\r\n  }\r\n  img {\r\n    @include border-radius(65px 0 0 65px);\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n  }\r\n  &-content {\r\n    position: absolute;\r\n    inset-inline-end: rem(35px);\r\n    bottom: 62px;\r\n    width: 120px;\r\n    height: 120px;\r\n    background-color: var(--dark-900);\r\n    display: flex;\r\n    flex-flow: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    @include border-radius(50%);\r\n    span {\r\n      font-size: rem(30px);\r\n      font-weight: 700;\r\n      color: #fff;\r\n      display: block;\r\n      line-height: 1.3;\r\n      margin-bottom: rem(5px);\r\n    }\r\n    small {\r\n      font-size: rem(18px);\r\n      font-weight: 500;\r\n      color: #fff;\r\n      line-height: 1.3;\r\n    }\r\n  }\r\n}\r\n\r\n.about-three-content {\r\n  .check-list {\r\n    margin: 0;\r\n  }\r\n  .call-btn {\r\n    margin-top: rem(30px);\r\n    i {\r\n      background-color: #fff;\r\n      color: var(--dark-900);\r\n    }\r\n    strong {\r\n      color: var(--dark-900);\r\n    }\r\n  }\r\n}\r\n", ".project-one {\r\n  & + & {\r\n    margin-top: rem(30px);\r\n  }\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center; \r\n  gap: rem(40px);\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  padding: rem(20px) rem(40px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  @include media(1399px) {\r\n    gap: rem(30px);\r\n    padding: rem(20px) rem(30px);\r\n  }\r\n  @include media(767px) {\r\n    justify-content: center;\r\n    text-align: center;\r\n    gap: rem(15px);\r\n  }\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  &:hover {\r\n    .thumb {\r\n      opacity: 1;\r\n    }\r\n    .icon {\r\n      transform: rotate(-45deg);\r\n    }\r\n  }\r\n\r\n  .number {\r\n    width: 65px;\r\n    height: 65px;\r\n    background-color: var(--light-100);\r\n    @include border-radius(10px);\r\n    font-size: rem(30px);\r\n    font-weight: 700;\r\n    color: var(--primary);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .title {\r\n    font-size: rem(30px);\r\n    font-weight: 700;\r\n    width: 20ch;\r\n    @include media(1399px) {\r\n      font-size: rem(24px);\r\n    }\r\n    @include media(1199px) {\r\n      width: 40ch;\r\n    }\r\n    @include media(991px) {\r\n      width: 35ch;\r\n      font-size: rem(20px);\r\n    }\r\n  }\r\n\r\n  .description {\r\n    max-width: 40ch;\r\n    @include media(991px) {\r\n      max-width: 35ch;\r\n    }\r\n  }\r\n\r\n  .thumb {\r\n    width: 212px;\r\n    height: 105px;\r\n    @include border-radius(20px);\r\n    overflow: hidden;\r\n    margin: 0 auto;\r\n    opacity: 0;\r\n    @include transition(all 0.3s);\r\n    @include media(1199px) {\r\n      opacity: 1;\r\n      margin-inline-start: 0;\r\n    }\r\n  }\r\n  .icon {\r\n    margin-inline-start: auto;\r\n    @include transition(all 0.3s);\r\n    @include media(767px) {\r\n      margin-inline-start: 0;\r\n    }\r\n  }\r\n}", ".project-quote {\r\n  position: relative;\r\n  padding: rem(30px);\r\n  border: 1px solid var(--border-color);\r\n  @include border-radius(5px);\r\n  margin-top: rem(40px);\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  &::after {\r\n    position: absolute;\r\n    content: \"\\f10e\";\r\n    bottom: 5px;\r\n    inset-inline-end: 70px;\r\n    font-family: \"Font Awesome 6 Free\";\r\n    font-weight: 900;\r\n    line-height: 1;\r\n    font-size: rem(100px);\r\n    opacity: 0.1;\r\n  }\r\n  .thumb {\r\n    width: 90px;\r\n    height: 90px;\r\n    @include border-radius(50%);\r\n    overflow: hidden;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .content {\r\n    width: calc(100% - 90px);\r\n    padding-inline-start: rem(22px);\r\n    @include media(575px) {\r\n      width: 100%;\r\n      padding-inline-start: 0;\r\n      margin-top: rem(20px);\r\n    }\r\n  }\r\n  .quote-name {\r\n    position: relative;\r\n    padding-inline-start: rem(25px);\r\n    font-weight: 500;\r\n    margin-top: rem(10px);\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 50%;\r\n      left: 0;\r\n      width: rem(15px);\r\n      height: 1px;\r\n      background-color: var(--dark-900);\r\n    }\r\n  }\r\n}\r\n\r\n.project-details-sidebar {\r\n  padding-block: rem(40px);\r\n  padding-inline-end: rem(40px);\r\n  background-color: var(--light-100);\r\n  @include border-radius(5px);\r\n  @include media(575px) {\r\n    padding-block: rem(20px);\r\n    padding-inline-end: rem(20px);\r\n  }\r\n  &-header {\r\n    padding-inline: rem(40px);\r\n    @include media(575px) {\r\n      padding-inline: rem(20px);\r\n    }\r\n  }\r\n}\r\n\r\n.project-details-sidebar-list {\r\n  li + li {\r\n    margin-top: rem(20px);\r\n  }\r\n  li {\r\n    padding: rem(10px) rem(40px);\r\n    background-color: #fff;\r\n    @include border-radius(0 5px 5px 0);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    @include media(575px) {\r\n      padding: rem(10px) rem(20px);\r\n    }\r\n    span {\r\n      &:first-child {\r\n        font-size: rem(18px);\r\n        font-weight: 500;\r\n        color: var(--dark-900);\r\n        width: 95px;\r\n      }\r\n      &:last-child {\r\n        width: calc(100% - 95px);\r\n      }\r\n      &.rating {\r\n        i {\r\n          color: var(--primary);\r\n          font-size: rem(14px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", ".message-section-one {\r\n  position: relative;\r\n  z-index: 1;\r\n  .message-section-el {\r\n    position: absolute;\r\n    z-index: -1;\r\n    &.message-left-el {\r\n      top: 0;\r\n      left: 0;\r\n    }\r\n\r\n    &.message-right-el {\r\n      bottom: 0;\r\n      right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.overview-item {\r\n  padding: rem(30px) rem(40px);\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  height: 100%;\r\n  @include media(1199px){\r\n    padding: rem(30px);\r\n  }\r\n  @include media(575px){\r\n    padding: rem(20px);\r\n  }\r\n  .icon {\r\n    height: 60px;\r\n    @include media(1399px) {\r\n      height: 50px;\r\n    }\r\n    @include media(575px) {\r\n      height: 40px;\r\n    }\r\n  }\r\n  .amount {\r\n    font-family: var(--display-font);\r\n    font-size: rem(42px);\r\n    margin-top: rem(30px);\r\n    margin-bottom: rem(15px);\r\n    @include media(1399px) {\r\n      font-size: rem(36px);\r\n    }\r\n    @include media(991px) {\r\n      font-size: rem(32px);\r\n    }\r\n    @include media(575px) {\r\n      font-size: rem(26px);\r\n      margin-top: rem(20px);\r\n      margin-bottom: rem(10px);\r\n    }\r\n  }\r\n  p {\r\n    @include media(575px) {\r\n      font-size: rem(14px);\r\n    }\r\n  }\r\n}\r\n\r\n.message-wrapper {\r\n  padding-inline-start: rem(50px);\r\n  @include media(991px) {\r\n    padding-inline-start: 0;\r\n  }\r\n  .message-form {\r\n    margin-top: rem(40px);\r\n  }\r\n}\r\n\r\n\r\n.message-section-two-inner {\r\n  padding: rem(80px) 0;\r\n  position: relative;\r\n  z-index: 1;\r\n  @include media(1199px) {\r\n    padding: rem(50px) 0;\r\n  }\r\n  @include media(991px) {\r\n    padding: 0;\r\n  }\r\n  .message-section-two-thumb {\r\n    position: absolute;\r\n    top: 0;\r\n    inset-inline-start: 0;\r\n    width: 64%;\r\n    height: 100%;\r\n    z-index: -1;\r\n    @include border-radius(10px);\r\n    overflow: hidden;\r\n    @include media(991px) {\r\n      position: static;\r\n      width: 100%;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n}\r\n\r\n.message-wrapper-two {\r\n  padding: rem(60px);\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  box-shadow: 0 -5px 20px -1px rgba(#131022, 0.05);\r\n  @include media(1399px) {\r\n    padding: rem(40px);\r\n  }\r\n  @include media(575px) {\r\n    padding: rem(30px);\r\n  }\r\n  .section-title {\r\n    margin-bottom: rem(20px);\r\n  }\r\n  .message-form {\r\n    margin-top: rem(40px);\r\n  }\r\n}\r\n\r\n.message-three-thumb {\r\n  width: 512px;\r\n  height: 512px;\r\n  position: relative;\r\n  z-index: 1;\r\n  @include media(1199px) {\r\n    width: 450px;\r\n    height: 450px;\r\n  }\r\n  @include media(575px) {\r\n    width: 280px;\r\n    height: 280px;\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: -10px;\r\n    width: 96%;\r\n    height: 96%;\r\n    background-color: var(--primary);\r\n    @include border-radius(50%);\r\n    z-index: -1;\r\n    @include media(575px) {\r\n      left: -5px;\r\n    }\r\n  }\r\n  img {\r\n    width: 512px;\r\n    height: 512px;\r\n    @include object-fit;\r\n    @include border-radius(50%);\r\n    @include media(1199px) {\r\n      width: 450px;\r\n      height: 450px;\r\n    }\r\n    @include media(575px) {\r\n      width: 280px;\r\n      height: 280px;\r\n    }\r\n  }\r\n  .message-three-video-btn {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: rem(70px);\r\n    height: rem(70px);\r\n    background-color: #fff;\r\n    @include border-radius(50%);\r\n    color: var(--primary);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: rem(22px);\r\n    @include media(575px) {\r\n      width: rem(45px);\r\n      height: rem(45px);\r\n      font-size: rem(14px);\r\n    }\r\n  }\r\n  &-content {\r\n    position: absolute;\r\n    bottom: 15px;\r\n    inset-inline-end: 0;\r\n    width: 130px;\r\n    height: 130px;\r\n    background-color: #fff;\r\n    @include border-radius(50%);\r\n    display: flex;\r\n    flex-flow: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 0;\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 10px;\r\n      left: 10px;\r\n      width: calc(100% - 20px);\r\n      height: calc(100% - 20px);\r\n      @include border-radius(50%);\r\n      border: 1px dashed var(--light-200);\r\n    }\r\n    span {\r\n      font-size: rem(12px);\r\n    }\r\n    p {\r\n      font-size: rem(20px);\r\n      font-weight: 700;\r\n      display: block;\r\n      color: #fff;\r\n      -webkit-text-fill-color: transparent;\r\n      -webkit-text-stroke-width: 1px;\r\n      -webkit-text-stroke-color: var(--primary);\r\n    }\r\n  }\r\n}", ".brand-section {\r\n  overflow: hidden;\r\n}\r\n\r\n.brand-item {\r\n  height: 48px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  img {\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n.brand-slider {\r\n  .slick-list {\r\n    margin: 0 rem(-15px);\r\n  }\r\n  .brand-slide {\r\n    padding: 0 rem(15px);\r\n  }\r\n}", ".news-letter-wrapper {\r\n  position: relative;\r\n  overflow: hidden;\r\n  padding: rem(60px);\r\n  @include border-radius(20px);\r\n  z-index: 1;\r\n  @include media(991px) {\r\n    padding: rem(40px) rem(20px);\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-color: var(--dark-900);\r\n    opacity: 0.8;\r\n  }\r\n  &-img {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n    z-index: -1;\r\n  }\r\n  &-inner {\r\n    width: 505px;\r\n    position: relative;\r\n    z-index: 1;\r\n    @include media(991px) {\r\n      width: 100%;\r\n    }\r\n  }\r\n  .section-title {\r\n    color: #fff;\r\n    @include media(991px) {\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .subscribe-form {\r\n    margin-top: rem(35px);\r\n  }\r\n\r\n  .news-letter-icon-wrapper {\r\n    position: absolute;\r\n    top: 0;\r\n    inset-inline-end: rem(60px);\r\n    height: 100%;\r\n    width: 330px;\r\n    background-color: #fff;\r\n    clip-path: polygon(29% 0, 100% 0, 75% 100%, 0% 100%);\r\n    @include media(1199px) {\r\n      inset-inline-end: rem(30px);\r\n    }\r\n    @include media(991px) {\r\n      display: none;\r\n    }\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 0;\r\n      left: rem(20px);\r\n      width: 100%;\r\n      height: 100%;\r\n      clip-path: polygon(29% 0, 100% 0, 75% 100%, 0% 100%);\r\n      background-color: var(--primary);\r\n    }\r\n    img {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      @include transform(translate(-50%, -50%));\r\n      min-height: 115px;\r\n    }\r\n  }\r\n}\r\n\r\n.subscribe-form {\r\n  position: relative;\r\n  input {\r\n    height: 80px;\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(99px);\r\n    width: 100%;\r\n    padding: rem(20px) rem(150px) rem(20px) rem(30px);\r\n    color: #fff;\r\n    @include placeholder(var(--light-200));\r\n    @include transition(all 0.3s);\r\n    &:focus {\r\n      border-color: var(--secondary);\r\n    }\r\n  }\r\n  button {\r\n    position: absolute;\r\n    top: 10px;\r\n    inset-inline-end: 10px;\r\n    height: calc(100% - 20px);\r\n    padding: rem(10px) rem(30px);\r\n    background-color: var(--secondary);\r\n    color: #fff;\r\n    @include border-radius(99px);\r\n    text-transform: uppercase;\r\n    font-weight: 500;\r\n  }\r\n}", ".testimonial-section-one {\r\n  position: relative;\r\n  .testimonial-section-one-line {\r\n    position: absolute;\r\n    bottom: 0;\r\n    right: 0;\r\n    max-inline-size: initial;\r\n    max-block-size: initial;\r\n  }\r\n\r\n  .testimonial-section-one-thumb {\r\n    position: relative;\r\n    z-index: 1;\r\n  }\r\n\r\n  .section-top {\r\n    @include media(991px) {\r\n      text-align: center;\r\n    }\r\n    .section-title {\r\n      @include media(991px) {\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.testimonial-one {\r\n  .content {\r\n    padding: rem(40px) rem(40px) rem(70px) rem(40px);\r\n    @include border-radius(20px);\r\n    background-color: var(--dark-700);\r\n    @include media(575px) {\r\n      padding: rem(25px) rem(25px) rem(60px) rem(25px);\r\n      text-align: center;\r\n    }\r\n    .icon {\r\n      i {\r\n        color: var(--primary);\r\n        font-size: rem(60px);\r\n        @include media(991px) {\r\n          font-size: rem(48px);\r\n        }\r\n        @include media(575px) {\r\n          font-size: rem(42px);\r\n        }\r\n      }\r\n    }\r\n    p {\r\n      color: var(--light-200);\r\n      font-size: rem(24px);\r\n      @include media(1399px) {\r\n        font-size: rem(22px);\r\n      }\r\n      @include media(991px) {\r\n        font-size: rem(20px);\r\n      }\r\n      @include media(575px) {\r\n        font-size: rem(18px);\r\n      }\r\n    }\r\n  }\r\n  .testimonial-client {\r\n    padding-inline-start: rem(40px);\r\n    margin-top: rem(-40px);\r\n    @include media(575px) {\r\n      padding-inline-start: 0;\r\n      text-align: center;\r\n    }\r\n    .client-img {\r\n      width: rem(73px);\r\n      height: rem(73px);\r\n      @include border-radius(50%);\r\n      overflow: hidden;\r\n      @include media(575px) {\r\n        margin-left: auto;\r\n        margin-right: auto;\r\n      }\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        @include object-fit;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.testimonial-slider-one {\r\n  z-index: 1;\r\n  @include media(575px) {\r\n    padding-bottom: rem(70px);\r\n  }\r\n  .slick-arrow {\r\n    position: absolute;\r\n    bottom: rem(10px);\r\n    inset-inline-end: 0;\r\n    width: rem(40px);\r\n    height: rem(40px);\r\n    color: #fff;\r\n    border: 1px solid var(--primary);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    @include border-radius(50%);\r\n    @include transition(all 0.3s);\r\n    z-index: 1;\r\n    @include media(575px) {\r\n      width: rem(35px);\r\n      height: rem(35px);\r\n    }\r\n    &.prev {\r\n      inset-inline-end: rem(55px);\r\n      @include media(575px) {\r\n        inset-inline-end: 50%;\r\n      }\r\n    }\r\n    &.next {\r\n      @include media(575px) {\r\n        inset-inline-end: calc(50% - 40px);\r\n      }\r\n    }\r\n    &:hover {\r\n      color: #fff;\r\n      background-color: var(--primary);\r\n    }\r\n  }\r\n}\r\n\r\n.testimonial-two-area {\r\n  position: relative;\r\n  padding: rem(80px) 0;\r\n  @include media(991px) {\r\n    padding: 0;\r\n  }\r\n  .testimonial-two-area-thumb {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: 57%;\r\n    height: 100%;\r\n    @include border-radius(20px);\r\n    overflow: hidden;\r\n    @include media(991px) {\r\n      display: none;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .testimonial-two-slider-area {\r\n    padding: rem(60px);\r\n    background-color: var(--dark-700);\r\n    position: relative;\r\n    z-index: 1;\r\n    @include border-radius(20px);\r\n    @include media(575px) {\r\n      padding: rem(20px);\r\n    }\r\n    .testimonial-one {\r\n      .content {\r\n        padding: 0;\r\n      }\r\n      .testimonial-client {\r\n        margin-top: 0;\r\n        padding-inline-start: rem(0);\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        margin-top: rem(35px);\r\n        .client-img {\r\n          width: rem(70px);\r\n          height: rem(70px);\r\n        }\r\n        .client-details {\r\n          width: calc(100% - 70px);\r\n          padding-inline-start: rem(20px);\r\n          @include media(575px) {\r\n            width: 100%;\r\n            padding-inline-start: 0;\r\n            margin-top: rem(15px);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.testimonial-section-three {\r\n  position: relative;\r\n  z-index: 1;\r\n  .testimonial-section-three-el {\r\n    position: absolute;\r\n    bottom: 0;\r\n    right: 0;\r\n    z-index: -1;\r\n    height: 100%;\r\n    @include object-fit;\r\n  }\r\n}\r\n\r\n.testimonial-three-slider-area {\r\n  padding: rem(60px);\r\n  background-color: #fff;\r\n  position: relative;\r\n  z-index: 1;\r\n  @include border-radius(20px);\r\n  @include media(1399px) {\r\n    padding: rem(40px);\r\n  }\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  .testimonial-one {\r\n    .content {\r\n      padding: 0;\r\n      background-color: #fff;\r\n      p {\r\n        color: var(--dark-800);\r\n      }\r\n    }\r\n    .testimonial-client {\r\n      margin-top: 0;\r\n      padding-inline-start: rem(0);\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      margin-top: rem(35px);\r\n      .client-img {\r\n        width: rem(70px);\r\n        height: rem(70px);\r\n      }\r\n      .client-details {\r\n        width: calc(100% - 70px);\r\n        padding-inline-start: rem(20px);\r\n        @include media(575px) {\r\n          width: 100%;\r\n          padding-inline-start: 0;\r\n          margin-top: rem(15px);\r\n        }\r\n        p {\r\n          margin-top: rem(5px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .testimonial-slider-one {\r\n    .slick-arrow {\r\n      color: var(--dark-900);\r\n      border-color: var(--border-color);\r\n      &:hover {\r\n        border-color: var(--primary);\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.testimonial-section-four {\r\n  .testimonial-one {\r\n    .content {\r\n      background-color: var(--light-100);\r\n      p {\r\n        color: var(--dark-800);\r\n      }\r\n    }\r\n  }\r\n  .testimonial-slider-one {\r\n    .slick-arrow {\r\n      border-color: var(--border-color);\r\n      color: var(--dark-900);\r\n      &:hover {\r\n        border-color: var(--primary);\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}", ".team-section-one {\r\n  overflow: hidden;\r\n  padding-inline: rem(95px);\r\n  @include media(1399px) {\r\n    padding-inline: rem(40px);\r\n  }\r\n  @include media(991px) {\r\n    padding-inline: rem(15px);\r\n  }\r\n}\r\n\r\n.team-one {\r\n  padding: rem(30px);\r\n  background-color: #fff;\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  @include border-radius(20px);\r\n  border-bottom: 2px solid #fff;\r\n  @include transition(all 0.3s);\r\n  @include media(991px) {\r\n    padding: rem(20px);\r\n  }\r\n  &:hover {\r\n    border-color: var(--primary);\r\n    .thumb {\r\n      .team-social-list {\r\n        bottom: 20px;\r\n        opacity: 1;\r\n        li {\r\n          transform: translateY(0) !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .thumb {\r\n    position: relative;\r\n    height: 295px;\r\n    @include border-radius(20px);\r\n    overflow: hidden;\r\n    @include media(1620px) {\r\n      height: 250px;\r\n    }\r\n    @include media(1199px) {\r\n      height: 210px;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n    .team-social-list {\r\n      position: absolute;\r\n      bottom: 10px;\r\n      width: 100%;\r\n      padding: 0 rem(20px);\r\n      opacity: 0;\r\n      @include transition(all 0.3s);\r\n      li {\r\n        &:nth-child(1) {\r\n          transform: translateY(10px);\r\n        }\r\n        &:nth-child(2) {\r\n          transform: translateY(30px);\r\n        }\r\n        &:nth-child(3) {\r\n          transform: translateY(50px);\r\n        }\r\n        &:nth-child(4) {\r\n          transform: translateY(70px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .team-social-list {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    gap: rem(10px);\r\n    li {\r\n      @include transition(all 0.3s);\r\n      a {\r\n        width: 35px;\r\n        height: 35px;\r\n        background-color: #fff;\r\n        border: 1px solid var(--primary);\r\n        @include border-radius(50%);\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        color: var(--dark-900);\r\n        @include transition(all 0.3s);\r\n        &:hover {\r\n          background-color: var(--primary);\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .content {\r\n    text-align: center;\r\n    margin-top: rem(30px);\r\n  }\r\n  &.style-two {\r\n    background-color: transparent;\r\n    border: none;\r\n    padding: rem(30px) rem(40px) 0 rem(40px);\r\n    box-shadow: none;\r\n    position: relative;\r\n    z-index: 1;\r\n    @include media(575px) {\r\n      padding: rem(30px) rem(30px) 0 rem(30px);\r\n    }\r\n    &:hover {\r\n      &::before {\r\n        background-color: var(--light-100);\r\n        box-shadow: none;\r\n      }\r\n    }\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 60%;\r\n      background-color: #fff;\r\n      @include border-radius(20px);\r\n      box-shadow: 0 15px 55px rgba(#000, 0.05);\r\n      z-index: -1;\r\n      @include transition(all 0.3s);\r\n    }\r\n    .thumb {\r\n      height: 315px;\r\n      @include media(1199px) {\r\n        height: 250px;\r\n      }\r\n    }\r\n    .content {\r\n      margin-top: 0;\r\n      margin-bottom: rem(30px);\r\n      p {\r\n        margin-top: rem(5px);\r\n      }\r\n    }\r\n  }\r\n  .team-social-list {\r\n    li {\r\n      a {\r\n        border: none;\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.team-two {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  position: relative;\r\n  padding: rem(40px) rem(20px) rem(40px) 0;\r\n  z-index: 1;\r\n  @include media(1399px) {\r\n    padding: rem(30px) rem(20px) rem(30px) 0;\r\n  }\r\n  @include media(1199px) {\r\n    padding: rem(20px);\r\n  }\r\n  &:hover {\r\n    &::before {\r\n      border-color: var(--primary);\r\n    }\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    inset-inline-end: 0;\r\n    height: 100%;\r\n    width: calc(100% - 80px);\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(20px);\r\n    z-index: -1;\r\n    @include transition(all 0.3s);\r\n    @include media(1199px) {\r\n      width: 100%;\r\n    }\r\n  }\r\n  .thumb {\r\n    width: rem(180px);\r\n    height: rem(180px);\r\n    overflow: hidden;\r\n    @include border-radius(20px);\r\n    @include media(1399px) {\r\n      width: rem(150px);\r\n      height: rem(150px);\r\n    }\r\n    @include media(1199px) {\r\n      width: 100%;\r\n      height: rem(200px);\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .content {\r\n    width: calc(100% - rem(180px));\r\n    padding-inline-start: rem(20px);\r\n    @include media(1399px) {\r\n      width: calc(100% - rem(150px));\r\n    }\r\n    @include media(1199px) {\r\n      width: 100%;\r\n      padding-inline-start: 0;\r\n      margin-top: rem(20px);\r\n      text-align: center;\r\n    }\r\n  }\r\n  .team-social-list {\r\n    display: inline-flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    background-color: var(--light-100);\r\n    @include border-radius(20px);\r\n    margin-top: rem(20px);\r\n    li {\r\n      position: relative;\r\n      &:last-child {\r\n        &::after {\r\n          display: none;\r\n        }\r\n      }\r\n      &::after {\r\n        position: absolute;\r\n        content: '';\r\n        top: 50%;\r\n        right: 0;\r\n        width: 1px;\r\n        height: 16px;\r\n        background-color: var(--border-color);\r\n        margin-top: -8px;\r\n      }\r\n      a {\r\n        padding: rem(10px) rem(15px);\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.team-slider,\r\n.team-slider-two {\r\n  .slick-list {\r\n    margin: rem(-15px);\r\n  }\r\n  .team-slide {\r\n    padding: rem(15px);\r\n  }\r\n}", ".team-details-wrapper {\r\n  background-color: var(--light-100);\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .thumb {\r\n    width: 430px;\r\n    @include media(991px) {\r\n      width: 100%;\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .content {\r\n    width: calc(100% - 430px);\r\n    padding: rem(40px);\r\n    @include media(991px) {\r\n      width: 100%;\r\n    }\r\n    @include media(575px) {\r\n      padding: rem(25px);\r\n    }\r\n    .designation {\r\n      position: relative;\r\n      padding-bottom: rem(5px);\r\n      margin-top: rem(10px);\r\n      &::after {\r\n        position: absolute;\r\n        content: '';\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 110px;\r\n        height: 3px;\r\n        background-color: var(--primary);\r\n        @include border-radius(30px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.skill-bar p {\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.progressbar {\r\n  position: relative;\r\n  display: block;\r\n  width: 100%;\r\n  height: 8px;\r\n  background-color: var(--light-200);\r\n  @include border-radius(99px);\r\n  .bar {\r\n    position:absolute;\r\n    width: 0px;\r\n    height: 100%;\r\n    top: 0;\r\n    left: 0;\r\n    background: var(--primary);\r\n    overflow:hidden;\r\n    @include border-radius(99px);\r\n  }\r\n  .label {\r\n    position: absolute;\r\n    top: -40px;\r\n    left: 0;\r\n    width: 30px;\r\n    height: 30px;\r\n    display: block;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    @include transform(translateX(-10px));\r\n  }\r\n}", ".faq-img {\r\n  position: relative;\r\n  padding-inline-end: rem(145px);\r\n  z-index: 1;\r\n  @include media(575px) {\r\n    padding-inline-end: rem(45px);\r\n  } \r\n  &::after {\r\n    position: absolute;\r\n    content: '';\r\n    inset-inline-end: rem(120px);\r\n    bottom: 0;\r\n    width: rem(19px);\r\n    height: rem(300px);\r\n    background-color: var(--primary);\r\n    z-index: -1;\r\n  }\r\n  .faq-main-img {\r\n    @include border-radius(20px 5px 5px 20px);\r\n  }\r\n  .faq-float-img {\r\n    position: absolute;\r\n    top: rem(40px);\r\n    inset-inline-end: 0;\r\n    width: rem(295px);\r\n    height: rem(400px);\r\n    border: rem(14px) solid #fff;\r\n    @include border-radius(20px);\r\n    @include object-fit;\r\n    @include media(575px) {\r\n      width: rem(195px);\r\n      height: rem(250px);\r\n      border-width: rem(8px);\r\n    }\r\n  }\r\n}\r\n\r\n.faq-section-two {\r\n  background-color: var(--light-100);\r\n  padding-bottom: rem(60px);\r\n}", ".blog-one {\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  box-shadow: 0 15px 50px rgba(#000, 0.07); \r\n  height: 100%;\r\n  .thumb {\r\n    position: relative;\r\n    height: 300px;\r\n    @include border-radius(20px 20px 0 0);\r\n    overflow: hidden;\r\n    @include media(1399px) {\r\n      height: rem(250px);\r\n    }\r\n    .blog-date {\r\n      position: absolute;\r\n      top: 0;\r\n      inset-inline-end: 0;\r\n      background-color: var(--light-100);\r\n      color: var(--dark-900);\r\n      font-size: rem(18px);\r\n      text-transform: uppercase;\r\n      width: 80px;\r\n      height: 80px;\r\n      @include border-radius(20px);\r\n      display: flex;\r\n      flex-flow: column;\r\n      justify-content: center;\r\n      align-items: center;\r\n      font-weight: 500;\r\n      @include media(575px) {\r\n        font-size: rem(16px);\r\n        width: 70px;\r\n        height: 70px;\r\n      }\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n    }\r\n  }\r\n  .content {\r\n    padding: rem(30px) rem(40px) rem(40px) rem(40px);\r\n    @include media(1399px) {\r\n      padding: rem(30px);\r\n    }\r\n  }\r\n  .blog-meta {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: rem(30px);\r\n    margin-bottom: rem(20px);\r\n    li {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: rem(8px);\r\n      font-size: rem(14px);\r\n      i {\r\n        color: var(--dark-900);\r\n      }\r\n      span {\r\n        margin-bottom: -1px;\r\n      }\r\n    }\r\n  }\r\n  .blog-title {\r\n    margin-bottom: rem(20px);\r\n  }\r\n  .btn {\r\n    margin-top: rem(40px);\r\n  }\r\n  &.style-two {\r\n    .content {\r\n      padding-top: rem(0);\r\n    }\r\n    .blog-meta {\r\n      padding: rem(10px) rem(40px);\r\n      margin-left: rem(-40px);\r\n      margin-right: rem(-40px);\r\n      background-color: var(--light-100);\r\n      @include border-radius(0 0 20px 20px);\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.blog-list-post {\r\n  .thumb {\r\n    position: relative;\r\n    a {\r\n      display: block;\r\n    }\r\n    img {\r\n      @include border-radius(5px 5px 0 0);\r\n      width: 100%;\r\n    }\r\n    .post-date {\r\n      position: absolute;\r\n      bottom: 0;\r\n      inset-inline-end: 0;\r\n      padding: rem(8px) rem(20px);\r\n      background-color: var(--primary);\r\n      color: #fff;\r\n      font-size: rem(20px);\r\n      font-weight: 500;\r\n      @include media(575px) {\r\n        font-size: rem(14px);\r\n      }\r\n    }\r\n  }\r\n  .blog-meta {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: rem(30px);\r\n    margin-bottom: rem(30px);\r\n    @include media(575px) {\r\n      gap: rem(10px);\r\n    }\r\n  }\r\n  .content {\r\n    padding: rem(40px);\r\n    background-color: var(--light-100);\r\n    @include border-radius(0 0 5px 5px);\r\n    @include media(575px) {\r\n      padding: rem(25px);\r\n    }\r\n  }\r\n  .title {\r\n    margin-bottom: rem(20px);\r\n    a {\r\n      font-size: rem(42px);\r\n      font-family: var(--display-font);\r\n      font-weight: 700;\r\n      @media (max-width: 1399px) {\r\n        font-size: rem(36px);\r\n      }\r\n      @media (max-width: 1199px) {\r\n        font-size: rem(32px);\r\n      }\r\n      @media (max-width: 575px) {\r\n        font-size: rem(24px);\r\n      }\r\n    }\r\n  }\r\n  .btn {\r\n    margin-top: rem(30px);\r\n  }\r\n}\r\n\r\n.blog-list-post + .blog-list-post {\r\n  margin-top: rem(60px);\r\n  @include media(991px) {\r\n    margin-top: rem(40px);\r\n  }\r\n}", ".blog-details-wrapper {\r\n  background-color: var(--light-100);\r\n  @include border-radius(5px);\r\n  .blog-meta {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    gap: rem(30px);\r\n    margin-bottom: rem(30px);\r\n    @include media(575px) {\r\n      gap: rem(10px);\r\n    }\r\n  }\r\n  .blog-details-content {\r\n    padding: rem(40px);\r\n    @include media(575px) {\r\n      padding: rem(20px);\r\n    }\r\n  }\r\n  .blog-quote {\r\n    margin-top: rem(20px);\r\n  }\r\n  .check-list {\r\n    li {\r\n      margin-top: 10px;\r\n    }\r\n    li + li {\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.blog-details-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: rem(10px);\r\n  li {\r\n    span {\r\n      font-weight: 500;\r\n      color: var(--dark-900);\r\n    }\r\n    a {\r\n      padding: rem(4px) rem(12px);\r\n      background-color: #fff;\r\n      @include border-radius(5px);\r\n    }\r\n  }\r\n}\r\n\r\n.blog-quote {\r\n  position: relative;\r\n  padding: rem(30px) rem(20px) rem(30px) rem(90px);\r\n  background-color: #fff;\r\n  @include border-radius(5px);\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  &::before {\r\n    position: absolute;\r\n    content: \"\\f10d\";\r\n    top: rem(30px);\r\n    inset-inline-start: rem(30px);\r\n    font-family: \"Font Awesome 6 Free\";\r\n    font-weight: 900;\r\n    line-height: 1;\r\n    font-size: rem(46px);\r\n    color: var(--primary);\r\n    @include media(575px) {\r\n      opacity: 0.1;\r\n    }\r\n  }\r\n  p {\r\n    font-size: rem(20px);\r\n    @include media(575px) {\r\n      font-size: rem(16px);\r\n    }\r\n  }\r\n  &-name {\r\n    position: relative;\r\n    padding-inline-start: rem(80px);\r\n    margin-top: rem(20px);\r\n    @include media(575px) {\r\n      padding-inline-start: rem(50px);\r\n    }\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      top: 50%;\r\n      left: 0;\r\n      width: 70px;\r\n      height: 2px;\r\n      background-color: var(--primary);\r\n      margin-top: -1px;\r\n      @include media(575px) {\r\n        width: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.blog-prev-next {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: rem(20px);\r\n  margin-top: rem(60px);\r\n  padding: rem(30px);\r\n  border: 1px solid var(--border-color);\r\n  @include border-radius(5px);\r\n  position: relative;\r\n  &::after {\r\n    position: absolute;\r\n    content: '';\r\n    top: 50%;\r\n    left: 50%;\r\n    width: 1px;\r\n    height: 50px;\r\n    background-color: var(--primary);\r\n    margin-top: -25px;\r\n    @include media(575px) {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.blog-arrow-item {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: 20px;\r\n  @include media(575px) {\r\n    width: 100%;\r\n  }\r\n  .icon {\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    background-color: var(--light-100);\r\n    color: var(--dark-900);\r\n    font-size: rem(22px);\r\n    @include border-radius(5px);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    transition: all 0.3s;\r\n    @include media(575px) {\r\n      width: rem(45px);\r\n      height: rem(45px);\r\n    }\r\n    &:hover {\r\n      background-color: var(--primary);\r\n      color: #fff;\r\n    }\r\n  }\r\n  .title {\r\n    max-width: 13ch;\r\n  }\r\n  &.blog-next {\r\n    .title {\r\n      text-align: right;\r\n    }\r\n  }\r\n}\r\n\r\n.comment-wrapper {\r\n  margin-top: rem(60px);\r\n}\r\n\r\n.single-comment {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: rem(30px);\r\n  padding: rem(30px);\r\n  background-color: var(--light-100);\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  .thumb {\r\n    width: 65px;\r\n    height: 65px;\r\n    img {\r\n      @include border-radius(5px);\r\n    }\r\n  }\r\n  .content {\r\n    width: calc(100% - 65px);\r\n    padding-inline-start: rem(20px);\r\n    @include media(575px) {\r\n      width: 100%;\r\n      padding-inline-start: 0;\r\n      margin-top: rem(20px);\r\n    }\r\n  }\r\n}\r\n\r\n.reply-btn {\r\n  font-size: rem(14px);\r\n  font-weight: 700;\r\n  color: var(--dark-900);\r\n  text-transform: uppercase;\r\n  padding: rem(3px) rem(12px);\r\n  border: 1px solid var(--border-color);\r\n  @include border-radius(5px);\r\n  &:hover {\r\n    background-color: #fff;\r\n  }\r\n}\r\n\r\n.reply-wrapper {\r\n  margin-top: rem(60px);\r\n  form {\r\n    margin-top: rem(40px);\r\n    .form-control,\r\n    .form-select {\r\n      border-width: 2px;\r\n    }\r\n  }\r\n}\r\n\r\n.blog-sidebar {\r\n  position: sticky;\r\n  top: 100px;\r\n}\r\n\r\n.blog-sidebar-box {\r\n  padding: rem(30px);\r\n  background-color: #fff;\r\n  @include border-radius(20px);\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  @include media(575px) {\r\n    padding: rem(20px);\r\n  }\r\n  .title {\r\n    font-weight: 700;\r\n    margin-bottom: rem(40px);\r\n  }\r\n}\r\n\r\n.blog-sidebar-search {\r\n  position: relative;\r\n  input {\r\n    height: 56px;\r\n    border: 1px solid var(--border-color);\r\n    @include border-radius(20px);\r\n    padding: rem(10px) rem(20px);\r\n    width: 100%;\r\n  }\r\n  button {\r\n    position: absolute;\r\n    top: 50%;\r\n    inset-inline-end: rem(20px);\r\n    @include transform(translateY(-50%));\r\n    color: var(--dark-900);\r\n  }\r\n}\r\n\r\n.short-blog-list {\r\n  .short-blog + .short-blog {\r\n    margin-top: rem(20px);\r\n  }\r\n}\r\n\r\n.short-blog {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .thumb {\r\n    width: 80px;\r\n    height: 85px;\r\n    display: block;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n      @include border-radius(5px);\r\n    }\r\n  }\r\n  .content {\r\n    width: calc(100% - 80px);\r\n    padding-inline-start: rem(15px);\r\n  }\r\n  .post-date {\r\n    font-size: rem(14px);\r\n    margin-bottom: rem(8px);\r\n    i {\r\n      color: var(--primary);\r\n      margin-inline-end: rem(5px);\r\n    }\r\n  }\r\n}\r\n\r\n.blog-category-list {\r\n  padding: rem(10px) rem(20px);\r\n  @include border-radius(5px);\r\n  background-color: var(--light-100);\r\n  li {\r\n    position: relative;\r\n    padding-inline-start: rem(30px);\r\n    padding-block: rem(10px);\r\n    &::before {\r\n      position: absolute;\r\n      content: \"\\f07c\";\r\n      top: 10px;\r\n      left: 0;\r\n      font-family: \"Font Awesome 6 Free\";\r\n      font-weight: 400;\r\n      color: var(--primary);\r\n    }\r\n    a {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 5px;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      color: var(--dark-900);\r\n      transition: all 0.3s;\r\n      &:hover {\r\n        color: var(--primary);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.blog-tag-list {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  li {\r\n    a {\r\n      padding: rem(5px) rem(15px);\r\n      background-color: var(--light-100);\r\n      @include border-radius(5px);\r\n      transition: all 0.3s;\r\n      &:hover {\r\n        background-color: var(--primary);\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n\r\n.pricing-switcher {\r\n  width: rem(130px);\r\n  display: flex;\r\n  flex-flow: column;\r\n  gap: rem(10px);\r\n  .pricing-switcher-btn {\r\n    @include border-radius(99px);\r\n    border: 1px solid var(--border-color);\r\n    text-transform: capitalize;\r\n    &.active {\r\n      background-color: var(--primary);\r\n      border-color: var(--primary);\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n.pricing-card {\r\n  height: 100%;\r\n  @include border-radius(20px);\r\n  overflow: hidden;\r\n  position: relative;\r\n  &::after {\r\n    position: absolute;\r\n    content: '';\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 50%;\r\n    background: linear-gradient(to top, #010D14, rgba(#010D14, 0));\r\n  }\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n  }\r\n  &-overlay-content {\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    padding: rem(40px);\r\n    z-index: 1;\r\n    @include media(575px) {\r\n      padding: rem(20px);\r\n    }\r\n    .top {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: rem(20px);\r\n      i {\r\n        color: #fff;\r\n        font-size: rem(66px);\r\n        width: rem(60px);\r\n        @include media(575px) {\r\n          font-size: rem(48px);\r\n          width: rem(42px);\r\n        }\r\n      }\r\n      .title {\r\n        font-size: rem(24px);\r\n        color: #fff;\r\n        font-weight: 700;\r\n        width: calc(100% - rem(60px));\r\n        padding-left: rem(15px);\r\n        max-width: 15ch;\r\n        @include media(575px) {\r\n          width: calc(100% - rem(42px));\r\n          font-size: rem(18px);\r\n        }\r\n      }\r\n    }\r\n    .second-title {\r\n      @include media(575px) {\r\n        font-size: rem(16px);\r\n      }\r\n    }\r\n  }\r\n\r\n  .pricing-card-social {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: rem(10px);\r\n    margin-top: rem(30px);\r\n    @include media(575px) {\r\n      margin-top: rem(20px);\r\n    }\r\n    li {\r\n      a {\r\n        width: 26px;\r\n        height: 26px;\r\n        border: 1px solid var(--light-200);\r\n        color: #fff;\r\n        @include border-radius(50%);\r\n        font-size: rem(12px);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pricing-one {\r\n  @include border-radius(20px);\r\n  border-top: 5px solid var(--primary);\r\n  background-color: #fff;\r\n  box-shadow: 0 0 60px rgba(#000, 0.05);\r\n  display: flex;\r\n  flex-flow: column;\r\n  &-header {\r\n    padding: rem(40px) rem(30px);\r\n    text-align: center;\r\n    position: relative;\r\n    background-color: #fff2e6;\r\n    @include border-radius(20px 20px 0 0);\r\n    @include media(575px) {\r\n      padding: rem(20px) rem(25px);\r\n    }\r\n    .title {\r\n      font-weight: 700;\r\n      font-size: rem(30px);\r\n      @include media(575px) {\r\n        font-size: rem(24px);\r\n      }\r\n    }\r\n  }\r\n  &-body {\r\n    padding: rem(20px) rem(40px) rem(30px) rem(40px);\r\n    @include media(575px) {\r\n      padding: rem(25px);\r\n    }\r\n    .price {\r\n      font-size: rem(42px);\r\n      font-weight: 700;\r\n      color: var(--dark-900);\r\n      font-family: var(--display-font);\r\n      margin-bottom: rem(10px);\r\n      @include media(575px) {\r\n        font-size: rem(32px);\r\n      }\r\n      sub {\r\n        color: var(--dark-800);\r\n        font-weight: 400;\r\n        font-size: rem(16px);\r\n        font-family: var(--body-font);\r\n      }\r\n      .yearly-price {\r\n        display: none;\r\n      }\r\n    }\r\n  }\r\n  .pricing-feature-list {\r\n    margin-top: rem(40px);\r\n    li + li {\r\n      margin-top: rem(10px);\r\n    }\r\n    li {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      gap: rem(10px);\r\n      font-size: rem(18px);\r\n      @include media(575px) {\r\n        font-size: rem(16px);\r\n      }\r\n    }\r\n  }\r\n  &-footer {\r\n    padding: rem(20px) rem(40px) rem(30px) rem(40px);\r\n    margin-top: auto;\r\n    @include media(575px) {\r\n      padding: rem(20px) rem(25px) rem(25px) rem(25px);\r\n    }\r\n  }\r\n}\r\n\r\n.pricing-section-two {\r\n  .pricing-switcher {\r\n    flex-flow: row;\r\n    .btn {\r\n      padding-block: rem(14px);\r\n      padding-inline: rem(35px);\r\n    }\r\n  }\r\n}\r\n\r\n.pricing-two {\r\n  padding: rem(30px) rem(30px) rem(40px) rem(30px);\r\n  border: 1px solid var(--border-color);\r\n  @include border-radius(20px);\r\n  text-align: center;\r\n  height: 100%;\r\n  .title {\r\n    margin-bottom: rem(30px);\r\n  }\r\n  .price {\r\n    font-size: rem(30px);\r\n    font-weight: 700;\r\n    color: var(--dark-900);\r\n    margin-bottom: rem(10px);\r\n    display: inline-block;\r\n    padding: rem(5px) rem(30px);\r\n    background-color: var(--light-100);\r\n    @include border-radius(5px);\r\n    @include media(575px) {\r\n      font-size: rem(24px);\r\n    }\r\n    sub {\r\n      color: var(--dark-800);\r\n      font-weight: 400;\r\n      font-size: rem(16px);\r\n      font-family: var(--body-font);\r\n    }\r\n    .yearly-price {\r\n      display: none;\r\n    }\r\n  }\r\n  &-feature-list {\r\n    margin-bottom: rem(20px);\r\n    margin-top: rem(10px);\r\n    li + li {\r\n      border-top: 1px solid var(--border-color);\r\n    }\r\n    li {\r\n      padding: rem(12px) 0;\r\n      span {\r\n        color: var(--dark-800);\r\n        display: none;\r\n        @include media(1199px) {\r\n          display: inline-block;\r\n        }\r\n      }\r\n      i {\r\n        color: var(--dark-900);\r\n        @include media(1199px) {\r\n          margin-inline-end: rem(5px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.pricing-two-main {\r\n    text-align: left;\r\n    padding-top: rem(96px);\r\n    .price {\r\n      padding: 0;\r\n      background-color: transparent;\r\n    }\r\n    .pricing-two-feature-list {\r\n      li {\r\n        span {\r\n          display: inline-block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", ".overview-section {\r\n  position: relative;\r\n  z-index: 1;\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 180px;\r\n    background-color: #fff;\r\n    @include media(767px) {\r\n      height: 140px;\r\n    }\r\n  }\r\n  .overview-section-line {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: -2;\r\n    opacity: 0.35;\r\n  }\r\n}\r\n\r\n.overview-content {\r\n  @include media(991px) {\r\n    text-align: center;\r\n  }\r\n  .section-title {\r\n    color: #fff;\r\n  }\r\n  p {\r\n    color: var(--light-200);\r\n    margin-top: rem(20px);\r\n  }\r\n  .btn {\r\n    margin-top: rem(35px);\r\n    @include media(991px) {\r\n      margin-top: rem(20px);\r\n    }\r\n  }\r\n}\r\n\r\n.overview-item-two {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  padding: rem(18px) rem(20px) rem(28px) rem(20px);\r\n  background-color: #1B1C28;\r\n  @include border-radius(20px);\r\n  @include media(575px) {\r\n    justify-content: center;\r\n  }\r\n  .icon {\r\n    width: rem(60px);\r\n    height: rem(60px);\r\n    background-color: var(--primary);\r\n    @include border-radius(20px);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-bottom: rem(-15px);\r\n  }\r\n  .content {\r\n    width: calc(100% - 60px);\r\n    padding-inline-start: rem(35px);\r\n    @include media(1199px) {\r\n      padding-inline-start: rem(20px);\r\n    }\r\n    @include media(575px) {\r\n      width: 100%;\r\n      padding-inline-start: 0;\r\n      margin-top: rem(25px);\r\n      text-align: center;\r\n    }\r\n    .amount {\r\n      font-size: rem(42px);\r\n      font-weight: 700;\r\n      color: var(--primary);\r\n      font-family: var(--display-font);\r\n      @include media(1199px) {\r\n        font-size: rem(36px);\r\n      }\r\n      @include media(575px) {\r\n        font-size: rem(32px);\r\n      }\r\n    }\r\n    p {\r\n      color: var(--light-200);\r\n      margin-top: rem(8px);\r\n    }\r\n  }\r\n}\r\n\r\n.overview-wrapper {\r\n  padding-top: rem(90px);\r\n  position: relative;\r\n  z-index: 2;\r\n  margin-top: rem(70px);\r\n  @include media(1199px) {\r\n    margin-top: rem(30px);\r\n  }\r\n  @include media(767px) {\r\n    padding-top: rem(30px);\r\n  }\r\n  &-inner {\r\n    position: relative;\r\n    padding: rem(60px);\r\n    background-color: #fff;\r\n    @include border-radius(20px);\r\n    box-shadow: 0 0 60px rgba(#000, 0.05);\r\n    z-index: 1;\r\n    overflow: hidden;\r\n    @include media(1199px) {\r\n      padding: rem(40px);\r\n    }\r\n    @include media(767px) {\r\n      text-align: center;\r\n    }\r\n  }\r\n  .section-title {\r\n    margin-bottom: rem(20px);\r\n  }\r\n  .about-bottom-users {\r\n    margin-top: rem(25px);\r\n    @include media(767px) {\r\n      justify-content: center;\r\n    }\r\n  }\r\n  .overview-wrapper-shape {\r\n    position: absolute;\r\n    bottom: 0;\r\n    inset-inline-end: rem(25px);\r\n    z-index: -1;\r\n  }\r\n  .overview-wrapper-man {\r\n    position: absolute;\r\n    inset-inline-end: 0;\r\n    bottom: 0;\r\n    z-index: 1;\r\n    height: 100%;\r\n    @include media(767px) {\r\n      display: none;\r\n    }\r\n  }\r\n}", "\r\n.gallery-section {\r\n  position: relative;\r\n  overflow: hidden;\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    bottom: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 210px;\r\n    background-color: var(--dark-900);\r\n  }\r\n}\r\n\r\n.gallery-item {\r\n  position: relative;\r\n  &:hover {\r\n    .thumb {\r\n      img {\r\n        @include transform(scale(1.1, 1.1));\r\n      }\r\n    }\r\n    .content {\r\n      bottom: rem(30px);\r\n      opacity: 1;\r\n    }\r\n  }\r\n  .thumb {\r\n    height: rem(545px);\r\n    overflow: hidden;\r\n    @include border-radius(20px);\r\n    @include media(1399px) {\r\n      height: rem(445px);\r\n    }\r\n    @include media(1199px) {\r\n      height: rem(354px);\r\n    }\r\n    @include media(991px) {\r\n      height: rem(250px);\r\n    }\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n      @include object-fit;\r\n      @include transition(all 0.5s);\r\n    }\r\n  }\r\n  .content {\r\n    position: absolute;\r\n    bottom: rem(10px);\r\n    left: rem(30px);\r\n    width: calc(100% - rem(60px));\r\n    padding: rem(30px);\r\n    background-color: #fff;\r\n    @include border-radius(20px);\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    gap: rem(15px);\r\n    opacity: 0;\r\n    @include transition(all 0.3s);\r\n    @include media(1199px) {\r\n      left: rem(15px);\r\n      width: calc(100% - rem(30px));\r\n      padding: rem(20px);\r\n    }\r\n    .gallery-item-btn {\r\n      width: rem(50px);\r\n      height: rem(50px);\r\n      background-color: var(--primary);\r\n      color: #fff;\r\n      @include border-radius(50%);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n  }\r\n  &.style-md {\r\n    &::before {\r\n      position: absolute;\r\n      content: '';\r\n      bottom: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 70%;\r\n      background: linear-gradient(to top, #0B0D26, rgba(#D9D9D9, 0));\r\n      @include border-radius(20px);\r\n      z-index: 1;\r\n      opacity: 0;\r\n      @include transition(all 0.3s);\r\n    }\r\n    &:hover {\r\n      &::before {\r\n        opacity: 1;\r\n      }\r\n    }\r\n    .thumb {\r\n      height: rem(380px);\r\n      @include media(1399px) {\r\n        height: rem(310px);\r\n      } \r\n      @include media(1399px) {\r\n        height: rem(280px);\r\n      }\r\n      @include media(767px) {\r\n        height: rem(210px);\r\n      }\r\n    }\r\n    .content {\r\n      z-index: 1;\r\n      background-color: transparent;\r\n      padding: 0;\r\n      p {\r\n        color: #fff;\r\n      }\r\n      h3 {\r\n        a {\r\n          color: #fff;\r\n          &:hover {\r\n            color: var(--primary);\r\n          }\r\n        }\r\n      }\r\n      .gallery-item-btn {\r\n        width: rem(40px);\r\n        height: rem(40px);\r\n        font-size: rem(14px);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n#galleryTabContent {\r\n  .tab-pane {\r\n    &.show {\r\n      .row {\r\n        div[class*=\"col-\"] {\r\n          opacity: 1;\r\n          transform: translateY(0);\r\n        }\r\n      }\r\n    }\r\n    .row {\r\n      div[class*=\"col-\"] {\r\n        opacity: 0;\r\n        transform: translateY(50px);\r\n        transition: all 0.5s;\r\n        &:nth-child(1) {\r\n          transition-delay: .1s;\r\n        }\r\n        &:nth-child(2) {\r\n          transition-delay: .2s;\r\n        }\r\n        &:nth-child(3) {\r\n          transition-delay: .3s;\r\n        }\r\n        &:nth-child(4) {\r\n          transition-delay: .4s;\r\n        }\r\n        &:nth-child(5) {\r\n          transition-delay: .5s;\r\n        }\r\n        &:nth-child(6) {\r\n          transition-delay: .6s;\r\n        }\r\n        &:nth-child(7) {\r\n          transition-delay: .7s;\r\n        }\r\n        &:nth-child(8) {\r\n          transition-delay: .8s;\r\n        }\r\n        &:nth-child(9) {\r\n          transition-delay: .9s;\r\n        }\r\n        &:nth-child(10) {\r\n          transition-delay: .10s;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.gallery-slider {\r\n  .slick-list {\r\n    margin: 0 rem(-15px);\r\n    @include media(991px) {\r\n      margin: 0 rem(-10px);\r\n    }\r\n  }\r\n  .gallery-slide {\r\n    padding: 0 rem(15px);\r\n    @include media(991px) {\r\n      padding: 0 rem(10px);\r\n    }\r\n    &.slick-active:not(.slick-current) {\r\n      .gallery {\r\n        .thumb {\r\n          @include border-radius(0 20px 20px 0)\r\n        }\r\n      }\r\n    }\r\n    &.slick-current ~ .slick-active {\r\n      .gallery {\r\n        .thumb {\r\n          @include border-radius(20px 0 0 20px)\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", ".video-section-one {\r\n  position: relative;\r\n  z-index: 1;\r\n  &::before {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 180px;\r\n    background-color: var(--light-100);\r\n    z-index: -1;\r\n    @include media(991px) {\r\n      height: 120px;\r\n    }\r\n    @include media(767px) {\r\n      height: 80px;\r\n    }\r\n  }\r\n}\r\n\r\n.video-wrapper {\r\n  @include border-radius(20px);\r\n  overflow: hidden;\r\n  position: relative;\r\n  img {\r\n    width: 100%;\r\n    height: 100%;\r\n    @include object-fit;\r\n  }\r\n  &-btn {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    width: rem(70px);\r\n    height: rem(70px);\r\n    background-color: #fff;\r\n    @include border-radius(50%);\r\n    color: var(--primary);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: rem(22px);\r\n    @include media(575px) {\r\n      width: rem(45px);\r\n      height: rem(45px);\r\n      font-size: rem(14px);\r\n    }\r\n  }\r\n}", ".choose-section {\r\n  position: relative;\r\n  overflow: hidden;\r\n  z-index: 1;\r\n  &-line {\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 0;\r\n    opacity: 0.35;\r\n    z-index: -1;\r\n  }\r\n}\r\n\r\n.choose-item {\r\n  padding: rem(20px) rem(30px);\r\n  background-color: var(--dark-700);\r\n  @include border-radius(20px);\r\n  border-left: 1px solid var(--primary);\r\n  &-title {\r\n    color: #fff;\r\n    margin-bottom: rem(12px);\r\n  }\r\n  p {\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.choose-thumb-one {\r\n  padding-right: 30px;\r\n  position: relative;\r\n  img {\r\n    @include border-radius(50px 0 0 50px);\r\n  }\r\n  &::after {\r\n    position: absolute;\r\n    content: '';\r\n    top: 0;\r\n    right: 0;\r\n    width: 8px;\r\n    height: 100%;\r\n    background-color: var(--secondary);\r\n    @include border-radius(50px);\r\n  }\r\n}\r\n\r\n.choose-thumb-two {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-top: rem(30px);\r\n  .content {\r\n    width: 190px;\r\n    background-color: var(--primary);\r\n    display: flex;\r\n    flex-flow: column;\r\n    justify-content: center;\r\n    padding: rem(30px);\r\n    @include border-radius(20px 0 0 20px);\r\n    h3 {\r\n      color: #fff;\r\n      font-family: var(--display-font);\r\n      font-weight: 700;\r\n      font-size: rem(66px);\r\n      margin-top: rem(-10px);\r\n    }\r\n    p {\r\n      color: #fff;\r\n      font-size: rem(18px);\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  img {\r\n    width: calc(100% - 190px);\r\n    @include object-fit;\r\n    @include border-radius(0 50px 50px 0);\r\n  }\r\n}", ".contact-item {\r\n  padding: rem(30px);\r\n  background-color: var(--light-100);\r\n  height: 100%;\r\n  .icon {\r\n    width: rem(40px);\r\n    height: rem(40px);\r\n    background-color: #fff;\r\n    color: var(--primary);\r\n    @include border-radius(5px);\r\n    display: inline-flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: rem(20px);\r\n  }\r\n  .content {\r\n    margin-top: rem(20px);\r\n    p {\r\n      margin-top: rem(15px);\r\n    }\r\n    a {\r\n      word-break: break-all;\r\n    }\r\n  }\r\n}\r\n\r\n.contact-map {\r\n  filter: grayscale(1);\r\n  iframe {\r\n    width: 100%;\r\n    height: 650px;\r\n    border: none;\r\n    @include media(1399px) {\r\n      height: 550px;\r\n    }\r\n    @include media(991px) {\r\n      height: 450px;\r\n    }\r\n    @include media(575px) {\r\n      height: 300px;\r\n    }\r\n  }\r\n}"]}