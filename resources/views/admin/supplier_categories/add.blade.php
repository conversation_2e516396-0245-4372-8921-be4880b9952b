@extends('admin.layouts.master')
@section('title', 'Supplier Category Creation')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Add New Supplier Category</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="{{ route('suppliercategories.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Supplier Category
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Add Supplier Category</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to add new supplier category</h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard">
                    <form action="{{ route('suppliercategories.store') }}" method="POST" id="form" enctype="multipart/form-data">
                        @csrf
                        <fieldset class="wizard-fieldset show">
                            <div class="row gy-3">
                                {{-- Image upload section commented out --}}
                                {{-- <div class="col-sm-12">
                                    <label class="form-label">Category Logo</label>
                                    <div class="position-relative">
                                        <div class="upload-image-wrapper d-flex align-items-center gap-3">
                                            <div
                                                class="uploaded-img d-none position-relative h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50">
                                                <button type="button"
                                                    class="uploaded-img__remove position-absolute top-0 end-0 z-1 text-2xxl line-height-1 me-8 mt-8 d-flex">
                                                    <iconify-icon icon="radix-icons:cross-2"
                                                        class="text-xl text-danger-600"></iconify-icon>
                                                </button>
                                                <img id="uploaded-img__preview" class="w-100 h-100 object-fit-cover"
                                                    src="{{ old('image', asset('assets/images/user.png')) }}" alt="image">
                                            </div>

                                            <label
                                                class="upload-file h-120-px w-120-px border input-form-light radius-8 overflow-hidden border-dashed bg-neutral-50 bg-hover-neutral-200 d-flex align-items-center flex-column justify-content-center gap-1"
                                                for="upload-file">
                                                <iconify-icon icon="solar:camera-outline"
                                                    class="text-xl text-secondary-light"></iconify-icon>
                                                <span class="fw-semibold text-secondary-light">Upload</span>
                                                <input id="upload-file" type="file" hidden name="image">
                                            </label>
                                        </div>
                                    </div>
                                </div> --}}
                                <div class="col-sm-6">
                                    <label class="form-label">Category Name<span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="name"
                                        placeholder="Enter Category Name" value="{{ old('name') }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Status<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="stauts" name="status">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>

                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                    <a href="{{ route('suppliercategories.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                                    <button type="submit" class="form-wizard-next-btn btn btn-primary-600 px-32">Save</button>
                                </div>
                            </div>
                        </fieldset>
                        <fieldset class="wizard-fieldset">
                            <h6 class="text-md text-neutral-500">Business Info</h6>
                        </fieldset>
                    </form>

                </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>
@stop

@section('script')
    <script>
        $(document).ready(function() {
            // Initialize jQuery validation
            $("form").validate({
                ignore: [], // Ensures Select2 fields are not ignored
                rules: {
                    name: {
                        required: true
                    }
                },
                messages: {
                    name: "Category name is required",

                },
                errorPlacement: function(error, element) {
                    // Handle Select2 validation error placement
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });


            // Trigger validation on blur for all fields
            $(document).on("focusout", "input, select, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) { // Check if the form has been initialized with validate()
                    $(this).valid();
                }
            });

        });

        // =============================== Upload Single Image js commented out ================================================
        /*
        const fileInput = document.getElementById("upload-file");
        const imagePreview = document.getElementById("uploaded-img__preview");
        const uploadedImgContainer = document.querySelector(".uploaded-img");
        const removeButton = document.querySelector(".uploaded-img__remove");

        fileInput.addEventListener("change", (e) => {
            if (e.target.files.length) {
                const src = URL.createObjectURL(e.target.files[0]);
                imagePreview.src = src;
                uploadedImgContainer.classList.remove('d-none');
            }
        });
        removeButton.addEventListener("click", () => {
            imagePreview.src = "";
            uploadedImgContainer.classList.add('d-none');
            fileInput.value = "";
        });
        */
        // =============================== Upload Single Image js End here ================================================
    </script>
@stop
@section('css')
    <style>
        .error-border {
            border: 1px solid #dc3545 !important;
            /* Bootstrap red */
            border-radius: 5px;
        }
    </style>
@stop
