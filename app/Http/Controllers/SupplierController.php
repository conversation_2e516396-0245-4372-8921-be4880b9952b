<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\District;
use App\Models\Supplier;
use App\Models\State;
use App\Models\SupplierCategory;
use App\Models\SupplierService;
use App\Models\SupplierImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;

class SupplierController extends Controller
{
    //
    public function index()
    {
        $categories = SupplierCategory::where('status', 1)->orderBy('name')->get();
        $states = State::where('status', 1)->orderBy('name')->get();
        // Don't load all districts at once - use AJAX loading instead
        $districts = collect(); // Empty collection
        // Don't load all areas at once - use AJAX loading instead
        $areas = collect(); // Empty collection
        return view('admin.suppliers.list', compact('categories', 'states', 'districts', 'areas'));
    }

    public function getSuppliersData(Request $request)
    {
        $suppliers = Supplier::with(['suppliercategory', 'area', 'district', 'state']);

        if ($request->has('searchkey') && $request->searchkey != '') {
            $suppliers->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                  ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        if ($request->has('searchCategory') && $request->searchCategory != '') {
            $suppliers->where('category_id', $request->searchCategory);
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $suppliers->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $suppliers->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $suppliers->where('area_id', $request->searchArea);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
            $suppliers->where('verified', $request->searchVerified);
        }

        return DataTables::of($suppliers)
            ->addColumn('category', function ($supplier) {
                return $supplier->suppliercategory ? $supplier->suppliercategory->name : 'N/A';
            })
            ->addColumn('area', function ($supplier) {
                return $supplier->area ? $supplier->area->name : 'N/A';
            })
            ->addColumn('action', function ($row) {
                $output = '';

                // Add view button
                $output .= '
                    <a href="' . route('suppliers.show', $row->id) . '" class="w-32-px h-32-px bg-info-focus text-info-main rounded-circle d-inline-flex align-items-center justify-content-center">
                        <iconify-icon icon="lucide:eye"></iconify-icon>
                    </a>';

                // Add edit button
                $output .= '<button class="btn btn-danger btn-sm delete-supplier" data-id="'. $row->id .'" title="Delete">
                                <iconify-icon icon="lucide:trash-2"></iconify-icon>
                            </button>';

                return $output;
            })
            ->filter(function ($query) use ($request) {
                if ($request->has('searchCategory') && $request->searchCategory != '') {
                    $query->where('category_id', $request->searchCategory);
                }
                if ($request->has('searchArea') && $request->searchArea != '') {
                    $query->where('area_id', $request->searchArea);
                }
                if ($request->has('searchkey') && $request->searchkey != '') {
                    $query->where(function ($q) use ($request) {
                        $q->where('business_title', 'like', "%{$request->searchkey}%");
                    });
                }
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }
    public function  show($id)  {
        $supplier=Supplier::where('id',$id)->first();
        $supplier_services=SupplierService::where('supplier_id',$supplier->id)->get();
        $supplier_images=SupplierImage::where('supplier_id',$supplier->id)->get();
        return view('admin.suppliers.view',compact('supplier','supplier_services','supplier_images'));

    }

    /**
     * Update the status of a supplier.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the supplier
            $supplier = Supplier::findOrFail($request->supplier_id);

            // Update status
            $supplier->status = $request->status;
            $supplier->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Supplier status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update supplier status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the verification status of a supplier.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateVerificationStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
            'verified' => 'required|in:0,1',
        ]);

        try {
            // Find the supplier
            $supplier = Supplier::findOrFail($request->supplier_id);

            // Update verification status
            $supplier->verified = $request->verified;
            $supplier->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Supplier verification status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update supplier verification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a supplier.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'supplier_id' => 'required|exists:suppliers,id',
        ]);

        try {
            // Find the supplier
            $supplier = Supplier::findOrFail($request->supplier_id);

            // Delete the supplier
            $supplier->delete();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Supplier deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete supplier: ' . $e->getMessage()
            ], 500);
        }
    }
    /**
     * Export suppliers data to CSV based on filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        // Fetch suppliers query with all relationships
        $suppliers = Supplier::with([
            'suppliercategory',
            'area',
            'district',
            'state'
        ]);

        // Apply filters
        if ($request->has('searchkey') && $request->searchkey != '') {
            $suppliers->where(function ($q) use ($request) {
                $q->where('business_title', 'like', "%{$request->searchkey}%")
                  ->orWhere('phone', 'like', "%{$request->searchkey}%");
            });
        }

        if ($request->has('searchCategory') && $request->searchCategory != '') {
            $suppliers->where('category_id', $request->searchCategory);
        }

        if ($request->has('searchState') && $request->searchState != '') {
            $suppliers->where('state_id', $request->searchState);
        }

        if ($request->has('searchDistrict') && $request->searchDistrict != '') {
            $suppliers->where('district_id', $request->searchDistrict);
        }

        if ($request->has('searchArea') && $request->searchArea != '') {
            $suppliers->where('area_id', $request->searchArea);
        }

        if ($request->has('searchVerified') && $request->searchVerified !== '' && $request->searchVerified !== null) {
            $suppliers->where('verified', $request->searchVerified);
        }

        // Get all suppliers (no pagination)
        $suppliers = $suppliers->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Business Title',
            'Category',
            'Description',
            'Phone',
            'Email',
            'Address',
            'Area',
            'District',
            'State',
            'Website',
            'Facebook',
            'Twitter',
            'LinkedIn',
            'Instagram',
            'YouTube',
            'Status',
            'Verified',
            'Created At'
        ]);

        // Add data rows
        foreach ($suppliers as $supplier) {
            $csv->insertOne([
                $supplier->business_title,
                $supplier->suppliercategory->name ?? 'N/A',
                $supplier->description,
                $supplier->phone,
                $supplier->email,
                $supplier->address,
                $supplier->area->name ?? 'N/A',
                $supplier->district->name ?? 'N/A',
                $supplier->state->name ?? 'N/A',
                $supplier->website,
                $supplier->facebook,
                $supplier->twitter,
                $supplier->linked_in,
                $supplier->instagram,
                $supplier->youtube,
                $supplier->status ? 'Active' : 'Inactive',
                $supplier->verified ? 'Verified' : 'Not Verified',
                $supplier->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'Suppliers_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }
    /**
     * Get suppliers data for DataTables.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getsuppliers(Request $request)
    {
        // This is an alias method that calls the existing getSuppliersData method
        return $this->getSuppliersData($request);
    }
}
