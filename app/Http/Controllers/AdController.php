<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class AdController extends Controller
{

    //
    public function index()
    {
        return view('admin.ads.list');
    }
    public function add()
    {
        return view('admin.ads.add');
    }
    //
    // Store method to save ad category data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'link'        => 'string',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);
        $data = $request->all();
        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/ads/
            $filePath = $file->storeAs('ads', $filename, 'public');

            // Save the file path in the database
            $data['image'] = $filePath;
        }

        // Save data to database
        Ad::create($data);

        // Redirect to ad list page with success message
        return redirect()->route('ads.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $ad = Ad::where('id', $id)->first();


        return view('admin.ads.edit', compact('ad'));
    }
    public function update(Request $request, Ad $ad)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'link'        => 'string',
            'image'         => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);

        $data = $request->except(['image']); // Exclude logo from mass update initially

        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/ads/
            $filePath = $file->storeAs('ads', $filename, 'public');

            // Delete the old logo if exists
            if ($ad->image && Storage::disk('public')->exists($ad->image)) {
                Storage::disk('public')->delete($ad->image);
            }

            // Save the new file path
            $data['image'] = $filePath;
        }
        // Update vendorcategory data
        $ad->update($data);

        // Redirect to vendorcategory list page with success message
        return redirect()->route('ads.index')->with('success', $ad->name . ' updated successfully!');
    }

    public function getads(Request $request)
    {
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();
            // Fetch base vendor categories query
            $ads = Ad::select(['id', 'name', 'image', 'status', 'created_at']);



            return DataTables::of($ads)
                ->addColumn('status', function ($row) {
                    if ($row->status == 1) {
                        return '<span class="badge badge-sm text-sm fw-normal text-success-600 bg-success-100 px-20 py-9 radius-4 text-white">Active</span>';
                    } else {
                        return '<span class="badge badge-sm text-sm fw-normal text-danger-600 bg-danger-100 px-20 py-9 radius-4 text-white">Inactive</span>';
                    }
                })
                ->addColumn('image', function ($row) {
                    if ($row->image) {
                        $imageUrl = url('storage/'.$row->image); // update path if needed
                        return '<img src="' . $imageUrl . '" alt="' . $row->name . '"  height="100" class="rounded">';
                    } else {
                        return '<img src="' . asset('assets/images/default.png') . '" width="50" height="50" class="rounded">';
                    }
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'vendor ad-edit' permission
                    if ($user->can('ad-edit')) {
                        $output .= '
                            <a href="' . route('ads.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }


                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ;
                        });
                    }
                })

                ->rawColumns(['image', 'status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }

}
