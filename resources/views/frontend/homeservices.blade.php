@extends('frontend.layouts.master')
@section('content')

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Top-Rated home services in {{ @$category->name }} Services</h2>
				<span class="ipn-subtitle">Discover professional and reliable {{ strtolower(@$category->name) }} service providers in your area. Compare profiles, read reviews, and book the best homeservice for your job today.</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">

	<div class="container">

		<div class="row">
			<div class="col-lg-12 col-md-12">
				<div class="filter_search_opt">
					<a href="javascript:void(0);" class="btn btn-dark full-width mb-4" onclick="openFilterSearch()">
						<span class="svg-icon text-light svg-icon-2hx me-2">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z" fill="currentColor"/>
							</svg>
						</span>Open Filter Option
					</a>
				</div>
			</div>
		</div>

		<div class="row">

			<!-- property-sidebar code  -->
			<!-- property Sidebar -->
                <div class="col-lg-4 col-md-12 col-sm-12">
                    <div class="simple-sidebar sm-sidebar" id="filter_search"  style="left:0;">

                        <div class="search-sidebar_header">
                            <h4 class="ssh_heading">Close Filter</h4>
                            <button onclick="closeFilterSearch()" class="w3-bar-item w3-button w3-large"><i class="fa-regular fa-circle-xmark fs-5 text-muted-2"></i></button>
                        </div>

                        <!-- Find New Property -->
                        <div class="sidebar-widgets">

                            <div class="search-inner p-0">
                                {{-- here i want to move serach icon from left to right --}}
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h3 class="title">Search Home Service</h3>
                                </div>

                                <div class="filter-search-box">
                                    <div class="form-group">
                                        <div class="position-relative">
                                            <input type="text" class="form-control rounded-3 ps-2" id="searchkey" value="{{ request()->searchkey }}" placeholder="Search by homeservice Name/Phone…">
                                            <div id="searchButton" class="position-absolute top-50 end-0 translate-middle-y me-2" style="cursor: pointer;">
                                                   <svg fill="#0065ff" width="20px" height="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M21.71,20.29,18,16.61A9,9,0,1,0,16.61,18l3.68,3.68a1,1,0,0,0,1.42,0A1,1,0,0,0,21.71,20.29ZM11,18a7,7,0,1,1,7-7A7,7,0,0,1,11,18Z"/></svg>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Verified Filter Section -->
                                <div class="verified-filter-section mb-4">
                                    <div class="verified-toggle-container bg-white rounded-3 shadow-sm border p-3">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="d-flex align-items-center">
                                                <div class="verified-icon-badge me-3">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"/>
                                                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1 fw-semibold text-dark">Show Verified Only</h6>
                                                    {{-- <small class="text-black">Show trusted & verified service providers</small> --}}
                                                </div>
                                            </div>

                                            <button type="button" class="verified-toggle-btn {{ request()->verified ? 'active' : '' }}" id="verifiedToggle" data-verified="{{ request()->verified ? 'true' : 'false' }}">
                                                <span class="toggle-text">{{ request()->verified ? 'ON' : 'OFF' }}</span>
                                                <div class="toggle-slider">
                                                    <div class="toggle-circle"></div>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <style>
                                .verified-filter-section {
                                    position: relative;
                                }

                                .verified-toggle-container {
                                    transition: all 0.3s ease;
                                    border: 1px solid #e9ecef;
                                }

                                .verified-toggle-container:hover {
                                    border-color: #007bff;
                                    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
                                }

                                .verified-icon-badge {
                                    width: 40px;
                                    height: 40px;
                                    background: linear-gradient(135deg, #007bff, #0056b3);
                                    border-radius: 10px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: white;
                                    transition: transform 0.3s ease;
                                }

                                .verified-toggle-container:hover .verified-icon-badge {
                                    transform: scale(1.05);
                                }

                                .verified-toggle-btn {
                                    background: #f8f9fa;
                                    border: 2px solid #dee2e6;
                                    border-radius: 25px;
                                    padding: 8px 16px;
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    min-width: 100px;
                                    justify-content: space-between;
                                }

                                .verified-toggle-btn:hover {
                                    border-color: #007bff;
                                    background: #f0f8ff;
                                }

                                .verified-toggle-btn.active {
                                    background: #007bff;
                                    border-color: #007bff;
                                    color: white;
                                }

                                .toggle-text {
                                    font-weight: 600;
                                    font-size: 12px;
                                    letter-spacing: 0.5px;
                                }

                                .toggle-slider {
                                    width: 32px;
                                    height: 18px;
                                    background: #dee2e6;
                                    border-radius: 12px;
                                    position: relative;
                                    transition: background 0.3s ease;
                                }

                                .verified-toggle-btn.active .toggle-slider {
                                    background: rgba(255, 255, 255, 0.3);
                                }

                                .toggle-circle {
                                    width: 14px;
                                    height: 14px;
                                    background: white;
                                    border-radius: 50%;
                                    position: absolute;
                                    top: 2px;
                                    left: 2px;
                                    transition: transform 0.3s ease;
                                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                                }

                                .verified-toggle-btn.active .toggle-circle {
                                    transform: translateX(14px);
                                }

                                @media (max-width: 768px) {
                                    .verified-toggle-container {
                                        padding: 15px;
                                    }

                                    .verified-toggle-btn {
                                        min-width: 80px;
                                        padding: 6px 12px;
                                    }

                                    .verified-icon-badge {
                                        width: 35px;
                                        height: 35px;
                                    }
                                }
                                </style>





                                <!-- State Filter -->
                                <div class="filter_wraps">
                                    <div class="single_search_boxed">
                                        <div class="form-group">
                                            <label class="mb-2">Select State</label>
                                            <select id="state" class="form-control">
                                                @if (request()->has('searchState'))
                                                    <option value="{{ request()->searchState }}" selected>{{ $stateName }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- District Filter -->
                                <div class="filter_wraps">
                                    <div class="single_search_boxed">
                                        <div class="form-group">
                                            <label class="mb-2">Select District</label>
                                            <select id="district" class="form-control">
                                                @if (request()->has('searchDistrict'))
                                                    <option value="{{ request()->searchDistrict }}" selected>{{ $districtName }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Area Filter -->
                                <div class="filter_wraps">
                                    <div class="single_search_boxed">
                                        <div class="form-group">
                                            <label class="mb-2">Select Area</label>
                                            <select id="area" class="form-control">
                                                @if (request()->has('searchArea'))
                                                    <option value="{{ request()->searchArea }}" selected>{{ $areaName }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <!-- cleare filter button -->
                                <div class="filter_wraps">
                                    <div class="single_search_boxed">
                                        <div class="form-group ">
                                            <a href="/homeservices/{{ $category->slug }}" class="btn btn-primary full-width" onclick="clearFilters()">Clear Filters</a>
                                        </div>
                                    </div>
                                </div>

                                <!-- ad-->
                                <div class="row">
                                    <div class="col-12">
                                        <a href="{{ $ads[8]->link }}" target="_blank">
                                            <img src="{{ url('storage/' . $ads[8]->image) }}" class="img-fluid" alt="">
                                        </a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- Sidebar End -->

                </div>

			<div class="col-lg-8 col-md-12 list-layout">



				<div class="row justify-content-center">

					<!-- propertys-6 code  -->

                        @forelse ($homeservices as $homeservice)
                        <!-- Single Property Start -->
                        <div class="col-xl-12 col-lg-12 col-md-12">
                            <div class="property-listing property-1 bg-white p-2 rounded">
                                @if ($homeservice->verified == 1)

                                <div class="position-absolute top-0 left-0 ms-3 mt-3 z-1">
                                    <div class="label bg-success text-light d-inline-flex align-items-center justify-content-center">
                                        <span class="svg-icon text-light svg-icon-2hx me-1">
                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                                                <path d="M14.854 11.321C14.7568 11.2282 14.6388 11.1818 14.4998 11.1818H14.3333V10.2272C14.3333 9.61741 14.1041 9.09378 13.6458 8.65628C13.1875 8.21876 12.639 8 12 8C11.361 8 10.8124 8.21876 10.3541 8.65626C9.89574 9.09378 9.66663 9.61739 9.66663 10.2272V11.1818H9.49999C9.36115 11.1818 9.24306 11.2282 9.14583 11.321C9.0486 11.4138 9 11.5265 9 11.6591V14.5227C9 14.6553 9.04862 14.768 9.14583 14.8609C9.24306 14.9536 9.36115 15 9.49999 15H14.5C14.6389 15 14.7569 14.9536 14.8542 14.8609C14.9513 14.768 15 14.6553 15 14.5227V11.6591C15.0001 11.5265 14.9513 11.4138 14.854 11.321ZM13.3333 11.1818H10.6666V10.2272C10.6666 9.87594 10.7969 9.57597 11.0573 9.32743C11.3177 9.07886 11.6319 8.9546 12 8.9546C12.3681 8.9546 12.6823 9.07884 12.9427 9.32743C13.2031 9.57595 13.3333 9.87594 13.3333 10.2272V11.1818Z" fill="currentColor"></path>
                                            </svg>
                                        </span> Verified
                                    </div>
                                </div>

                                @endif
                                <div class="listing-img-wrapper">
                                    <a href="/homeservice-details/{{ $homeservice->slug }}" target="_blank">
                                        {{-- here we need replace logo with vedor_images first image --}}
                                        @if ($homeservice->homeserviceimages && $homeservice->homeserviceimages->count() > 0 && $homeservice->homeserviceimages->first())
                                            <img src="{{ url('storage/' . $homeservice->homeserviceimages->first()->image_path) }}" class="img-fluid mx-auto rounded" alt="" />
                                        @elseif(!empty($homeservice->logo))
                                            <img src="{{ url('storage/' . $homeservice->logo) }}" class="img-fluid mx-auto rounded" alt="" />
                                        @else
                                            <img src="/frontend/assets/img/homeservice-placeholder.jpg" class="img-fluid mx-auto rounded" alt="" />
                                        @endif


                                    </a>
                                </div>

                                <div class="listing-content">

                                    <div class="listing-detail-wrapper-box">
                                        <div class="listing-detail-wrapper d-flex align-items-center justify-content-between">
                                            <div class="listing-short-detail">
                                                <span class="label bg-light-primary text-primary d-inline-flex mb-1">{{$homeservice->homeservicecategory->name}}</span>
                                                <h4 class="listing-name mb-1 mt-3"><a href="/homeservice-details/{{ $homeservice->slug }}" target="_blank">{{ $homeservice->business_title }}</a></h4>


                                            </div>
                                            {{-- <div class="list-price">
                                                <h6 class="listing-card-info-price text-primary"><?php //echo $item['price']; ?></h6>
                                            </div> --}}
                                        </div>
                                    </div>

                                    {{-- <div class="price-features-wrapper">
                                        <div class="list-fx-features d-flex align-items-center justify-content-between mt-3 mb-1">
                                            <div class="listing-card d-flex align-items-center">
                                                <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-building-shield fs-xs"></i></div><span class="text-muted-2 fs-sm">3BHK</span>
                                            </div>
                                            <div class="listing-card d-flex align-items-center">
                                                <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-bed fs-xs"></i></div><span class="text-muted-2 fs-sm">3 Beds</span>
                                            </div>
                                            <div class="listing-card d-flex align-items-center">
                                                <div class="square--25 text-muted-2 fs-sm circle gray-simple me-1"><i class="fa-solid fa-clone fs-xs"></i></div><span class="text-muted-2 fs-sm">1800 SQFT</span>
                                            </div>
                                        </div>
                                    </div> --}}

                                    <div class="listing-footer-wrapper">
                                        <div class="listing-locate">
                                            <span class="listing-location text-muted-2"><i class="fa-solid fa-location-pin me-1"></i>{{$homeservice->address}}</span>
                                        </div>
                                        <div class="listing-detail-btn">
                                            <a href="/homeservice-details/{{ $homeservice->slug }}" class="btn btn-sm px-4 fw-medium btn-primary" target="_blank">View</a>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                        <!-- Single Property End -->
                        @empty
                        <!-- No Data State -->
                        <div class="col-lg-12">
                            <div class="no-data-state text-center py-5">
                                <div class="no-data-card bg-white rounded-3 shadow-sm p-5 mx-auto" style="max-width: 600px;">
                                    <!-- Illustration -->
                                    <div class="no-data-illustration mb-4">
                                        <div class="illustration-container d-inline-block position-relative">
                                            <div class="main-icon" style="font-size: 80px; color: #e9ecef;">
                                                <i class="fa-solid fa-tools"></i>
                                            </div>
                                            <div class="search-icon position-absolute" style="top: -10px; right: -10px; font-size: 30px; color: #6c757d;">
                                                <i class="fa-solid fa-magnifying-glass"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Main Message -->
                                    <h3 class="no-data-title mb-3" style="color: #495057; font-weight: 600;">
                                        No Home Services Found
                                    </h3>

                                    <p class="no-data-message text-muted mb-4" style="font-size: 16px; line-height: 1.6;">
                                        We couldn't find any home service providers matching your search criteria.
                                        Try adjusting your filters or search in a different area.
                                    </p>

                                    <!-- Suggestions -->
                                    <div class="suggestions mb-4">
                                        <h5 class="suggestions-title mb-3" style="color: #6c757d; font-size: 18px;">
                                            <i class="fa-solid fa-lightbulb me-2" style="color: #ffc107;"></i>
                                            Try These Suggestions:
                                        </h5>

                                        <div class="row text-start">
                                            <div class="col-md-6">
                                                <ul class="suggestion-list" style="list-style: none; padding: 0;">
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Clear all filters and search again</span>
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Try searching in nearby areas</span>
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Use broader search terms</span>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <ul class="suggestion-list" style="list-style: none; padding: 0;">
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Check different service categories</span>
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Turn off "Verified Only" filter</span>
                                                    </li>
                                                    <li class="mb-2">
                                                        <i class="fa-solid fa-check-circle me-2" style="color: #28a745;"></i>
                                                        <span>Browse all available services</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="no-data-actions">
                                        <div class="row justify-content-center">
                                            <div class="col-auto">
                                                <button type="button" class="btn btn-primary px-4 py-2 me-3" onclick="clearAllFilters()" style="border-radius: 25px;">
                                                    <i class="fa-solid fa-filter-circle-xmark me-2"></i>
                                                    Clear All Filters
                                                </button>
                                            </div>
                                            <div class="col-auto">
                                                <a href="/find_homeservices" class="btn btn-outline-primary px-4 py-2" style="border-radius: 25px;">
                                                    <i class="fa-solid fa-home me-2"></i>
                                                    Browse All Services
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Service Provider CTA -->
                                    <div class="service-provider-cta mt-4 pt-4 border-top">
                                        <p class="text-muted mb-3">
                                            <strong>Are you a service provider?</strong>
                                        </p>
                                        <a href="/homeservice_registration" class="btn btn-success px-4 py-2" style="border-radius: 25px;">
                                            <i class="fa-solid fa-plus-circle me-2"></i>
                                            Register Your Service
                                        </a>
                                        <p class="text-muted mt-2" style="font-size: 14px;">
                                            Join our platform and connect with customers in your area
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforelse

				</div>

				<!-- Pagination -->
				@if ($homeservices->lastPage() > 1)
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <ul class="pagination p-center">

                                {{-- Previous Page Link --}}
                                @if ($homeservices->onFirstPage())
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fa-solid fa-arrow-left-long"></i></span>
                                    </li>
                                @else
                                    <li class="page-item">
                                        <a class="page-link" href="{{ $homeservices->previousPageUrl() }}"><i class="fa-solid fa-arrow-left-long"></i></a>
                                    </li>
                                @endif

                                {{-- Page Number Links --}}
                                @for ($i = 1; $i <= $homeservices->lastPage(); $i++)
                                    @if ($i == $homeservices->currentPage())
                                        <li class="page-item active"><span class="page-link">{{ $i }}</span></li>
                                    @elseif ($i == 1 || $i == $homeservices->lastPage() || ($i >= $homeservices->currentPage() - 1 && $i <= $homeservices->currentPage() + 1))
                                        <li class="page-item"><a class="page-link" href="{{ $homeservices->url($i) }}">{{ $i }}</a></li>
                                    @elseif ($i == 2 || $i == $homeservices->lastPage() - 1)
                                        <li class="page-item disabled"><span class="page-link">...</span></li>
                                    @endif
                                @endfor

                                {{-- Next Page Link --}}
                                @if ($homeservices->hasMorePages())
                                    <li class="page-item">
                                        <a class="page-link" href="{{ $homeservices->nextPageUrl() }}"><i class="fa-solid fa-arrow-right-long"></i></a>
                                    </li>
                                @else
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fa-solid fa-arrow-right-long"></i></span>
                                    </li>
                                @endif

                            </ul>
                        </div>
                    </div>
                    @endif


			</div>

		</div>
	</div>
</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="#" class="btn btn-call-to-act">SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->
@stop
@section('js')
<script>
    // Select Property Types
	$('#area').select2({
		placeholder: "Show All",
		allowClear: true
	});
        $(document).ready(function() {
            $('#area').select2({
                placeholder: 'Select Area',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "areas",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
        });
        // implementing serach option for select area and searchkey
    //  function performSearch() {
    //     alert('verified');
    //     exit();
    //         const searchkey = $('#searchkey').val().trim();
    //         const searchArea = $('#area').val();
    //         const verified = $('#verified').is(':checked');

    //         let queryParams = [];

    //         if (searchkey !== '') {
    //             queryParams.push('searchkey=' + encodeURIComponent(searchkey));
    //         }

    //         if (searchArea !== '' && searchArea !== null) {
    //             queryParams.push('searchArea=' + encodeURIComponent(searchArea));
    //         }
    //         if (verified) {
    //             queryParams.push('verified=' + encodeURIComponent(verified));
    //         }

    //         const queryString = queryParams.length ? '?' + queryParams.join('&') : '';

    //         window.location.href = '/homeservices/{{ $category->slug }}' + queryString;
    //     }

        // Search icon click
        $('#searchButton').on('click', function () {
            performSearch();
        });

        // Area dropdown change
        $('#area').on('change', function () {
            performSearch();
        });

        // Initialize Select2 for all dropdowns
        $('#state, #district, #area').select2({
            placeholder: "Show All",
            allowClear: true
        });

        $(document).ready(function() {
            // Initialize State Select2
            $('#state').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Initialize District Select2 with dependency on State
            $('#district').select2({
                placeholder: 'Select District',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('#state').val();
                        return {
                            get_type: 4,
                            datafrom: "districts",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Initialize Area Select2 with dependency on District
            $('#area').select2({
                placeholder: 'Select Area',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let districtId = $('#district').val();
                        return {
                            get_type: 4,
                            datafrom: "areas_with_pincode",
                            district_id: districtId, // Send selected district_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Clear dependent dropdowns when parent changes
            $('#state').on('change', function() {
                $('#district').val(null).trigger('change');
                $('#area').val(null).trigger('change');
                performSearch();
            });

            $('#district').on('change', function() {
                $('#area').val(null).trigger('change');
                performSearch();
            });

            $('#area').on('change', function() {
                performSearch();
            });

            // Prevent opening district dropdown if state is not selected
            $('#district').on('select2:opening', function(e) {
                if (!$('#state').val()) {
                    Swal.fire({
                        icon: 'info',
                        title: 'Select State First',
                        text: 'Please select a state before choosing a district',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    e.preventDefault();
                }
            });

            // Prevent opening area dropdown if district is not selected
            $('#area').on('select2:opening', function(e) {
                if (!$('#district').val()) {
                    Swal.fire({
                        icon: 'info',
                        title: 'Select District First',
                        text: 'Please select a district before choosing an area',
                        timer: 2000,
                        showConfirmButton: false
                    });
                    e.preventDefault();
                }
            });
        });

        // Update the performSearch function to include state, district and area
        function performSearch() {
            const searchkey = $('#searchkey').val().trim();
            const searchState = $('#state').val();
            const searchDistrict = $('#district').val();
            const searchArea = $('#area').val();
            const category = '{{ isset($category) ? $category->slug : "" }}';
            const verifiedToggle = document.getElementById('verifiedToggle');
            const verified = verifiedToggle ? verifiedToggle.getAttribute('data-verified') === 'true' : false;

            let queryParams = [];

            if (searchkey !== '') {
                queryParams.push('searchkey=' + encodeURIComponent(searchkey));
            }

            if (searchState !== '' && searchState !== null) {
                queryParams.push('searchState=' + encodeURIComponent(searchState));
            }

            if (searchDistrict !== '' && searchDistrict !== null) {
                queryParams.push('searchDistrict=' + encodeURIComponent(searchDistrict));
            }

            if (searchArea !== '' && searchArea !== null) {
                queryParams.push('searchArea=' + encodeURIComponent(searchArea));
            }
            if (verified) {
                queryParams.push('verified=true');
            }

            const queryString = queryParams.length ? '?' + queryParams.join('&') : '';

            window.location.href = '/homeservices/' + category + queryString;
        }

        // Search icon click
        $('#searchButton').on('click', function () {
            performSearch();
        });

        // Area dropdown change
        $('#area').on('change', function () {
            performSearch();
        });
        // Verified toggle button functionality
        $(document).ready(function() {
            const toggleBtn = document.getElementById('verifiedToggle');

            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    const isActive = this.classList.contains('active');
                    const newState = !isActive;

                    // Toggle visual state
                    if (newState) {
                        this.classList.add('active');
                        this.querySelector('.toggle-text').textContent = 'ON';
                        this.setAttribute('data-verified', 'true');
                    } else {
                        this.classList.remove('active');
                        this.querySelector('.toggle-text').textContent = 'OFF';
                        this.setAttribute('data-verified', 'false');
                    }

                    // Trigger search with new verified state
                    performSearch();
                });
            }
        });

        $(document).ready(function () {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function (position) {
                    let lat = position.coords.latitude;
                    let lng = position.coords.longitude;

                    // Reverse geocode using OpenStreetMap (Nominatim API)
                    $.get('https://nominatim.openstreetmap.org/reverse', {
                        lat: lat,
                        lon: lng,
                        format: 'json'
                    }, function (data) {
                        let areaName = data.address.suburb || data.address.village || data.address.town || data.address.city_district;
                        let districtName = data.address.city || data.address.town || data.address.village;
                        let stateName = data.address.state;
                        // Send to backend to match area
                        $.ajax({
                            url: '/api/match-area',
                            type: 'POST',
                            data: {
                                area: areaName,
                                district: districtName,
                                state: stateName
                            },
                            success: function (response) {
                                if (response && response.id) {
                                    let newOption = new Option(response.text, response.id, true, true);
                                    $('#area').append(newOption).trigger('change');
                                }
                            }
                        });

                    });
                });
            } else {
                alert("Geolocation is not supported by your browser.");
            }
        });

        // Clear all filters function
        function clearAllFilters() {
            // Clear search input
            $('#searchkey').val('');

            // Clear verified toggle button
            const verifiedToggle = document.getElementById('verifiedToggle');
            if (verifiedToggle) {
                verifiedToggle.classList.remove('active');
                verifiedToggle.querySelector('.toggle-text').textContent = 'OFF';
                verifiedToggle.setAttribute('data-verified', 'false');
            }

            // Clear all select2 dropdowns
            $('#state').val(null).trigger('change');
            $('#district').val(null).trigger('change');
            $('#area').val(null).trigger('change');

            // Perform search with cleared filters
            performSearch();

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Filters Cleared!',
                text: 'All search filters have been reset.',
                timer: 2000,
                showConfirmButton: false
            });
        }
</script>

<style>
/* No Data State Styles */
.no-data-state {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-data-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.no-data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
}

.illustration-container {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.search-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.suggestion-list li {
    transition: all 0.2s ease;
}

.suggestion-list li:hover {
    transform: translateX(5px);
    color: #495057;
}

.no-data-actions .btn {
    transition: all 0.3s ease;
}

.no-data-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.service-provider-cta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
    margin: 20px -20px -20px -20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .no-data-card {
        padding: 30px 20px !important;
        margin: 0 15px;
    }

    .main-icon {
        font-size: 60px !important;
    }

    .search-icon {
        font-size: 24px !important;
    }

    .no-data-title {
        font-size: 24px !important;
    }

    .no-data-actions .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .no-data-actions .col-auto {
        width: 100%;
    }
}
</style>
@stop
