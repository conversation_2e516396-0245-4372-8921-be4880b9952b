@extends('frontend.layouts.master')
@section('content')

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Post Real Estate Job </h2>
				<span class="ipn-subtitle">We are looking for passionate and driven candidates to join our growing team.
                    If you have a keen eye for property, strong communication skills, and a commitment to excellence, we’d love to hear from you.</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">
	<div class="container">
    @if(session()->has('errors'))
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

		<div class="row">

			<!-- Success/Error Messages -->
			@if(session('success'))
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-success alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-check me-2"></i>
						<strong>Success!</strong> {{ session('success') }}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			@if(session('error'))
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-exclamation me-2"></i>
						<strong>Error!</strong> {{ session('error') }}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			@if($errors->any())
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-exclamation me-2"></i>
						<strong>Please fix the following errors:</strong>
						<ul class="mb-0 mt-2">
							@foreach($errors->all() as $error)
								<li>{{ $error }}</li>
							@endforeach
						</ul>
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			<!-- Submit Form -->
			<div class="col-lg-12 col-md-12">
<form action="{{ route('post_job.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
				<div class="submit-page">

					<!-- Basic Information -->
					<div class="form-submit">
						<h3>Job Details</h3>
						<div class="submit-section">
							<div class="row">

								<div class="form-group col-md-12">
									<label class="mb-2">Title<span class="error"> *</span></label>
									<input type="text" class="form-control" name="job_title" id="job_title" placeholder="Enter Job Title ">
								</div>



								<div class="form-group col-md-6">
									<label class="mb-2">Job Role <span class="error">*</span></label>
									<input type="text" class="form-control" name="job_role" id="job_role" placeholder="Enter Job Role ">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Experience</label>
									<input type="text" class="form-control" name="experience" id="experience" placeholder="Enter Experience ">
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Salary</label>
									<input type="text" class="form-control"name="salary" id="salary" placeholder="Enter Salary 1L to 2L">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">No. Of Positions  </label>
									<input type="text" class="form-control"name="no_of_positions" id="no_of_positions" placeholder="Enter No. Of Positions">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Job Description<span class="error">*</span></label>

									<textarea class="form-control h-120" name="job_description" id="job_description" placeholder="Enter Job Description"></textarea>
									{{-- <input type="textarea" class="form-control"name="job_description" id="job_description"> --}}
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">About Company </label>
                                    <textarea class="form-control h-120" name="about_company" id="about_company" placeholder="Enter About Company"></textarea>

									{{-- <input type="textarea" class="form-control"name="about_company" id="about_company"> --}}
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Company Name <span class="error">*</span></label>
									<input type="text" class="form-control"name="company_name" id="company_name" placeholder="Enter Company Name">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Qualification </label>
									<input type="text" class="form-control"name="qualification" id="qualification" placeholder="Enter Qualification">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Email <span class="error">*</span></label>
									<input type="text" class="form-control"name="email" id="email" placeholder="Enter Email">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Mobile <span class="error">*</span></label>
									<input type="text" class="form-control"name="mobile" id="mobile" placeholder="Enter Mobile">
								</div>
                                <div class="form-group col-md-6">
									<label class="mb-2">State <span class="error">*</span></label>
									<select class="form-control" id="state_id" name="state_id">
                                        <option value="">Select State</option>
                                    </select>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">District<span class="error">*</span></label>
									<select class="form-control" id="district_id" name="district_id">
                                        <option value="">Select District</option>
                                    </select>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Area<span class="error">*</span></label>
									<select class="form-control" id="area_id" name="area_id">
                                        <option value="">Select Area</option>
                                    </select>
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Address<span class="error">*</span></label>
									<input type="text" class="form-control" name="address" id="address" placeholder="Enter Address">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Nearest Landmark</label>
									<input type="text" class="form-control" name="nearest_landmark" id="nearest_landmark" placeholder="Enter Nearest Landmark">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Distance</label>
									<input type="text" class="form-control" name="distance" id="distance" placeholder="Enter Distance (e.g., 2 km from Metro Station)">
								</div>

							</div>
						</div>
					</div>

					<!-- Verified Listing Upgrade -->
					<div class="form-submit">
						<div class="verified-listing-upgrade">
							<div class="row">
								<div class="col-lg-12">
									<div class="upgrade-card border rounded-3 p-4 mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: relative; overflow: hidden;">
										<!-- Background Pattern -->
										<div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
										<div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.2;"></div>

										<div class="row align-items-center">
											<div class="col-lg-8 col-md-7">
												<div class="upgrade-content position-relative">
													<div class="d-flex align-items-center mb-3">
														<div class="verified-badge me-3" style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: 600;">
															<i class="fa-solid fa-shield-check me-2" style="color: #4CAF50;"></i>
															VERIFIED LISTING
														</div>
														<div class="price-tag" style="background: #FF6B35; padding: 6px 12px; border-radius: 15px; font-size: 16px; font-weight: bold;">
															₹500 Only
														</div>
													</div>

													<h4 class="mb-3" style="font-weight: 700; font-size: 24px;">Boost Your Job Posting Visibility!</h4>
													<p class="mb-3" style="font-size: 16px; line-height: 1.6; opacity: 0.95;">
														Get premium exposure and attract top talent with our verified job listing package.
													</p>

													<div class="benefits-list">
														<div class="row">
															<div class="col-md-6">
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Verified badge & priority listing</span>
																</div>
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Featured in "Show Verified Only"</span>
																</div>
															</div>
															<div class="col-md-6">
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">NRI Real Estate Club posting</span>
																</div>
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Social media promotion</span>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>

											<div class="col-lg-4 col-md-5 text-center">
												<div class="upgrade-action">
													<div class="contact-info mb-3">
														<div class="phone-number" style="background: rgba(255,255,255,0.2); padding: 12px 20px; border-radius: 25px; margin-bottom: 15px;">
															<i class="fa-solid fa-phone me-2" style="color: #4CAF50;"></i>
															<strong style="font-size: 18px;">9121537711</strong>
														</div>
														<p style="font-size: 14px; opacity: 0.9; margin-bottom: 15px;">Call now to upgrade your listing</p>
													</div>

													<button type="button" class="btn btn-light btn-lg px-4 py-2" style="font-weight: 600; border-radius: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);" onclick="window.open('tel:9121537711')">
														<i class="fa-solid fa-phone me-2"></i>
														Call Now
													</button>
												</div>
											</div>
										</div>

										<!-- Detailed Benefits Expandable Section -->
										<div class="mt-4">


											<div  id="detailedBenefitsJob">
												<div class="detailed-benefits" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px);">
													<div class="row">
														<div class="col-md-6">
															<h6 style="color: #FFD700; margin-bottom: 15px;">
																<i class="fa-solid fa-star me-2"></i>Premium Visibility
															</h6>
															<ul style="list-style: none; padding: 0;">
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Appears under "Show Verified Only" filter
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Verified badge increases applicant trust
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Higher ranking in search results
																</li>
															</ul>
														</div>
														<div class="col-md-6">
															<h6 style="color: #FFD700; margin-bottom: 15px;">
																<i class="fa-solid fa-share-nodes me-2"></i>Multi-Platform Promotion
															</h6>
															<ul style="list-style: none; padding: 0;">
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Posted on nrireic.com (NRI Real Estate Club)
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Shared on Instagram, Facebook, LinkedIn
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Featured on Twitter and YouTube channels
																</li>
															</ul>
														</div>
													</div>

													<div class="mt-3 text-center">
														<small style="opacity: 0.8; font-style: italic;">
															* Social media posting subject to content approval and platform availability
														</small>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="form-group col-lg-12 col-md-12">
						{{-- <label>GDPR Agreement *</label> --}}
						<ul class="no-ul-list">
							<li>
								<input id="aj_1" class="form-check-input" name="aj_1" type="checkbox">
								<label for="aj_1" class="form-check-label">I accept <a href="/tcpostjob" target="_blank">Tems & Conditions</a>  and  <a href="/privacypolicy" target="_blank">Privacy Policies</a>.</label>
							</li>
						</ul>
					</div>


					<!-- Services -->


					<div class="form-group col-lg-12 col-md-12">
						<button class="btn btn-primary fw-medium px-5" type="submit" >Submit</button>
					</div>

				</div>
            </form>
			</div>

		</div>
	</div>

</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="#" class="btn btn-call-to-act">SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->
@stop
@section('js')

<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.3/dist/jquery.validate.min.js"></script>
    <script>
        //  $('#property_type, #building_type, #facing, #builder_id').select2();
        $(document).ready(function() {
            $('#state_id').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
             // Initialize Select2 for district
             $('select[name="district_id"]').select2({
                placeholder: 'Select District',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('select[name="state_id"]').val();
                        if (!stateId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a state first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "districts",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting district without state
            $('select[name="district_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });
             // Initialize Select2 for area
             $('select[name="area_id"]').select2({
                placeholder: 'Select Area',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let districtId = $('select[name="district_id"]').val();
                        if (!districtId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a district first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "areas_with_pincode",
                            district_id: districtId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting district without state
            $('select[name="district_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });
            $('select[name="state_id"]').change(function() {
                biller = $(this).val();
                $("#district_id").empty().trigger('change');
            });
            // Add custom validation methods
            $.validator.addMethod("lettersonly", function(value, element) {
                return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
            }, "Please enter only letters and spaces");

            $.validator.addMethod("numbersonly", function(value, element) {
                return this.optional(element) || /^[0-9]+$/.test(value);
            }, "Please enter only numbers");

            $.validator.addMethod("phonenumber", function(value, element) {
                return this.optional(element) || /^[5-9]\d{9}$/.test(value);
            }, "Please enter a valid 10-digit mobile number starting with 5-9");

            $.validator.addMethod("notonlynumbers", function(value, element) {
                return this.optional(element) || !/^\d+$/.test(value);
            }, "Field cannot contain only numbers");
$.validator.addMethod("pincode", function(value, element) {
                return this.optional(element) || /^\d{6}$/.test(value);
            }, "Please enter a valid 6-digit pincode");

            $.validator.addMethod("alphanumeric", function(value, element) {
                return this.optional(element) || /^[a-zA-Z0-9\s]+$/.test(value);
            }, "Please enter only letters, numbers and spaces");

            // Initialize jQuery validation
            $("form").validate({
                ignore: [], // ✅ Crucial for Select2
                rules: {
                    job_title: {
                        required: true,
                        minlength: 5,
                        maxlength: 100,
                        notonlynumbers: true
                    },
                    job_role: {
                        required: true,
                        minlength: 2,
                        maxlength: 50,
                        notonlynumbers: true
                    },
                    experience: {
                        minlength: 2,
                        maxlength: 50
                    },
                    salary: {
                        minlength: 2,
                        maxlength: 50
                    },
                    no_of_positions: {
                        numbersonly: true,
                        min: 1,
                        max: 100
                    },
                    job_description: {
                        required: true,
                        minlength: 20,
                        maxlength: 1000
                    },
                    about_company: {
                        minlength: 20,
                        maxlength: 1000
                    },
                    company_name: {
                        required: true,
                        minlength: 2,
                        maxlength: 100,
                        notonlynumbers: true
                    },
                    qualification: {
                        minlength: 2,
                        maxlength: 100
                    },
                    email: {
                        required: true,
                        email: true,
                        maxlength: 100
                    },
                    mobile: {
                        required: true,
                        phonenumber: true,
                        minlength: 10,
                        maxlength: 10
                    },
                    state_id: {
                        required: true
                    },
                    district_id: {
                        required: true
                    },
                    area_id: {
                        required: true
                    },
                    address: {
                        required: true,
                        minlength: 10,
                        maxlength: 255
                    },
                    nearest_landmark: {
                        required: false,
                        maxlength: 255
                    },
                    distance: {
                        required: false,
                        maxlength: 100
                    },
                    aj_1: {
                        required: true
                    }
                },
                messages: {
                    job_title: {
                        required: "Please enter job title",
                        minlength: "Job title must be at least 5 characters long",
                        maxlength: "Job title cannot exceed 100 characters",
                        notonlynumbers: "Job title cannot contain only numbers"
                    },
                    job_role: {
                        required: "Please enter job role",
                        minlength: "Job role must be at least 2 characters long",
                        maxlength: "Job role cannot exceed 50 characters",
                        notonlynumbers: "Job role cannot contain only numbers"
                    },
                    experience: {
                        minlength: "Experience must be at least 2 characters long",
                        maxlength: "Experience cannot exceed 50 characters"
                    },
                    salary: {
                        minlength: "Salary must be at least 2 characters long",
                        maxlength: "Salary cannot exceed 50 characters"
                    },
                    no_of_positions: {
                        numbersonly: "Number of positions should contain only numbers",
                        min: "Number of positions must be at least 1",
                        max: "Number of positions cannot exceed 100"
                    },
                    job_description: {
                        required: "Please enter job description",
                        minlength: "Job description must be at least 20 characters long",
                        maxlength: "Job description cannot exceed 1000 characters"
                    },
                    about_company: {
                        minlength: "About company must be at least 20 characters long",
                        maxlength: "About company cannot exceed 1000 characters"
                    },
                    company_name: {
                        required: "Please enter company name",
                        minlength: "Company name must be at least 2 characters long",
                        maxlength: "Company name cannot exceed 100 characters",
                        notonlynumbers: "Company name cannot contain only numbers"
                    },
                    qualification: {
                        minlength: "Qualification must be at least 2 characters long",
                        maxlength: "Qualification cannot exceed 100 characters"
                    },
                    email: {
                        required: "Please enter email address",
                        email: "Please enter a valid email address",
                        maxlength: "Email cannot exceed 100 characters"
                    },
                    mobile: {
                        required: "Please enter mobile number",
                        phonenumber: "Please enter a valid 10-digit mobile number starting with 5-9",
                        minlength: "Mobile number must be exactly 10 digits",
                        maxlength: "Mobile number must be exactly 10 digits"
                    },
                    state_id: "Please select a state",
                    district_id: "Please select a district",
                    area_id: "Please select an area",
                    address: {
                        required: "Please enter address",
                        minlength: "Address must be at least 10 characters long",
                        maxlength: "Address cannot exceed 255 characters"
                    },
                    nearest_landmark: {
                        maxlength: "Nearest landmark cannot exceed 255 characters"
                    },
                    distance: {
                        maxlength: "Distance cannot exceed 100 characters"
                    },
                    aj_1: "Please accept terms and conditions"
                },
                errorPlacement: function(error, element) {
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });





            // Trigger validation on blur for all fields
            $(document).on("focusout", "input, select, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) { // Check if the form has been initialized with validate()
                    $(this).valid();
                }
            });
            $('#state_id').on('change', function () {
                $(this).valid();
            });

            // Real-time input restrictions
            // Phone number - only allow digits and limit to 10 characters
            $('#mobile').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10);
            });

            // Number of positions - only allow digits
            $('#no_of_positions').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });

            // Job title - prevent only numbers
            $('#job_title').on('input', function() {
                var value = this.value;
                if (/^\d+$/.test(value)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<span class="invalid-feedback">Job title cannot contain only numbers</span>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Job role - prevent only numbers
            $('#job_role').on('input', function() {
                var value = this.value;
                if (/^\d+$/.test(value)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<span class="invalid-feedback">Job role cannot contain only numbers</span>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

            // Company name - prevent only numbers
            $('#company_name').on('input', function() {
                var value = this.value;
                if (/^\d+$/.test(value)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<span class="invalid-feedback">Company name cannot contain only numbers</span>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

        // Show SweetAlert for success message
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Job Posted Successfully!',
                text: '{{ session('success') }}',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
                timer: 5000,
                timerProgressBar: true
            });
        @endif

        // Show SweetAlert for error message
        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Job Posting Failed!',
                text: '{{ session('error') }}',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#dc3545'
            });
        @endif

        });
  </script>

<style>
/* Enhanced Verified Listing Styles */
.verified-listing-upgrade .upgrade-card {
    transition: all 0.3s ease;
    border: 2px solid transparent !important;
}

.verified-listing-upgrade .upgrade-card:hover {
    border-color: rgba(255,255,255,0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.verified-listing-upgrade .verified-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.verified-listing-upgrade .price-tag {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.verified-listing-upgrade .benefit-item {
    transition: all 0.2s ease;
}

.verified-listing-upgrade .benefit-item:hover {
    transform: translateX(5px);
}
</style>

@stop
