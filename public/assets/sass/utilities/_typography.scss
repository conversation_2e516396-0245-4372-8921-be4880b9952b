h1,h2,h3,h4,h5,h6 {
  color: var(--text-primary-light);
  font-weight: 600;
}

h1, .h1 {
  font-size: var(--h1) !important;;
}

h2, .h2 {
  font-size: var(--h2) !important;;
}

h3, .h3 {
  font-size: var(--h3) !important;;
}

h4, .h4 {
  font-size: var(--h4) !important;;
}

h5, .h5 {
  font-size: var(--h5) !important;;
}

h6, .h6 {
  font-size: var(--h6) !important;;
}

// body font variation
@each $property, $value in $font-sizes {
  .text-#{$property} {
    font-size: var(--font-#{$property}) !important;
  }
}  


.line-height-1 {
  line-height: 1 !important;
}

// Text Turnicate
.text-line-1 {
  @include text-line-1;
}

.text-line-2 {
  @include text-line-2;
}

.text-line-3 {
  @include text-line-3;
}