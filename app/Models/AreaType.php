<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AreaType extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function properties()
    {
        return $this->hasMany(Property::class, 'area_type_id');
    }

    // Scope for active area types
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // Accessor for display name with abbreviation
    public function getDisplayNameAttribute()
    {
        return $this->name . ($this->abbreviation ? ' (' . $this->abbreviation . ')' : '');
    }

    // Accessor for name without spaces
    public function getNameAttribute($value)
    {
        return str_replace(' ', '', $value);
    }
}
