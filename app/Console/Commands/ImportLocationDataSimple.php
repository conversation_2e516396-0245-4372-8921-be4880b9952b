<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\State;
use App\Models\City;
use App\Models\Area;
use Illuminate\Support\Facades\DB;

class ImportLocationDataSimple extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:locations-simple {file : Path to the CSV file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import states, cities, and areas from CSV file (simple version)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info('Starting location data import...');

        try {
            // Read CSV file
            $handle = fopen($filePath, 'r');
            if (!$handle) {
                $this->error("Could not open file: {$filePath}");
                return 1;
            }

            // Read header row
            $headers = fgetcsv($handle);
            if (!$headers) {
                $this->error("Could not read CSV headers");
                return 1;
            }

            // Find column indices
            $stateIndex = array_search('state', array_map('strtolower', $headers));
            $districtIndex = array_search('district', array_map('strtolower', $headers));
            $areaIndex = array_search('area name', array_map('strtolower', $headers));
            $pincodeIndex = array_search('pincode', array_map('strtolower', $headers));

            if ($stateIndex === false || $districtIndex === false || $areaIndex === false) {
                $this->error("Required columns not found. Expected: state, district, area name, pincode");
                $this->error("Found headers: " . implode(', ', $headers));
                return 1;
            }

            $stateCache = [];
            $cityCache = [];
            $processedCount = 0;
            $skippedCount = 0;

            DB::beginTransaction();

            while (($row = fgetcsv($handle)) !== false) {
                try {
                    // Clean and validate data
                    $stateName = trim($row[$stateIndex] ?? '');
                    $cityName = trim($row[$districtIndex] ?? ''); // Using district as city
                    $areaName = trim($row[$areaIndex] ?? '');
                    $pincode = trim($row[$pincodeIndex] ?? '');

                    if (empty($stateName) || empty($cityName) || empty($areaName)) {
                        $this->warn("Skipping row with missing data: State: {$stateName}, City: {$cityName}, Area: {$areaName}");
                        $skippedCount++;
                        continue;
                    }

                    // Get or create state
                    if (!isset($stateCache[$stateName])) {
                        $state = State::firstOrCreate(
                            ['name' => $stateName],
                            ['status' => 1]
                        );
                        $stateCache[$stateName] = $state->id;
                        $this->info("State: {$stateName} (ID: {$state->id})");
                    }
                    $stateId = $stateCache[$stateName];

                    // Get or create district (using City model for districts)
                    $districtKey = $stateName . '|' . $cityName;
                    if (!isset($cityCache[$districtKey])) {
                        $district = City::firstOrCreate(
                            ['name' => $cityName, 'state_id' => $stateId],
                            ['status' => 1]
                        );
                        $cityCache[$districtKey] = $district->id;
                        $this->info("District: {$cityName} in {$stateName} (ID: {$district->id})");
                    }
                    $districtId = $cityCache[$districtKey];

                    // Create or update area
                    $areaData = [
                        'name' => $areaName,
                        'district_id' => $districtId,
                        'state_id' => $stateId,
                        'status' => 1
                    ];

                    // Add pincode if available
                    if (!empty($pincode) && is_numeric($pincode)) {
                        $areaData['pincode'] = (int)$pincode;
                    }

                    $area = Area::updateOrCreate(
                        [
                            'name' => $areaName,
                            'district_id' => $districtId,
                            'state_id' => $stateId
                        ],
                        $areaData
                    );

                    $processedCount++;

                    if ($processedCount % 100 == 0) {
                        $this->info("Processed {$processedCount} records...");
                    }

                } catch (\Exception $e) {
                    $this->error("Error processing record: " . $e->getMessage());
                    $this->error("Record data: " . implode(', ', $row));
                    $skippedCount++;
                }
            }

            fclose($handle);
            DB::commit();

            $this->info("\n=== Import Summary ===");
            $this->info("Total processed: {$processedCount}");
            $this->info("Total skipped: {$skippedCount}");
            $this->info("States created/updated: " . count($stateCache));
            $this->info("Cities created/updated: " . count($cityCache));
            $this->info("Import completed successfully!");

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
