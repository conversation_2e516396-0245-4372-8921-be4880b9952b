<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->unsignedBigInteger('area_type_id')->nullable()->after('area_size');
            $table->foreign('area_type_id')->references('id')->on('area_types')->onDelete('set null');
        });

        // Set default area_type_id to 1 (Square Feet) for existing records
        DB::statement('UPDATE properties SET area_type_id = 1 WHERE area_type_id IS NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->dropForeign(['area_type_id']);
            $table->dropColumn('area_type_id');
        });
    }
};
