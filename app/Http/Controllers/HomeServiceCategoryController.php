<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\HomeServiceCategory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;
use League\Csv\Writer;
use SplTempFileObject;

class HomeServiceCategoryController extends Controller
{

    //
    public function index()
    {
        return view('admin.homeservice_categories.list');
    }
    public function add()
    {
        return view('admin.homeservice_categories.add');
    }
    //
    // Store method to save homeservice category data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'image' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);
        $data = $request->all();
        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/homeservicecatgories/
            $filePath = $file->storeAs('homeservicecatgories', $filename, 'public');

            // Save the file path in the database
            $data['image'] = $filePath;
        }
        //creat slug from name
        $data['slug'] = Str::slug($data['name']);

        // Save data to database
        HomeServiceCategory::create($data);

        // Redirect to homeservicecategory list page with success message
        return redirect()->route('homeservicecategories.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $homeservicecategory = HomeServiceCategory::where('id', $id)->first();


        return view('admin.homeservice_categories.edit', compact('homeservicecategory'));
    }
    public function update(Request $request, HomeServiceCategory $homeservice_category)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'image'         => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048', // Optional image upload (Max 2MB)
        ]);

        $data = $request->except(['image']); // Exclude logo from mass update initially

        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/homeservicecategory/
            $filePath = $file->storeAs('homeservicecatgories', $filename, 'public');

            // Delete the old logo if exists
            if ($homeservice_category->image && Storage::disk('public')->exists($homeservice_category->image)) {
                Storage::disk('public')->delete($homeservice_category->image);
            }

            // Save the new file path
            $data['image'] = $filePath;
        }
        // Update homeservicecategory data
        $homeservice_category->update($data);

        // Redirect to homeservicecategory list page with success message
        return redirect()->route('homeservicecategories.index')->with('success', $homeservice_category->name . ' updated successfully!');
    }

    public function gethomeservicecategories(Request $request)
    {
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();
            // Fetch base homeservice categories query
            $homeserviceCategories = HomeServiceCategory::select(['id', 'name', 'image', 'status', 'created_at']);



            return DataTables::of($homeserviceCategories)
                ->addColumn('status', function ($row) {
                    $checked = $row->status == 1 ? 'checked' : '';
                    $statusText = $row->status == 1 ? 'Active' : 'Inactive';

                    return '
                        <div class="form-switch switch-success d-flex align-items-center gap-2">
                            <input class="form-check-input status-toggle" type="checkbox" role="switch"
                                   data-id="' . $row->id . '" ' . $checked . '>
                            <label class="form-check-label fw-medium text-secondary-light mb-0">' . $statusText . '</label>
                        </div>';
                })
                ->addColumn('image', function ($row) {
                    if ($row->image) {
                        $imageUrl = url('storage/'.$row->image); // update path if needed
                        return '<img src="' . $imageUrl . '" alt="' . $row->name . '" width="50" height="50" class="rounded">';
                    } else {
                        return '<img src="' . asset('assets/images/default.png') . '" width="50" height="50" class="rounded">';
                    }
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'homeservice category-edit' permission
                    if ($user->can('homeservice-category-edit')) {
                        $output .= '
                            <a href="' . route('homeservicecategories.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }


                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ;
                        });
                    }
                })

                ->rawColumns(['image', 'status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }
    public function export(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'searchkey' => 'nullable|string',
        ]);

        // Get all filter values
        $searchkey = $validated['searchkey'] ?? '';

        // Fetch base homeservice categories query
        $homeserviceCategories = HomeServiceCategory::select(['name', 'status', 'created_at']);

        // Apply filters
        if ($searchkey) {
            $homeserviceCategories->where('name', 'like', "%$searchkey%");
        }

        // Get all homeservice categories (no pagination)
        $homeserviceCategories = $homeserviceCategories->get();

        // Create CSV writer
        $csv = Writer::createFromFileObject(new SplTempFileObject());

        // Set CSV header
        $csv->insertOne([
            'Name',
            'Status',
            'Created At'
        ]);

        // Add data rows
        foreach ($homeserviceCategories as $homeserviceCategory) {
            $csv->insertOne([
                $homeserviceCategory->name,
                $homeserviceCategory->status ? 'Active' : 'Inactive',
                $homeserviceCategory->created_at->format('d-m-Y H:i:s')
            ]);
        }

        // Create filename with current date
        $filename = 'HomeServiceCategories_' . now()->format('Ymd') . '.csv'; // Adjust date format as needed

        // Return CSV as download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        return response()->streamDownload(function() use ($csv) {
            // Add BOM to ensure proper UTF-8 encoding
            echo "\xEF\xBB\xBF";
            echo $csv->getContent();
        }, $filename, $headers);
    }

    /**
     * Update the status of a home service category.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'category_id' => 'required|exists:home_service_categories,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the category
            $category = HomeServiceCategory::findOrFail($request->category_id);

            // Update status
            $category->status = $request->status;
            $category->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Home Service Category status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update home service category status: ' . $e->getMessage()
            ], 500);
        }
    }
}
