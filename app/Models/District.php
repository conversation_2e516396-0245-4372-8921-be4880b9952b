<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class District extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function areas()
    {
        return $this->hasMany(Area::class, 'district_id');
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'district_id');
    }

    public function homeServices()
    {
        return $this->hasMany(HomeService::class, 'district_id');
    }

    public function suppliers()
    {
        return $this->hasMany(Supplier::class, 'district_id');
    }

    public function jobs()
    {
        return $this->hasMany(Job::class, 'district_id');
    }

    public function builders()
    {
        return $this->hasMany(Builder::class, 'district_id');
    }
}
