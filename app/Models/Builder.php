<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Builder extends Model
{
    use HasFactory;
    protected $guarded  = [];
    public function propeties()
    {
        return $this->hasMany(Property::class, 'builder_id');
    }
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }
    public function district()
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    // Keep city method for backward compatibility during transition
    public function city()
    {
        return $this->district();
    }
    public function area()
    {
        return $this->belongsTo(Area::class, 'area_id');
    }
}
