@extends('admin.layouts.master')
@section('title', 'Supplier Categories')
@section('content')
<div class="dashboard-main-body">
@if(session('success'))
    <div id="snackbar" class="snackbar alert alert-success bg-success-100 text-success-600 border-success-100 fw-semibold text-lg radius-8 d-flex align-items-center justify-content-between show">
        <div class="d-flex align-items-center gap-2">
            <iconify-icon icon="akar-icons:double-check" class="icon text-xl"></iconify-icon>
            <span id="snackbar-message"> {{ session('success') }}</span>

        </div>
        <button class="remove-button text-success-600 text-xxl line-height-1" onclick="closeSnackbar()">
            <iconify-icon icon="iconamoon:sign-times-light" class="icon"></iconify-icon>
        </button>
    </div>
@endif
    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
        <h6 class="fw-semibold mb-0">Supplier Categories List</h6>
        <ul class="d-flex align-items-center gap-2">
            <li class="fw-medium">
                <a href="dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                    Dashboard
                </a>
            </li>
            <li>-</li>
            <li class="fw-medium">Supplier Categories List</li>
        </ul>
    </div>

    <div class="card">
        <div class="card-header d-flex flex-wrap align-items-center justify-content-between gap-3">
            <div class="d-flex flex-wrap align-items-center gap-3 w-25 w-mb-100">
                <div class="icon-field w-100">
                    <input type="text" name="searchkey" class="form-control form-control-sm w-100" placeholder="Search Category" id="searchkey">
                    <span class="icon">
                        <iconify-icon icon="ion:search-outline"></iconify-icon>
                    </span>
                </div>
            </div>
            <div class="d-flex flex-no-wrap align-items-center gap-3">

                @can('supplier-category-add')

                <a href="/admin/supplier-categories/add" class="btn btn-sm btn-primary-600 d-flex"><i class="ri-add-line"></i>&nbsp;<span class="d-none d-md-block">Create&nbsp;</span></a>
                @endcan
            </div>
        </div>
        <div class="card-body">
            <!-- Table View for Desktop -->
            <div class="d-none d-md-block">
                <table id="suppliercategoriesTable" class="table table-sm table-striped table-hover table-bordered text-wrap mt-4" style="width:100%">
                    <thead>
                        <tr>
                            <th>Supplier Category</th>
                            {{-- <th>Image</th> --}}
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- Grid View for Mobile -->
            <div id="clientsGrid" class="d-block d-md-none"></div>
        </div>
    </div>

</div>
<div id="exportLoader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <span class="spinner-border text-primary" role="status"></span>
    <strong>Exporting...</strong>
</div>

@stop
@section('script')
<script>
    $(document).ready(function () {

        var table = $('#suppliercategoriesTable').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            pageLength: 25,
            ajax: {
                url: "{{ route('suppliercategories.data') }}",
                data: function (d) {
                    d.searchkey = $('#searchkey').val();
                }
            },
            columns: [
                { data: 'name', name: 'name' },
                // { data: 'image', name: 'image' },
                { data: 'status', name: 'status', orderable: false, searchable: false },
                { data: 'action', name: 'action', orderable: false, searchable: false }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var data = api.rows().data();

            },
            dom:
            "<'row d-flex align-items-center justify-content-between'<'col-md-6'l><'col-md-6 text-end'B>>" + // Entries Dropdown & CSV Button
            "<'row'<'col-md-12'tr>>" +
            "<'row d-flex justify-content-between align-items-center'<'col-md-6'i><'col-md-6'p>>", // Info at left, Pagination at right
            // buttons: [
            //     {
            //         extend: 'csv',
            //         text: '<iconify-icon icon="hugeicons:csv-02"></iconify-icon> Export CSV',
            //         className: 'btn btn-success btn-sm bg-success-500',
            //         filename: 'Clients_' + new Date().toISOString().slice(0, 10)
            //     }
            // ],
            buttons: [
                    {
                    text: 'Export CSV',
                    className: 'btn btn-success btn-sm bg-success-500 d-inline-flex align-items-center gap-1',
                    init: function (api, node, config) {
                        $(node).append('<iconify-icon icon="hugeicons:csv-02"></iconify-icon>');
                    },
                    action: function (e, dt, node, config) {
                        // Show loader
                        $('#exportLoader').show();

                        // Get all filter values
                        const searchkey = $("#searchkey").val();

                        // Create form data
                        const formData = new FormData();
                        if (searchkey) formData.append('searchkey', searchkey);
                        formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

                        // Send AJAX request
                        $.ajax({
                            url: "{{ route('suppliercategories.export') }}",
                            method: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (response) {
                                // Hide loader
                                $('#exportLoader').hide();

                                // Download the file
                                const url = URL.createObjectURL(new Blob([response]));
                                const link = document.createElement('a');
                                link.href = url;
                                link.setAttribute('download', 'SupplierCategories_' + new Date().toISOString().slice(0, 10) + '.csv');
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            },
                            error: function (xhr) {
                                // Hide loader
                                $('#exportLoader').hide();

                                // Show error message
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Export Failed',
                                    text: xhr.responseJSON?.message || 'Something went wrong during export.',
                                });
                            }
                        });
                    }
                }
                ],

            infoCallback: function(settings, start, end, max, total, pre) {
                return `Showing ${start} to ${end} of ${total} records`;
            }
        });

        // Custom search event triggers
        $("#searchkey").on("keyup", function () {
            table.draw();
        });

        // Status toggle functionality
        $(document).on("change", ".status-toggle", function() {
            let isChecked = $(this).prop("checked"); // Get switch status
            let categoryId = $(this).data("id"); // Get Category ID
            let status = isChecked ? 1 : 0; // 1 = Active, 0 = Inactive
            let switchElement = $(this); // Store switch reference
            let labelElement = $(this).next("label"); // Get label next to switch

            let actionText = status === 1 ? "activate" : "deactivate";
            let warningText = status === 1 ?
                "Do you want to activate this supplier category?" :
                "Do you want to deactivate this supplier category?";

            // Revert the toggle until confirmed
            switchElement.prop("checked", !isChecked);

            Swal.fire({
                title: "Are you sure?",
                text: warningText,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#28a745",
                cancelButtonColor: "#d33",
                confirmButtonText: `Yes, ${actionText} it!`,
            }).then((result) => {
                if (result.isConfirmed) {
                    // Disable the switch during request
                    switchElement.prop("disabled", true);

                    $.ajax({
                        url: "{{ route('suppliercategories.update-status') }}", // Your route to update status
                        type: "POST",
                        data: {
                            category_id: categoryId,
                            status: status,
                            _token: $('meta[name="csrf-token"]').attr("content") // CSRF token
                        },
                        success: function(response) {
                            // Enable the switch again
                            switchElement.prop("disabled", false);

                            // Update the switch state
                            switchElement.prop("checked", isChecked);

                            // Update text dynamically
                            if (status === 1) {
                                labelElement.text("Active");
                            } else {
                                labelElement.text("Inactive");
                            }

                            // Show success message
                            Swal.fire({
                                icon: "success",
                                title: "Updated!",
                                text: "Supplier Category status has been changed.",
                                timer: 2000,
                                showConfirmButton: false
                            });
                        },
                        error: function(xhr) {
                            // Enable the switch again
                            switchElement.prop("disabled", false);

                            // Show error message
                            Swal.fire({
                                icon: "error",
                                title: "Error!",
                                text: "Failed to update status. Please try again.",
                                timer: 3000,
                                showConfirmButton: false
                            });
                        }
                    });
                }
            });
        });

    });
</script>
@stop
