@extends('admin.layouts.master')
@section('title', 'Clients View- Paidash')
@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .verification-toggle:checked {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }
        .verification-toggle:not(:checked) {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }
        .verification-label {
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .verification-label.text-success {
            color: #28a745 !important;
        }
        .verification-label.text-danger {
            color: #dc3545 !important;
        }
        .form-switch .form-check-input:disabled {
            opadistrict: 0.6;
            cursor: not-allowed;
        }
    </style>
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Job</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/admin/jobs" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Job
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Job</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="vendor-information"
                                    data-bs-toggle="pill" data-bs-target="#vendor-information-d" type="button" role="tab"
                                    aria-controls="vendor-information-d" aria-selected="false" tabindex="-1">
                                    Job Information
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="vendor-information-d" role="tabpanel"
                                aria-labelledby="vendor-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">


                                                <div>
                                                    <h5 class="mb-1 d-flex align-items-center flex-wrap">
                                                        <span class="me-2">{{ $job->job_title }}</span>
                                                        @if($job->verified == 1)
                                                            <span class="badge bg-success d-flex align-items-center" title="Verified Job" id="verificationBadge">
                                                                <iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning d-flex align-items-center" title="Not Verified" id="verificationBadge">
                                                                <iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </h5>
                                                </div>


                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        About Job
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Role:</strong> <span class="text-muted">{{ $job->job_role}}</span></li>
                                                            <li><strong>Experience :</strong> <span class="text-muted">{{ $job->experience }}</span></li>
                                                              <li><strong>Salary:</strong> <span class="text-muted">{{ $job->salary }}</span></li>
                                                             <li><strong>Qualification:</strong> <span class="text-muted">{{ $job->qualification }}</span></li>
                                                             <li><strong>No. Of Positions:</strong> <span class="text-muted">{{ $job->no_of_positions }}</span></li>
                                                            <li class="d-flex align-items-center justify-content-between">
                                                                <strong>Verification Status:</strong>
                                                                <div class="form-switch switch-success d-flex align-items-center">
                                                                    <input class="form-check-input verification-toggle" type="checkbox" role="switch"
                                                                           id="verificationSwitch{{ $job->id }}"
                                                                           data-id="{{ $job->id }}"
                                                                           {{ $job->verified == 1 ? 'checked' : '' }}>
                                                                    <label class="form-check-label ms-2 verification-label {{ $job->verified == 1 ? 'text-success' : 'text-danger' }}" for="verificationSwitch{{ $job->id }}">
                                                                        {{ $job->verified == 1 ? 'Verified' : 'Not Verified' }}
                                                                    </label>
                                                                </div>
                                                            </li>
                                                       </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Contact Information
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">

                                                            <li><strong>Email :</strong> <span class="text-muted">{{ $job->email }} Sqft</span></li>
                                                            <li><strong> Mobile:</strong> <span class="text-muted">{{ $job->mobile }}</span></li>
                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $job->area->name }}</span></li>
                                                           <li><strong>District:</strong> <span class="text-muted">{{ $job->district->name }}</span></li>
                                                            <li><strong>State:</strong> <span class="text-muted">{{ $job->state->name }}</span></li>
                                                            @if($job->address)
                                                            <li><strong>Address:</strong> <span class="text-muted">{{ $job->address }}</span></li>
                                                            @endif
                                                            @if($job->nearest_landmark)
                                                            <li><strong>Nearest Landmark:</strong> <span class="text-muted">{{ $job->nearest_landmark }}</span></li>
                                                            @endif
                                                            @if($job->distance)
                                                            <li><strong>Distance:</strong> <span class="text-muted">{{ $job->distance }}</span></li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Additional Details Card -->
                                            <div class="col-md-12">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:share-variant" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                         Job Details
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">

                                                            <li><strong> Job Description:</strong>
                                                                 <p>{!! $job->job_description !!}</p>
                                                                </li>
                                                            <li><strong>Company:</strong> {{ $job->company_name }} </li>
                                                            <li><strong>About Company:</strong>
                                                                <p>{{ $job->about_company }}</p>
                                                                </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>


                                    </div>
                                </div>
                            </div>





                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>



@stop

@section('script')
<script>
$(document).ready(function() {
    // Handle verification status toggle
    $(document).on("change", ".verification-toggle", function() {
        let isChecked = $(this).prop("checked"); // Get switch status
        let jobId = $(this).data("id"); // Get Job ID
        let verified = isChecked ? 1 : 0; // 1 = Verified, 0 = Not Verified
        let switchElement = $(this); // Store switch reference
        let labelElement = $(this).next("label"); // Get label next to switch

        let actionText = verified === 1 ? "verify" : "unverify";
        let warningText = verified === 1 ?
            "Do you want to verify this job?" :
            "Do you want to remove verification from this job?";

        // Revert the toggle until confirmed
        switchElement.prop("checked", !isChecked);

        Swal.fire({
            title: "Are you sure?",
            text: warningText,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes, ${actionText} it!`,
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                // Disable the switch during the request
                switchElement.prop("disabled", true);

                $.ajax({
                    url: "{{ route('jobs.update-verification') }}",
                    type: "POST",
                    data: {
                        job_id: jobId,
                        verified: verified,
                        _token: $('meta[name="csrf-token"]').attr("content")
                    },
                    success: function(response) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Update the switch state
                        switchElement.prop("checked", isChecked);

                        // Update text dynamically
                        if (verified === 1) {
                            labelElement.text("Verified");
                            labelElement.removeClass("text-danger").addClass("text-success");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-warning').addClass('bg-success')
                                .html('<iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>Verified')
                                .attr('title', 'Verified Job');
                        } else {
                            labelElement.text("Not Verified");
                            labelElement.removeClass("text-success").addClass("text-danger");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-success').addClass('bg-warning')
                                .html('<iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>Not Verified')
                                .attr('title', 'Not Verified');
                        }

                        // Show success message
                        Swal.fire({
                            icon: "success",
                            title: "Updated!",
                            text: "Job verification status has been changed.",
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr, status, error) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Show error message
                        let errorMessage = "Failed to update verification status. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: errorMessage,
                            confirmButtonText: "OK"
                        });
                    }
                });
            }
            // If cancelled, the switch remains in its original state (already reverted above)
        });
    });
});
</script>
@endsection
