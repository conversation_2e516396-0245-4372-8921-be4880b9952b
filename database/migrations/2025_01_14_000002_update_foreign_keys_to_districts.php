<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For now, just add district_id columns and copy data
        // We'll keep city_id for backward compatibility during transition

        // Update areas table: add district_id and copy data from city_id
        if (!Schema::hasColumn('areas', 'district_id')) {
            Schema::table('areas', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('areas', 'city_id')) {
            DB::statement('UPDATE areas SET district_id = city_id WHERE district_id IS NULL');
        }

        // Update properties table: add district_id and copy data from city_id
        if (!Schema::hasColumn('properties', 'district_id')) {
            Schema::table('properties', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('properties', 'city_id')) {
            DB::statement('UPDATE properties SET district_id = city_id WHERE district_id IS NULL');
        }

        // Update home_services table: add district_id and copy data from city_id
        if (!Schema::hasColumn('home_services', 'district_id')) {
            Schema::table('home_services', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('home_services', 'city_id')) {
            DB::statement('UPDATE home_services SET district_id = city_id WHERE district_id IS NULL');
        }

        // Update suppliers table: add district_id and copy data from city_id
        if (!Schema::hasColumn('suppliers', 'district_id')) {
            Schema::table('suppliers', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('suppliers', 'city_id')) {
            DB::statement('UPDATE suppliers SET district_id = city_id WHERE district_id IS NULL');
        }

        // Update jobs table: add district_id and copy data from city_id
        if (!Schema::hasColumn('jobs', 'district_id')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('jobs', 'city_id')) {
            DB::statement('UPDATE jobs SET district_id = city_id WHERE district_id IS NULL');
        }

        // Update builders table: add district_id and copy data from city_id
        if (!Schema::hasColumn('builders', 'district_id')) {
            Schema::table('builders', function (Blueprint $table) {
                $table->unsignedBigInteger('district_id')->nullable();
            });
        }
        if (Schema::hasColumn('builders', 'city_id')) {
            DB::statement('UPDATE builders SET district_id = city_id WHERE district_id IS NULL');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove district_id columns (keep city_id for backward compatibility)
        if (Schema::hasColumn('areas', 'district_id')) {
            Schema::table('areas', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }

        if (Schema::hasColumn('properties', 'district_id')) {
            Schema::table('properties', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }

        if (Schema::hasColumn('home_services', 'district_id')) {
            Schema::table('home_services', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }

        if (Schema::hasColumn('suppliers', 'district_id')) {
            Schema::table('suppliers', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }

        if (Schema::hasColumn('jobs', 'district_id')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }

        if (Schema::hasColumn('builders', 'district_id')) {
            Schema::table('builders', function (Blueprint $table) {
                $table->dropColumn('district_id');
            });
        }
    }
};
