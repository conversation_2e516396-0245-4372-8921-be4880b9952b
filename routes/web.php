<?php

use App\Http\Controllers\AdController;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AreaController;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\BuilderController;
use App\Http\Controllers\CityController;
use App\Http\Controllers\DistrictController;
use App\Http\Controllers\Frontend\FrontendController;
use App\Http\Controllers\HomeServiceController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\JobController;
use App\Http\Controllers\HomeServiceCategoryController;
use App\Http\Controllers\SupplierCategoryController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\StateController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



Route::get('/', [FrontendController::class, 'index'])->name('hone');
Route::get('/aboutus', [FrontendController::class, 'aboutus'])->name('aboutus');
Route::get('/contact', [FrontendController::class, 'contact'])->name('contact');
Route::get('/advertise', [FrontendController::class, 'advertise'])->name('advertise');
Route::post('/enquiry_form',[FrontendController::class, 'general_enquiry_form'])->name('enquiry_form');
Route::get('/maintermsandconditions', [FrontendController::class, 'maintermsandconditions'])->name('maintermsandconditions');
Route::get('/privacypolicy', [FrontendController::class, 'privacypolicy'])->name('privacypolicy');
Route::get('/tchomeservice', [FrontendController::class, 'tchomeservice'])->name('tchomeservice');
Route::get('/tcsupplierreg', [FrontendController::class, 'tcsupplierreg'])->name('tcsupplierreg');
Route::get('/tcpostprop', [FrontendController::class, 'tcpostprop'])->name('tcpostprop');
Route::get('/tcpostjob', [FrontendController::class, 'tcpostjob'])->name('tcpostjob');
Route::get('/homeservices/{category}', [FrontendController::class, 'homeservices'])->name('homeservices');
Route::get('/suppliers/{category}', [FrontendController::class, 'suppliers'])->name('suppliers');
Route::get('/register', [FrontendController::class, 'register'])->name('register');
Route::get('/login', [FrontendController::class, 'login'])->name('login');
Route::get('/homeservice_registration', [FrontendController::class, 'homeservice_registration'])->name('homeservice_registration');
Route::post('/homeservice_registration', [FrontendController::class, 'storeHomeService'])->name('homeservice_registration.store');
Route::get('/supplier_registration', [FrontendController::class, 'supplier_registration'])->name('supplier_registration');
Route::post('/supplier_registration', [FrontendController::class, 'storeSupplier'])->name('supplier_registration.store');
Route::get('/post_property', [FrontendController::class, 'post_property'])->name('post_property');
Route::post('/post_property', [FrontendController::class, 'storeProperty'])->name('post_property.store');

Route::get('/post_job', [FrontendController::class, 'post_job'])->name('post_job');
Route::post('/post_job', [FrontendController::class, 'storeJob'])->name('post_job.store');
Route::get('/jobs', [FrontendController::class, 'jobs'])->name('jobs');
Route::get('/job-details/{id}', [FrontendController::class, 'job_details'])->name('job_details');

Route::get('/find_homeservices', [FrontendController::class, 'find_homeservices'])->name('find_homeservices');
Route::get('/find_suppliers', [FrontendController::class, 'find_suppliers'])->name('find_suppliers');
Route::get('/homeservice-details/{slug}', [FrontendController::class, 'homeservice_details'])->name('homeservice_details');
Route::get('/supplier-details/{slug}', [FrontendController::class, 'supplier_details'])->name('supplier_details');
Route::get('/builders', [FrontendController::class, 'builders'])->name('builders');
Route::get('/properties', [FrontendController::class, 'properties'])->name('properties');
Route::get('/property-details/{slug}', [FrontendController::class, 'property_details'])->name('property_details');


     Route::post('/users/store', [FrontendController::class, 'usersstore'])->name('users.store');

Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

Route::prefix('admin')->group(function () {

    // Route::get('/', function () {
    //     return view('admin.login');
    // });
    Route::get('/', [AuthController::class, 'showLoginForm'])->name('login');

    Route::get('/dashboard', function () {
        return view('admin.dashboard');

    });
    //Auth routes
    Route::middleware(['auth'])->group(function () {
        Route::get('/dashboard',  [Controller::class, 'dashboard'])->name('dashboard');
    });
    //homeservice categories
    Route::middleware('permission:homeservice-category-view')->group(function () {
        Route::get('/homeservice-categories',  [HomeServiceCategoryController::class, 'index'])->name('homeservicecategories.index');
        Route::get('/homeservice-categories-data', [HomeServiceCategoryController::class, 'gethomeservicecategories'])->name('homeservicecategories.data');
    });

    Route::middleware('permission:homeservice-category-edit')->group(function () {
        Route::get('/homeservice-categories/edit/{id}', [HomeServiceCategoryController::class, 'edit'])->name('homeservicecategories.edit');
        Route::put('/homeservice-categories/update/{homeservice_category}', [HomeServiceCategoryController::class, 'update'])->name('homeservicecategories.update');
    });

    Route::middleware('permission:homeservice-category-add')->group(function () {
        Route::get('/homeservice-categories/add', [HomeServiceCategoryController::class, 'add'])->name('homeservicecategories.add');
        Route::post('/homeservice-categories/store', [HomeServiceCategoryController::class, 'store'])->name('homeservicecategories.store');
    });
//supplier categories
    Route::middleware('permission:supplier-category-view')->group(function () {
        Route::get('/supplier-categories',  [SupplierCategoryController::class, 'index'])->name('suppliercategories.index');
        Route::get('/supplier-categories-data', [SupplierCategoryController::class, 'getsuppliercategories'])->name('suppliercategories.data');
    });

    Route::middleware('permission:supplier-category-edit')->group(function () {
        Route::get('/supplier-categories/edit/{id}', [SupplierCategoryController::class, 'edit'])->name('suppliercategories.edit');
        Route::put('/supplier-categories/update/{supplier_category}', [SupplierCategoryController::class, 'update'])->name('suppliercategories.update');
    });

    Route::middleware('permission:supplier-category-add')->group(function () {
        Route::get('/supplier-categories/add', [SupplierCategoryController::class, 'add'])->name('suppliercategories.add');
        Route::post('/supplier-categories/store', [SupplierCategoryController::class, 'store'])->name('suppliercategories.store');
    });
    //States
    Route::middleware('permission:state-view')->group(function () {
        Route::get('/states',  [StateController::class, 'index'])->name('states.index');
        Route::get('/states-data', [StateController::class, 'getstates'])->name('states.data');
    });
    Route::middleware('permission:state-edit')->group(function () {
        Route::get('/states/edit/{id}', [StateController::class, 'edit'])->name('states.edit');
        Route::put('/states/update/{state}', [StateController::class, 'update'])->name('states.update');
    });

    Route::middleware('permission:state-add')->group(function () {
        Route::get('/states/add', [StateController::class, 'add'])->name('states.add');
        Route::post('/states/store', [StateController::class, 'store'])->name('states.store');
    });

    //districts (formerly cities)
    Route::middleware('permission:district-view')->group(function () {
        Route::get('/districts',  [DistrictController::class, 'index'])->name('districts.index');
        Route::get('/districts-data', [DistrictController::class, 'getdistricts'])->name('districts.data');
    });
    Route::middleware('permission:district-edit')->group(function () {
        Route::get('/districts/edit/{id}', [DistrictController::class, 'edit'])->name('districts.edit');
        Route::put('/districts/update/{district}', [DistrictController::class, 'update'])->name('districts.update');
    });

    Route::middleware('permission:district-add')->group(function () {
        Route::get('/districts/add', [DistrictController::class, 'add'])->name('districts.add');
        Route::post('/districts/store', [DistrictController::class, 'store'])->name('districts.store');
    });

    // // Keep old city routes for backward compatibility during transition
    // Route::middleware('permission:city-view')->group(function () {
    //     Route::get('/cities',  [DistrictController::class, 'index'])->name('cities.index');
    //     Route::get('/cities-data', [DistrictController::class, 'getdistricts'])->name('cities.data');
    // });
    // Route::middleware('permission:city-edit')->group(function () {
    //     Route::get('/cities/edit/{id}', [DistrictController::class, 'edit'])->name('cities.edit');
    //     Route::put('/cities/update/{city}', [DistrictController::class, 'update'])->name('cities.update');
    // });

    // Route::middleware('permission:city-add')->group(function () {
    //     Route::get('/cities/add', [DistrictController::class, 'add'])->name('cities.add');
    //     Route::post('/cities/store', [DistrictController::class, 'store'])->name('cities.store');
    // });

    //Areas
    Route::middleware('permission:area-view')->group(function () {
        Route::get('/areas',  [AreaController::class, 'index'])->name('areas.index');
        Route::get('/areas-data', [AreaController::class, 'getareas'])->name('areas.data');
    });
    Route::middleware('permission:area-edit')->group(function () {
        Route::get('/areas/edit/{id}', [AreaController::class, 'edit'])->name('areas.edit');
        Route::put('/areas/update/{area}', [AreaController::class, 'update'])->name('areas.update');
    });

    Route::middleware('permission:area-add')->group(function () {
        Route::get('/areas/add', [AreaController::class, 'add'])->name('areas.add');
        Route::post('/areas/store', [AreaController::class, 'store'])->name('areas.store');
    });

    //venors
    Route::middleware('permission:homeservice-view')->group(function () {
        Route::get('/homeservices',  [HomeServiceController::class, 'index'])->name('homeservices.index');
        Route::get('/homeservices/data', [App\Http\Controllers\HomeServiceController::class, 'getHomeServicesData'])->name('homeservices.data');
        Route::get('/homeservices/view/{id}', [HomeServiceController::class, 'show'])->name('homeservices.show');
    });

    //Suppliers
    Route::middleware('permission:supplier-view')->group(function () {
        Route::get('/suppliers',  [SupplierController::class, 'index'])->name('suppliers.index');
        Route::get('/suppliers/data', [App\Http\Controllers\SupplierController::class, 'getSuppliersData'])->name('suppliers.data');
        Route::get('/suppliers/view/{id}', [SupplierController::class, 'show'])->name('suppliers.show');
    });
    //Property
    Route::middleware('permission:property-view')->group(function () {
        Route::get('/properties',  [PropertyController::class, 'index'])->name('properties.index');
        Route::get('/properties-data', [PropertyController::class, 'getPropertiesData'])->name('properties.data');
        Route::get('/properties/view/{id}', [PropertyController::class, 'show'])->name('properties.show');
    });
    //Jobs
    Route::middleware('permission:job-view')->group(function () {
        Route::get('/jobs',  [JobController::class, 'index'])->name('jobs.index');
        Route::get('/jobs-data', [JobController::class, 'getjobs'])->name('jobs.data');
        Route::get('/jobs/view/{id}', [JobController::class, 'show'])->name('jobs.show');
    });
     //builders
     Route::middleware('permission:builder-view')->group(function () {
        Route::get('/builders',  [BuilderController::class, 'index'])->name('builders.index');
        Route::get('/builders-data', [BuilderController::class, 'getBuildersData'])->name('builders.data');
        Route::get('/builders/view/{id}', [BuilderController::class, 'show'])->name('builders.show');
    });

    Route::middleware('permission:builder-edit')->group(function () {
        Route::get('/builders/edit/{id}', [BuilderController::class, 'edit'])->name('builders.edit');
        Route::put('/builders/update/{builder}', [BuilderController::class, 'update'])->name('builders.update');
    });

    Route::middleware('permission:builder-add')->group(function () {
        Route::get('/builders/add', [BuilderController::class, 'add'])->name('builders.add');
        Route::post('/builders/store', [BuilderController::class, 'store'])->name('builders.store');
    });


     //ads
     Route::middleware('permission:ad-view')->group(function () {
        Route::get('/ads',  [AdController::class, 'index'])->name('ads.index');
        Route::get('/ads-data', [AdController::class, 'getads'])->name('ads.data');
    });

    Route::middleware('permission:ad-edit')->group(function () {
        Route::get('/ads/edit/{id}', [AdController::class, 'edit'])->name('ads.edit');
        Route::put('/ads/update/{ad}', [AdController::class, 'update'])->name('ads.update');
    });

    Route::middleware('permission:ad-add')->group(function () {
        Route::get('/ads/add', [AdController::class, 'add'])->name('ads.add');
        Route::post('/ads/store', [AdController::class, 'store'])->name('ads.store');
    });

    //banners
    Route::middleware('permission:banner-view')->group(function () {
        Route::get('/banners',  [BannerController::class, 'index'])->name('banners.index');
        Route::get('/banners-data', [BannerController::class, 'getbanners'])->name('banners.data');
    });

    Route::middleware('permission:banner-edit')->group(function () {
        Route::get('/banners/edit/{id}', [BannerController::class, 'edit'])->name('banners.edit');
        Route::put('/banners/update/{banner}', [BannerController::class, 'update'])->name('banners.update');
    });

    Route::middleware('permission:banner-add')->group(function () {
        Route::get('/banners/add', [BannerController::class, 'add'])->name('banners.add');
        Route::post('/banners/store', [BannerController::class, 'store'])->name('banners.store');
    });

    // Property status update route
    Route::post('/properties/update-status', [App\Http\Controllers\PropertyController::class, 'updateStatus'])->name('properties.update-status');

    // Property verification status update route
    Route::post('/properties/update-verification', [App\Http\Controllers\PropertyController::class, 'updateVerificationStatus'])->name('properties.update-verification');

      // Job status update route
    Route::post('/jobs/update-status', [App\Http\Controllers\JobController::class, 'updateStatus'])->name('jobs.update-status');

    // Job verification status update route
    Route::post('/jobs/update-verification', [App\Http\Controllers\JobController::class, 'updateVerificationStatus'])->name('jobs.update-verification');


    // HomeService status update route
    Route::post('/homeservices/update-status', [App\Http\Controllers\HomeServiceController::class, 'updateStatus'])->name('homeservices.update-status');

    // HomeService verification status update route
    Route::post('/homeservices/update-verification', [App\Http\Controllers\HomeServiceController::class, 'updateVerificationStatus'])->name('homeservices.update-verification');

    // supplier status update route
    Route::post('/suppliers/update-status', [App\Http\Controllers\SupplierController::class, 'updateStatus'])->name('suppliers.update-status');

    // Supplier verification status update route
    Route::post('/suppliers/update-verification', [App\Http\Controllers\SupplierController::class, 'updateVerificationStatus'])->name('suppliers.update-verification');

    // HomeService Category status update route
    Route::post('/homeservice-categories/update-status', [App\Http\Controllers\HomeServiceCategoryController::class, 'updateStatus'])->name('homeservicecategories.update-status');

    // Supplier Category status update route
    Route::post('/supplier-categories/update-status', [App\Http\Controllers\SupplierCategoryController::class, 'updateStatus'])->name('suppliercategories.update-status');

    // Builder status update route
    Route::post('/builders/update-status', [App\Http\Controllers\BuilderController::class, 'updateStatus'])->name('builders.update-status');

    // Banner status update route
    Route::post('/banners/update-status', [App\Http\Controllers\BannerController::class, 'updateStatus'])->name('banners.update-status');

    // Banner delete route
    Route::post('/banners/delete', [App\Http\Controllers\BannerController::class, 'destroy'])->name('banners.delete');

    // Property delete route
    Route::post('/properties/delete', [App\Http\Controllers\PropertyController::class, 'destroy'])->name('properties.delete');

    // Job delete route
    Route::post('/jobs/delete', [App\Http\Controllers\JobController::class, 'destroy'])->name('jobs.delete');

    // HomeService delete route
    Route::post('/homeservices/delete', [App\Http\Controllers\HomeServiceController::class, 'destroy'])->name('homeservices.delete');
// supplier delete route
    Route::post('/suppliers/delete', [App\Http\Controllers\SupplierController::class, 'destroy'])->name('suppliers.delete');

    // Export routes
    Route::post('/homeservices/export', [App\Http\Controllers\HomeServiceController::class, 'export'])->name('homeservices.export');
    Route::post('/suppliers/export', [App\Http\Controllers\SupplierController::class, 'export'])->name('suppliers.export');
    Route::post('/jobs/export', [App\Http\Controllers\JobController::class, 'export'])->name('jobs.export');
    Route::post('/homeservicecategories/export', [HomeServiceCategoryController::class, 'export'])->name('homeservicecategories.export');
    Route::post('/suppliercategories/export', [SupplierCategoryController::class, 'supplierexport'])->name('suppliercategories.export');

    // Property export route
    Route::post('/properties/export', [App\Http\Controllers\PropertyController::class, 'export'])->name('properties.export');

    // Builder export route
    Route::post('/builders/export', [App\Http\Controllers\BuilderController::class, 'export'])->name('builders.export');

    // Route::middleware('permission:role-list')->group(function () {
        Route::get('/roles', [AdminController::class, 'index'])->name('roles');
    // });

    // Route::middleware('permission:role-permission')->group(function () {
        Route::get('/roles/permissions/{id}', [AdminController::class, 'rolePermissions'])->name('roles.permissions');
        Route::post('/roles/assign-permissions', [AdminController::class, 'assignPermissions'])->name('roles.assign-permissions');
    // });


});


