<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('area_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('abbreviation', 10)->nullable(); // e.g., 'sq ft', 'sq m', 'acres'
            $table->tinyInteger('status')->default(1)->comment('0 = Inactive, 1 = Active');
            $table->timestamps();
        });

        // Insert default area types
        DB::table('area_types')->insert([
            ['name' => 'Square Feet', 'abbreviation' => 'sq ft', 'status' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Square Meters', 'abbreviation' => 'sq m', 'status' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Acres', 'abbreviation' => 'acres', 'status' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Square Yards', 'abbreviation' => 'sq yd', 'status' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Cents', 'abbreviation' => 'cents', 'status' => 1, 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('area_types');
    }
};
