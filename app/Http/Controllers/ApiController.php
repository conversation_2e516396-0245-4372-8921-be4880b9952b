<?php

namespace App\Http\Controllers;

use App\Models\Area;
use App\Models\City;
use App\Models\District;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class ApiController extends Controller
{
    //
    public function fetch_data(Request $request){
        if($request->get_type==4){ // all select 2 data genration
            $search_keyword = $request->q;
            if($request->datafrom=="states"){
              $data = DB::table('states')->where('status',1)->where('name', 'LIKE', "%".$search_keyword."%")->orderByRaw('LOWER(name) ASC')->get();
              $json = [];
              foreach($data as $row){
                 $json[] = ['id'=>$row->id, 'text'=>strtoupper($row->name),'data'=>$row];
              }
            }
            if($request->datafrom=="cities"){
                $data = City::where('status',1)->where('name', 'LIKE', "%".$search_keyword."%");
                if($request->state_id){
                    $data = $data->where('state_id',$request->state_id);
                }
                $data = $data->orderByRaw('LOWER(name) ASC')->get();

              $json = [];
              foreach($data as $row){
                 $json[] = ['id'=>$row->id, 'text'=>ucwords($row->name .', '.$row->state->name),'data'=>$row];
              }
            }
            if($request->datafrom=="districts"){
                $data = District::where('status',1)->where('name', 'LIKE', "%".$search_keyword."%");
                if($request->state_id){
                    $data = $data->where('state_id',$request->state_id);
                }
                $data = $data->orderByRaw('LOWER(name) ASC')->get();

              $json = [];
              foreach($data as $row){
                 $json[] = ['id'=>$row->id, 'text'=>ucwords($row->name .', '.$row->state->name),'data'=>$row];
              }
            }
            if($request->datafrom=="areas"){
              $data =Area::where('status',1)->where('name', 'LIKE', "%".$search_keyword."%");
              if($request->city_id){
                $data=$data->where('city_id',$request->city_id);
              }
              if($request->district_id){
                $data=$data->where('district_id',$request->district_id);
              }
              $data=$data->orderByRaw('LOWER(name) ASC')->get();
              $json = [];
              foreach($data as $row){
                 $json[] = ['id'=>$row->id, 'text'=>ucwords($row->name.', '.$row->district->name.', '.$row->state->name),'data'=>$row];
              }
            }
            if($request->datafrom=="areas_with_pincode"){
              $data =Area::where('status',1)->where(function($query) use ($search_keyword) {
                  $query->where('name', 'LIKE', "%".$search_keyword."%")
                        ->orWhere('pincode', 'LIKE', "%".$search_keyword."%");
              });
              if($request->district_id){
                $data=$data->where('district_id',$request->district_id);
              }
              $data=$data->orderByRaw('LOWER(name) ASC')->get();
              $json = [];
              foreach($data as $row){
                 $text = $row->name;
                 if($row->pincode) {
                     $text .= ' - ' . $row->pincode;
                 }
                 $text .= ', ' . $row->district->name . ', ' . $row->state->name;
                 $json[] = ['id'=>$row->id, 'text'=>ucwords($text),'data'=>$row];
              }
            }
            if($request->datafrom=="building_types"){

                $data = DB::table('building_types')->where('status',1)->orderByRaw("CASE WHEN LOWER(type) = 'other' THEN 1 ELSE 0 END, LOWER(type) ASC")->get();
                $json = [];
                foreach($data as $row){
                   $json[] = ['id'=>$row->id, 'text'=>strtoupper($row->type),'data'=>$row];
                }
            }


            return Response::json($json);
        }
    }
}
