@extends('admin.layouts.master')
@section('title', 'Vendor Category Creation')

@section('content')
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">Add New City</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="{{ route('cities.index') }}" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        City
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">Add City</li>
            </ul>
        </div>

        <div class="card">
            <div class="card-body">
                <h6 class="mb-4 text-xl">Fill the required Details to add new city</h6>
                <!-- <p class="text-neutral-500">Fill up your details and proceed next steps.</p> -->
                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
                <!-- Form Wizard Start -->
                <div class="form-wizard">
                    <form action="{{ route('cities.store') }}" method="POST" id="form" enctype="multipart/form-data">
                        @csrf
                        <fieldset class="wizard-fieldset show">
                            <div class="row gy-3">

                                <div class="col-sm-6">
                                    <label class="form-label">State<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="state_id" name="state_id">
                                        <option value="">Select State</option>
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">City <span class="text-danger-600">*</span></label>
                                    <input type="text" class="form-control wizard-required1" name="name"
                                        placeholder="Enter City" value="{{ old('name') }}">
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Status<span class="text-danger-600">*</span></label>
                                    <select class="form-control wizard-required select2" id="status" name="status">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>

                                <div class="form-group d-flex align-items-center justify-content-end gap-8">
                                    <a href="{{ route('cities.index') }}" class="btn btn-neutral-500 border-neutral-100 px-32">Cancel</a>
                                    <button type="submit" class="form-wizard-next-btn btn btn-primary-600 px-32">Save</button>
                                </div>
                            </div>
                        </fieldset>
                    </form>

                </div>
                <!-- Form Wizard End -->
            </div>
        </div>

    </div>
@stop

@section('script')
    <script>
        $(document).ready(function() {
            $('#state_id').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
            // Initialize jQuery validation

            $("form").validate({
                ignore: [], // ✅ Crucial for Select2
                rules: {
                    name: {
                        required: true
                    },
                    state_id: {
                        required: true
                    }
                },
                messages: {
                    name: "City name is required",
                    state_id: "State is required"
                },
                errorPlacement: function(error, element) {
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });



            // Trigger validation on blur for all fields
            $(document).on("focusout", "input, select, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) { // Check if the form has been initialized with validate()
                    $(this).valid();
                }
            });
            $('#state_id').on('change', function () {
                $(this).valid();
            });

        });
  </script>
@stop
@section('css')
    <style>
        .error-border {
            border: 1px solid #dc3545 !important;
            /* Bootstrap red */
            border-radius: 5px;
        }
    </style>
@stop
