<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Area extends Model
{
    use HasFactory;
    protected $guarded  = [];

    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    public function district()
    {
        return $this->belongsTo(District::class, 'district_id');
    }

    // Keep city method for backward compatibility during transition
    public function city()
    {
        return $this->district();
    }

    public function properties()
    {
        return $this->hasMany(Property::class, 'area_id');
    }

    public function homeServices()
    {
        return $this->hasMany(HomeService::class, 'area_id');
    }

    public function suppliers()
    {
        return $this->hasMany(Supplier::class, 'area_id');
    }

    public function jobs()
    {
        return $this->hasMany(Job::class, 'area_id');
    }

    public function builders()
    {
        return $this->hasMany(Builder::class, 'area_id');
    }

    // Accessor for area with pincode display
    public function getAreaWithPincodeAttribute()
    {
        return $this->name . ($this->pincode ? ' - ' . $this->pincode : '');
    }
}
